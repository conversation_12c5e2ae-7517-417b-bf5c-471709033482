{"scan_time": "2025-07-05T16:22:29.164091", "workspace_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces", "current_project": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder", "n8n_folders": {"N8N_Builder": {"exists": true, "modified": "2025-07-05T16:14:38.558841", "total_files": 6242, "total_directories": 975, "total_size_mb": 1535.44, "key_files": {"README.md": true, "requirements.txt": true, "run.py": true, "setup.py": true, ".git": true, "n8n-docker": true, "Self_Healer": true, "Scripts": true}, "recent_files": 6242, "has_git": true, "appears_active": true, "relative_path": "N8N_Builder", "is_current_project": true}, "N8N_Builder_BACKUP_20250703_174421": {"exists": true, "modified": "2025-07-03T17:44:24.224555", "total_files": 9039, "total_directories": 1066, "total_size_mb": 152.27, "key_files": {"README.md": true, "requirements.txt": true, "run.py": true, "setup.py": true, ".git": true, "n8n-docker": true, "Self_Healer": true, "Scripts": true}, "recent_files": 9039, "has_git": true, "appears_active": true, "relative_path": "N8N_Builder_BACKUP_20250703_174421", "is_current_project": false}, "N8N_Builder_Community": {"exists": true, "modified": "2025-07-05T15:42:57.038433", "total_files": 519, "total_directories": 285, "total_size_mb": 7.26, "key_files": {"README.md": true, "requirements.txt": true, "run.py": true, "setup.py": true, ".git": true, "n8n-docker": true, "Self_Healer": false, "Scripts": true}, "recent_files": 519, "has_git": true, "appears_active": true, "relative_path": "N8N_Builder_Community", "is_current_project": false}, "N8N_Builder_Community.backup.20250705_152043": {"exists": true, "modified": "2025-07-05T15:20:43.776458", "total_files": 488, "total_directories": 273, "total_size_mb": 7.08, "key_files": {"README.md": true, "requirements.txt": true, "run.py": true, "setup.py": true, ".git": true, "n8n-docker": true, "Self_Healer": false, "Scripts": false}, "recent_files": 488, "has_git": true, "appears_active": true, "relative_path": "N8N_Builder_Community.backup.20250705_152043", "is_current_project": false}, "N8N_Builder_Community.backup.20250705_154242": {"exists": true, "modified": "2025-07-05T15:42:42.896625", "total_files": 488, "total_directories": 273, "total_size_mb": 7.08, "key_files": {"README.md": true, "requirements.txt": true, "run.py": true, "setup.py": true, ".git": true, "n8n-docker": true, "Self_Healer": false, "Scripts": false}, "recent_files": 488, "has_git": true, "appears_active": true, "relative_path": "N8N_Builder_Community.backup.20250705_154242", "is_current_project": false}, "N8N_Builder_Community.backup.20250705_154255": {"exists": true, "modified": "2025-07-05T15:42:56.178246", "total_files": 488, "total_directories": 273, "total_size_mb": 7.08, "key_files": {"README.md": true, "requirements.txt": true, "run.py": true, "setup.py": true, ".git": true, "n8n-docker": true, "Self_Healer": false, "Scripts": false}, "recent_files": 488, "has_git": true, "appears_active": true, "relative_path": "N8N_Builder_Community.backup.20250705_154255", "is_current_project": false}}, "analysis": {"current_project": "N8N_Builder", "git_repositories": ["N8N_Builder", "N8N_Builder_BACKUP_20250703_174421", "N8N_Builder_Community", "N8N_Builder_Community.backup.20250705_152043", "N8N_Builder_Community.backup.20250705_154242", "N8N_Builder_Community.backup.20250705_154255"], "backup_folders": ["N8N_Builder_BACKUP_20250703_174421", "N8N_Builder_Community", "N8N_Builder_Community.backup.20250705_152043", "N8N_Builder_Community.backup.20250705_154242", "N8N_Builder_Community.backup.20250705_154255"], "derivative_folders": ["N8N_Builder_Community", "N8N_Builder_Community.backup.20250705_152043", "N8N_Builder_Community.backup.20250705_154242", "N8N_Builder_Community.backup.20250705_154255"], "obsolete_candidates": [], "active_projects": ["N8N_Builder", "N8N_Builder_BACKUP_20250703_174421", "N8N_Builder_Community", "N8N_Builder_Community.backup.20250705_152043", "N8N_Builder_Community.backup.20250705_154242", "N8N_Builder_Community.backup.20250705_154255"]}, "recommendations": ["KEEP: N8N_Builder (current active project)", "REVIEW: N8N_Builder_BACKUP_20250703_174421 (has Git repository - check if still needed)", "REVIEW: N8N_Builder_Community (has Git repository - check if still needed)", "REVIEW: N8N_Builder_Community.backup.20250705_152043 (has Git repository - check if still needed)", "REVIEW: N8N_Builder_Community.backup.20250705_154242 (has Git repository - check if still needed)", "REVIEW: N8N_Builder_Community.backup.20250705_154255 (has Git repository - check if still needed)", "KEEP: N8N_Builder_Community (derivative folder with recent activity)", "KEEP: N8N_Builder_Community.backup.20250705_152043 (derivative folder with recent activity)", "KEEP: N8N_Builder_Community.backup.20250705_154242 (derivative folder with recent activity)", "KEEP: N8N_Builder_Community.backup.20250705_154255 (derivative folder with recent activity)", "REVIEW FOR REMOVAL: N8N_Builder_BACKUP_20250703_174421 (appears to be backup/copy)", "REVIEW FOR REMOVAL: N8N_Builder_Community (appears to be backup/copy)", "REVIEW FOR REMOVAL: N8N_Builder_Community.backup.20250705_152043 (appears to be backup/copy)", "REVIEW FOR REMOVAL: N8N_Builder_Community.backup.20250705_154242 (appears to be backup/copy)", "REVIEW FOR REMOVAL: N8N_Builder_Community.backup.20250705_154255 (appears to be backup/copy)"]}