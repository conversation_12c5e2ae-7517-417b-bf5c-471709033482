# VS Code MCP Compatibility Check
# Quick script to verify VS Code version and MCP support

Write-Host "VS Code MCP Compatibility Check" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Check VS Code version
Write-Host "`nChecking VS Code version..." -ForegroundColor Yellow
try {
    $version = & code --version 2>$null | Select-Object -First 1
    Write-Host "Current version: $version" -ForegroundColor Green
    
    # Parse version
    if ($version -match "^(\d+)\.(\d+)") {
        $major = [int]$matches[1]
        $minor = [int]$matches[2]
        
        Write-Host "Parsed: $major.$minor" -ForegroundColor Gray
        
        if ($major -eq 1 -and $minor -ge 102) {
            Write-Host "VS Code supports MCP (requires 1.102+)" -ForegroundColor Green
            $mcpSupported = $true
        } else {
            Write-Host "VS Code version too old for MCP (requires 1.102+)" -ForegroundColor Red
            Write-Host "Current: $major.$minor, Required: 1.102+" -ForegroundColor Yellow
            $mcpSupported = $false
        }
    } else {
        Write-Host "Could not parse version number" -ForegroundColor Yellow
        $mcpSupported = $false
    }
} catch {
    Write-Host "VS Code not found or not in PATH" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Gray
    $mcpSupported = $false
}

# Check for extensions that might provide MCP-like functionality
Write-Host "`nChecking for relevant extensions..." -ForegroundColor Yellow
try {
    $extensions = & code --list-extensions 2>$null
    $relevantExtensions = $extensions | Where-Object { 
        $_ -match "(mcp|context|protocol|ai|copilot)" 
    }
    
    if ($relevantExtensions) {
        Write-Host "Found relevant extensions:" -ForegroundColor Green
        $relevantExtensions | ForEach-Object { Write-Host "  - $_" -ForegroundColor White }
    } else {
        Write-Host "No MCP-related extensions found" -ForegroundColor Gray
    }
} catch {
    Write-Host "Could not check extensions" -ForegroundColor Yellow
}

# Recommendations
Write-Host "`nRecommendations:" -ForegroundColor Cyan
if (-not $mcpSupported) {
    Write-Host "1. Download latest VS Code from: https://code.visualstudio.com/download" -ForegroundColor White
    Write-Host "2. Install the latest version (1.103 or newer)" -ForegroundColor White
    Write-Host "3. Restart your terminal after installation" -ForegroundColor White
    Write-Host "4. Run this script again to verify" -ForegroundColor White
} else {
    Write-Host "Your VS Code version supports MCP!" -ForegroundColor Green
    Write-Host "Next steps:" -ForegroundColor White
    Write-Host "1. Create .vscode/mcp.json configuration" -ForegroundColor White
    Write-Host "2. Configure N8N MCP servers" -ForegroundColor White
    Write-Host "3. Test MCP functionality" -ForegroundColor White
}

Write-Host "`nMCP Documentation:" -ForegroundColor Cyan
Write-Host "- VS Code MCP Guide: https://code.visualstudio.com/docs/copilot/chat/mcp-servers" -ForegroundColor White
Write-Host "- MCP Specification: https://modelcontextprotocol.io/" -ForegroundColor White

return $mcpSupported
