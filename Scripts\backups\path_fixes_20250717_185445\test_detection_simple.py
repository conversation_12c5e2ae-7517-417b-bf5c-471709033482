#!/usr/bin/env python3
"""Simple test of detection patterns."""

import re
from pathlib import Path

# Test patterns
patterns = [
    r"Self-Healer",
    r"Self_Healer", 
    r"KnowledgeBase",
    r"healer_manager"
]

# Test file
test_file = Path("n8n_builder/optional_integrations.py")

print(f"Testing detection on: {test_file}")

if test_file.exists():
    with open(test_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    matches_found = 0
    for line_num, line in enumerate(lines, 1):
        for pattern in patterns:
            if re.search(pattern, line, re.IGNORECASE):
                print(f"Line {line_num}: Found '{pattern}' in: {line.strip()}")
                matches_found += 1
    
    print(f"\nTotal matches found: {matches_found}")
else:
    print(f"File not found: {test_file}")
