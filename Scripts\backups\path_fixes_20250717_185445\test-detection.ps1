# Simple test of detection patterns
param(
    [string]$TestFile = "n8n_builder\optional_integrations.py"
)

Write-Host "Testing detection on: $TestFile"

if (Test-Path $TestFile) {
    $content = Get-Content -Path $TestFile -Raw
    $lines = $content -split "`n"
    
    $patterns = @("Self-Healer", "Self_Healer", "KnowledgeBase", "healer_manager")
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        foreach ($pattern in $patterns) {
            if ($line -match $pattern) {
                Write-Host "Line $($i + 1): Found '$pattern' in: $($line.Trim())"
            }
        }
    }
} else {
    Write-Host "File not found: $TestFile"
}
