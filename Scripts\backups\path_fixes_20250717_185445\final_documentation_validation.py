#!/usr/bin/env python3
"""
Final Documentation Validation Script for N8N_Builder

This script provides a final validation after improvements to ensure:
1. All tunnel references are updated to LocalTunnel
2. Developer onboarding is streamlined
3. LocalTunnel setup is clearly explained
4. Documentation is clean, concise, and accurate

Author: N8N_Builder Team
Date: 2025-01-16
Purpose: Final documentation quality validation
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime

class FinalDocumentationValidator:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.validation_results = {
            "tunnel_references": [],
            "developer_onboarding": [],
            "localtunnel_quality": [],
            "overall_quality": []
        }
        
    def validate_tunnel_references(self):
        """Validate that all tunnel references are updated to LocalTunnel"""
        print("🔍 Validating tunnel references...")
        
        key_files = [
            "README.md",
            "GETTING_STARTED.md", 
            "n8n-docker/README-LocalTunnel.md",
            "Documentation/ReadMe_TunnelSetup.md"
        ]
        
        issues = []
        successes = []
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for outdated ngrok references (excluding archived content)
                    if "ngrok" in content.lower() and "archive" not in str(file_path).lower():
                        # Check if it's just mentioning alternatives
                        if "alternative" in content.lower() or "option" in content.lower():
                            successes.append(f"✅ {file_path}: Mentions ngrok as alternative only")
                        else:
                            issues.append(f"❌ {file_path}: Still contains ngrok references")
                    
                    # Check for LocalTunnel presence in key files
                    if "localtunnel" in content.lower() or "loca.lt" in content.lower():
                        successes.append(f"✅ {file_path}: Contains LocalTunnel references")
                    elif file_path in ["GETTING_STARTED.md", "n8n-docker/README-LocalTunnel.md"]:
                        issues.append(f"❌ {file_path}: Missing LocalTunnel references")
                        
                except Exception as e:
                    issues.append(f"❌ Error reading {file_path}: {e}")
            else:
                issues.append(f"❌ File not found: {file_path}")
        
        self.validation_results["tunnel_references"] = {
            "issues": issues,
            "successes": successes,
            "score": len(successes) / (len(successes) + len(issues)) if (successes or issues) else 1.0
        }
        
        for success in successes:
            print(f"  {success}")
        for issue in issues:
            print(f"  {issue}")
    
    def validate_developer_onboarding(self):
        """Validate developer onboarding experience"""
        print("👨‍💻 Validating developer onboarding...")
        
        onboarding_checks = [
            ("README.md", "Has quick start table", r"Time Available.*Start Here"),
            ("GETTING_STARTED.md", "Has step-by-step setup", r"Step \d+:|## Step \d+"),
            ("Documentation/DEVELOPER_QUICK_REFERENCE.md", "Quick reference exists", r"Essential Commands"),
            ("n8n-docker/README-LocalTunnel.md", "OAuth2 setup guide", r"OAuth2.*Setup|OAuth.*callback"),
            ("GETTING_STARTED.md", "Virtual environment setup", r"virtual environment|venv")
        ]
        
        issues = []
        successes = []
        
        for file_path, description, pattern in onboarding_checks:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if re.search(pattern, content, re.IGNORECASE):
                        successes.append(f"✅ {description} in {file_path}")
                    else:
                        issues.append(f"❌ {description} missing in {file_path}")
                        
                except Exception as e:
                    issues.append(f"❌ Error checking {file_path}: {e}")
            else:
                issues.append(f"❌ File not found: {file_path}")
        
        self.validation_results["developer_onboarding"] = {
            "issues": issues,
            "successes": successes,
            "score": len(successes) / (len(successes) + len(issues)) if (successes or issues) else 1.0
        }
        
        for success in successes:
            print(f"  {success}")
        for issue in issues:
            print(f"  {issue}")
    
    def validate_localtunnel_quality(self):
        """Validate LocalTunnel documentation quality"""
        print("🌐 Validating LocalTunnel documentation quality...")
        
        localtunnel_file = self.project_root / "n8n-docker/README-LocalTunnel.md"
        
        quality_checks = [
            ("Quick start section", r"Quick Start|🚀.*Quick"),
            ("OAuth2 callback URL", r"oauth2-credential/callback"),
            ("Step-by-step instructions", r"Step \d+|### \d+"),
            ("Troubleshooting section", r"Troubleshooting|Common Issues"),
            ("Service examples", r"Twitter|Google|GitHub"),
            ("PowerShell commands", r"Start-LocalTunnel\.ps1"),
            ("Browser password explanation", r"password.*prompt|browser.*password")
        ]
        
        issues = []
        successes = []
        
        if localtunnel_file.exists():
            try:
                with open(localtunnel_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for description, pattern in quality_checks:
                    if re.search(pattern, content, re.IGNORECASE):
                        successes.append(f"✅ {description}")
                    else:
                        issues.append(f"❌ Missing {description}")
                
                # Check file length (should be comprehensive but not too long)
                line_count = len(content.split('\n'))
                if 50 <= line_count <= 150:
                    successes.append(f"✅ Good length ({line_count} lines)")
                else:
                    issues.append(f"❌ Length issue ({line_count} lines - should be 50-150)")
                    
            except Exception as e:
                issues.append(f"❌ Error reading LocalTunnel documentation: {e}")
        else:
            issues.append("❌ LocalTunnel documentation file not found")
        
        self.validation_results["localtunnel_quality"] = {
            "issues": issues,
            "successes": successes,
            "score": len(successes) / (len(successes) + len(issues)) if (successes or issues) else 1.0
        }
        
        for success in successes:
            print(f"  {success}")
        for issue in issues:
            print(f"  {issue}")
    
    def validate_overall_quality(self):
        """Validate overall documentation quality"""
        print("📊 Validating overall documentation quality...")
        
        key_files = [
            "README.md",
            "GETTING_STARTED.md",
            "Documentation/DEVELOPER_QUICK_REFERENCE.md",
            "n8n-docker/README-LocalTunnel.md"
        ]
        
        issues = []
        successes = []
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for good structure
                    if content.startswith('# '):
                        successes.append(f"✅ {file_path}: Has proper title")
                    else:
                        issues.append(f"❌ {file_path}: Missing proper title")
                    
                    # Check for sections
                    if '## ' in content:
                        successes.append(f"✅ {file_path}: Has sections")
                    else:
                        issues.append(f"❌ {file_path}: Missing sections")
                    
                    # Check for code examples
                    if '```' in content:
                        successes.append(f"✅ {file_path}: Has code examples")
                    
                    # Check for emojis (good for readability)
                    if re.search(r'[🎯🚀📖🔧⚡✅🌟💡🤖🔍👨‍💻📊🌐]', content):
                        successes.append(f"✅ {file_path}: Has visual elements")
                        
                except Exception as e:
                    issues.append(f"❌ Error checking {file_path}: {e}")
            else:
                issues.append(f"❌ File not found: {file_path}")
        
        self.validation_results["overall_quality"] = {
            "issues": issues,
            "successes": successes,
            "score": len(successes) / (len(successes) + len(issues)) if (successes or issues) else 1.0
        }
        
        for success in successes:
            print(f"  {success}")
        for issue in issues:
            print(f"  {issue}")
    
    def generate_final_report(self):
        """Generate final validation report"""
        print("\n" + "="*60)
        print("📋 FINAL DOCUMENTATION VALIDATION REPORT")
        print("="*60)
        
        # Calculate overall score
        scores = [result["score"] for result in self.validation_results.values()]
        overall_score = sum(scores) / len(scores) if scores else 0
        
        print(f"\n🎯 Overall Score: {overall_score:.1%}")
        
        # Category scores
        print(f"\n📊 Category Scores:")
        for category, result in self.validation_results.items():
            score = result["score"]
            status = "✅ PASS" if score >= 0.8 else "⚠️ NEEDS WORK" if score >= 0.6 else "❌ FAIL"
            print(f"  {category.replace('_', ' ').title()}: {score:.1%} {status}")
        
        # Summary of remaining issues
        all_issues = []
        for result in self.validation_results.values():
            all_issues.extend(result["issues"])
        
        if all_issues:
            print(f"\n⚠️ Remaining Issues ({len(all_issues)}):")
            for issue in all_issues[:10]:  # Show top 10
                print(f"  {issue}")
            if len(all_issues) > 10:
                print(f"  ... and {len(all_issues) - 10} more")
        else:
            print(f"\n✅ No critical issues found!")
        
        # Recommendations
        print(f"\n🎯 Recommendations:")
        if overall_score >= 0.9:
            print("  🌟 Excellent! Documentation is ready for developers")
            print("  📝 Consider periodic reviews to maintain quality")
        elif overall_score >= 0.8:
            print("  👍 Good quality! Address remaining issues for excellence")
            print("  🔍 Focus on areas with lower scores")
        else:
            print("  🔧 Needs improvement before developer onboarding")
            print("  📋 Address critical issues first, then optimize")
        
        # Next steps
        print(f"\n🚀 Next Steps:")
        print("  1. Review and address any remaining issues")
        print("  2. Test the developer onboarding process")
        print("  3. Verify LocalTunnel setup works end-to-end")
        print("  4. Get feedback from a new developer")
        
        return overall_score
    
    def run_full_validation(self):
        """Run complete validation process"""
        print("🚀 N8N_Builder Final Documentation Validation")
        print("=" * 50)
        
        self.validate_tunnel_references()
        print()
        self.validate_developer_onboarding()
        print()
        self.validate_localtunnel_quality()
        print()
        self.validate_overall_quality()
        
        return self.generate_final_report()

def main():
    """Main execution function"""
    validator = FinalDocumentationValidator()
    score = validator.run_full_validation()
    
    # Save results
    results_path = Path("data/final_documentation_validation.json")
    results_path.parent.mkdir(exist_ok=True)
    
    import json
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump({
            "validation_date": datetime.now().isoformat(),
            "overall_score": score,
            "results": validator.validation_results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Detailed results saved to: {results_path}")

if __name__ == "__main__":
    main()
