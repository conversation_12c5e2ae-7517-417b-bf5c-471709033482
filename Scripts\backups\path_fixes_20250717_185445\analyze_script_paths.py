#!/usr/bin/env python3
"""
Script Path Analysis Tool
Analyzes all scripts in the Scripts folder to identify which ones need path adjustments
after being moved from root to Scripts subfolder.

This script identifies patterns that suggest scripts are assuming they're running from root:
- Direct references to project folders (n8n_builder/, Self_Healer/, etc.)
- File operations on root-level files (README.md, requirements.txt, etc.)
- Relative paths that would break when run from Scripts/
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple

class ScriptPathAnalyzer:
    def __init__(self):
        self.scripts_dir = Path(__file__).parent
        self.project_root = self.scripts_dir.parent
        
        # Common project folders that scripts might reference
        self.project_folders = {
            'n8n_builder', 'Self_Healer', 'KnowledgeBase', 'Documentation', 
            'config', 'data', 'tests', 'Tests', 'archive', 'Scripts'
        }
        
        # Root-level files that scripts might reference
        self.root_files = {
            'README.md', 'requirements.txt', 'setup.py', 'run.py', 
            '.gitignore', 'docker-compose.yml', 'Dockerfile'
        }
        
        # Patterns that suggest root-relative paths
        self.path_patterns = [
            # Direct folder references
            r'["\'](?:\.\/)?(' + '|'.join(self.project_folders) + r')\/[^"\']*["\']',
            # Root file references
            r'["\'](?:\.\/)?(' + '|'.join(self.root_files) + r')["\']',
            # Common path operations that might be root-relative
            r'os\.path\.join\(["\'][^"\']*["\']',
            r'Path\(["\'][^"\']*["\']',
            r'open\(["\'][^"\']*["\']',
            r'with open\(["\'][^"\']*["\']',
            # PowerShell path patterns
            r'\$[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*["\'][^"\']*["\']',
            r'Test-Path\s+["\'][^"\']*["\']',
            r'Get-Content\s+["\'][^"\']*["\']',
            r'Set-Content\s+["\'][^"\']*["\']'
        ]

    def analyze_file(self, file_path: Path) -> Dict:
        """Analyze a single script file for path issues."""
        result = {
            'file': str(file_path.relative_to(self.project_root)),
            'extension': file_path.suffix,
            'issues': [],
            'suspicious_lines': [],
            'needs_root_detection': False
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                if not line_stripped or line_stripped.startswith('#') or line_stripped.startswith('//'):
                    continue
                    
                # Check for suspicious path patterns
                for pattern in self.path_patterns:
                    matches = re.finditer(pattern, line, re.IGNORECASE)
                    for match in matches:
                        matched_text = match.group(0)
                        
                        # Skip if it's clearly an absolute path or URL
                        if any(x in matched_text.lower() for x in ['http', 'c:', 'd:', '\\\\', 'localhost']):
                            continue
                            
                        # Check if it references project folders or root files
                        issue_type = self._classify_path_issue(matched_text)
                        if issue_type:
                            result['issues'].append({
                                'line': line_num,
                                'content': line_stripped,
                                'match': matched_text,
                                'type': issue_type
                            })
                            result['suspicious_lines'].append(f"L{line_num}: {line_stripped}")
                            result['needs_root_detection'] = True
                            
        except Exception as e:
            result['error'] = str(e)
            
        return result

    def _classify_path_issue(self, path_text: str) -> str:
        """Classify the type of path issue found."""
        path_lower = path_text.lower()
        
        # Check for project folder references
        for folder in self.project_folders:
            if folder.lower() in path_lower:
                return f"project_folder_reference ({folder})"
                
        # Check for root file references
        for file in self.root_files:
            if file.lower() in path_lower:
                return f"root_file_reference ({file})"
                
        # Check for relative path patterns that might be problematic
        if path_text.startswith('"./') or path_text.startswith("'./"):
            return "relative_path_reference"
            
        return "potential_path_issue"

    def get_script_files(self) -> List[Path]:
        """Get all script files in the Scripts directory."""
        script_extensions = {'.py', '.ps1', '.bat', '.sh'}
        script_files = []
        
        for file_path in self.scripts_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in script_extensions:
                script_files.append(file_path)
                
        return sorted(script_files)

    def analyze_all_scripts(self) -> Dict:
        """Analyze all scripts and return comprehensive results."""
        script_files = self.get_script_files()
        results = {
            'analysis_summary': {
                'total_scripts': len(script_files),
                'scripts_with_issues': 0,
                'total_issues': 0
            },
            'scripts': []
        }
        
        for script_file in script_files:
            print(f"Analyzing: {script_file.name}")
            analysis = self.analyze_file(script_file)
            results['scripts'].append(analysis)
            
            if analysis['needs_root_detection']:
                results['analysis_summary']['scripts_with_issues'] += 1
                results['analysis_summary']['total_issues'] += len(analysis['issues'])
                
        return results

    def generate_markdown_report(self, results: Dict) -> str:
        """Generate a markdown report of the analysis."""
        report = []
        report.append("# Script Path Analysis Report")
        report.append("")
        report.append("Analysis of Scripts folder to identify files that need path adjustments")
        report.append("after being moved from root to Scripts subfolder.")
        report.append("")
        
        # Summary
        summary = results['analysis_summary']
        report.append("## Summary")
        report.append(f"- **Total Scripts Analyzed**: {summary['total_scripts']}")
        report.append(f"- **Scripts Needing Updates**: {summary['scripts_with_issues']}")
        report.append(f"- **Total Path Issues Found**: {summary['total_issues']}")
        report.append("")
        
        # Scripts needing updates
        scripts_with_issues = [s for s in results['scripts'] if s['needs_root_detection']]
        
        if scripts_with_issues:
            report.append("## Scripts Requiring Path Updates")
            report.append("")
            
            for script in scripts_with_issues:
                report.append(f"### {script['file']}")
                report.append(f"**File Type**: {script['extension']}")
                report.append(f"**Issues Found**: {len(script['issues'])}")
                report.append("")
                
                # Group issues by type
                issue_types = {}
                for issue in script['issues']:
                    issue_type = issue['type']
                    if issue_type not in issue_types:
                        issue_types[issue_type] = []
                    issue_types[issue_type].append(issue)
                
                for issue_type, issues in issue_types.items():
                    report.append(f"**{issue_type}**:")
                    for issue in issues:
                        report.append(f"- Line {issue['line']}: `{issue['match']}`")
                        report.append(f"  ```")
                        report.append(f"  {issue['content']}")
                        report.append(f"  ```")
                    report.append("")
        
        # Recommended fixes
        report.append("## Recommended Path Fix Pattern")
        report.append("")
        report.append("For Python scripts, add this at the beginning:")
        report.append("```python")
        report.append("import os")
        report.append("from pathlib import Path")
        report.append("")
        report.append("# Get project root (parent of Scripts folder)")
        report.append("project_root = Path(__file__).parent.parent")
        report.append("os.chdir(project_root)  # Optional: change working directory")
        report.append("```")
        report.append("")
        report.append("For PowerShell scripts, add this at the beginning:")
        report.append("```powershell")
        report.append("# Get project root (parent of Scripts folder)")
        report.append("$ProjectRoot = Split-Path -Parent $PSScriptRoot")
        report.append("Set-Location $ProjectRoot  # Optional: change working directory")
        report.append("```")
        report.append("")
        
        return "\n".join(report)

def main():
    analyzer = ScriptPathAnalyzer()
    
    print("Analyzing all scripts in Scripts folder...")
    results = analyzer.analyze_all_scripts()
    
    # Generate markdown report
    report_content = analyzer.generate_markdown_report(results)
    
    # Save report
    report_path = analyzer.scripts_dir / "Reset_Paths.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\nAnalysis complete!")
    print(f"Report saved to: {report_path}")
    print(f"Scripts with issues: {results['analysis_summary']['scripts_with_issues']}")
    print(f"Total issues found: {results['analysis_summary']['total_issues']}")

if __name__ == "__main__":
    main()
