#!/usr/bin/env python3
"""
Prepare Initial Public Release for N8N_Builder Community Edition
===============================================================
Prepares the public repository for initial GitHub release with professional presentation.

Usage: python prepare_public_release.py [--public-repo-path PATH] [--version VERSION] [--dry-run]
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import json
import argparse
import subprocess
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class PublicReleasePreparator:
    """Prepare public repository for initial release."""
    
    def __init__(self, public_repo_path: str = "../N8N_Builder_Community", version: str = "1.0.0", dry_run: bool = False):
        self.public_repo_path = Path(public_repo_path)
        self.version = version
        self.dry_run = dry_run
        self.preparation_start_time = datetime.now()
        
        self.results = {
            "preparation_metadata": {
                "start_time": self.preparation_start_time.isoformat(),
                "public_repo_path": str(self.public_repo_path),
                "version": version,
                "dry_run": dry_run,
                "tasks_completed": [],
                "tasks_failed": []
            },
            "release_files": {},
            "git_operations": {}
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
    
    def run_command(self, command: str, cwd: str = None, timeout: int = 60) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", f"Command timed out after {timeout} seconds"
        except Exception as e:
            return False, "", str(e)
    
    def validate_public_repository(self) -> Dict[str, Any]:
        """Validate that public repository is ready for release."""
        task_name = "validate_public_repository"
        self.log("=== VALIDATING PUBLIC REPOSITORY ===")
        
        result = {
            "task_name": task_name,
            "success": True,
            "validation_results": {},
            "issues_found": []
        }
        
        if not self.public_repo_path.exists():
            result["success"] = False
            result["issues_found"].append(f"Public repository path does not exist: {self.public_repo_path}")
            return result
        
        # Check required files
        required_files = [
            "README.md",
            "requirements.txt", 
            "run.py",
            "setup.py",
            "LICENSE"
        ]
        
        missing_files = []
        for file_name in required_files:
            file_path = self.public_repo_path / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        result["validation_results"]["required_files_present"] = len(missing_files) == 0
        result["validation_results"]["missing_files"] = missing_files
        
        if missing_files:
            result["success"] = False
            result["issues_found"].append(f"Missing required files: {missing_files}")
        
        # Check directory structure
        required_dirs = ["n8n_builder", "Documentation", "static"]
        missing_dirs = []
        for dir_name in required_dirs:
            dir_path = self.public_repo_path / dir_name
            if not dir_path.exists():
                missing_dirs.append(dir_name)
        
        result["validation_results"]["required_dirs_present"] = len(missing_dirs) == 0
        result["validation_results"]["missing_dirs"] = missing_dirs
        
        if missing_dirs:
            result["success"] = False
            result["issues_found"].append(f"Missing required directories: {missing_dirs}")
        
        if result["success"]:
            self.log("✅ Public repository validation passed")
        else:
            self.log(f"❌ Public repository validation failed: {len(result['issues_found'])} issues", "ERROR")
        
        return result
    
    def create_release_notes(self) -> Dict[str, Any]:
        """Create release notes for initial public release."""
        task_name = "create_release_notes"
        self.log("=== CREATING RELEASE NOTES ===")
        
        result = {
            "task_name": task_name,
            "success": True,
            "files_created": []
        }
        
        release_notes_content = f"""# N8N_Builder Community Edition v{self.version} - Initial Public Release

## 🎉 Welcome to N8N_Builder Community Edition!

We're excited to announce the initial public release of N8N_Builder Community Edition - an AI-powered workflow automation system that transforms plain English descriptions into fully functional n8n workflows.

## ✨ What's Included in Community Edition

### 🧠 **AI-Powered Workflow Generation**
- Convert natural language descriptions into n8n workflows
- Local AI processing using LM Studio for complete privacy
- Support for 200+ n8n integrations and services
- Intelligent workflow validation and optimization

### 🏠 **Local & Private**
- 100% local processing - no data sent to external services
- Works with LM Studio and local LLM models
- Complete privacy and control over your automation workflows
- No internet connection required for core functionality

### 🌐 **Developer-Friendly**
- REST API with OpenAPI documentation
- Clean, responsive web interface
- Command-line interface for automation
- Extensible architecture for customization

### 🐳 **Easy Deployment**
- Docker support for containerized deployment
- Simple Python setup with virtual environments
- Cross-platform compatibility (Windows, macOS, Linux)
- Comprehensive documentation and examples

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- [LM Studio](https://lmstudio.ai/) with a compatible model
- [n8n](https://n8n.io/) (Docker or standalone)

### Installation
```bash
# Clone the repository
git clone https://github.com/vbwyrde/N8N_Builder.git
cd N8N_Builder

# Set up Python environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\\Scripts\\activate
pip install -r requirements.txt

# Start the application
python run.py
```

### First Workflow
1. Open http://localhost:8002 in your browser
2. Describe your workflow: "Send a Slack message when a new email arrives in Gmail"
3. Click Generate and watch N8N_Builder create your workflow
4. Import the generated JSON into n8n and start automating!

## 📚 Documentation

- **[Complete Documentation](Documentation/README.md)** - Comprehensive guides and tutorials
- **[API Documentation](Documentation/api/API_DOCUMENTATION.md)** - REST API reference
- **[Troubleshooting Guide](Documentation/TROUBLESHOOTING.md)** - Common issues and solutions
- **[Contributing Guidelines](CONTRIBUTING.md)** - How to contribute to the project

## 🛠️ Supported Integrations

N8N_Builder Community Edition supports all n8n integrations, including:

- **Communication**: Slack, Discord, Microsoft Teams, Telegram
- **Email**: Gmail, Outlook, IMAP, SMTP
- **Cloud Storage**: Google Drive, Dropbox, OneDrive, AWS S3
- **Databases**: MySQL, PostgreSQL, MongoDB, SQLite
- **Productivity**: Google Sheets, Airtable, Notion, Trello
- **Development**: GitHub, GitLab, Jenkins, Docker
- **And 200+ more...**

## 🤝 Community & Support

- **GitHub Issues**: Report bugs and request features
- **GitHub Discussions**: Ask questions and share ideas
- **Documentation**: Comprehensive guides and examples
- **Contributing**: We welcome contributions from the community!

## 📄 License

N8N_Builder Community Edition is released under the MIT License. See [LICENSE](LICENSE) for details.

## 🙏 Acknowledgments

- **[n8n](https://n8n.io/)** - The amazing workflow automation platform
- **[LM Studio](https://lmstudio.ai/)** - Local AI model hosting
- **[FastAPI](https://fastapi.tiangolo.com/)** - Modern web framework
- **The open-source community** - For inspiration and support

## 🔮 What's Next?

This is just the beginning! We have exciting plans for future releases:

- Enhanced workflow templates and examples
- Improved AI model integration
- Advanced workflow optimization
- Community-contributed integrations
- Performance improvements and optimizations

## 📞 Getting Help

- **Documentation**: Check our comprehensive guides first
- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and community support

---

**Ready to start automating?** Follow our [Quick Start guide](README.md#quick-start) and create your first AI-generated workflow in minutes!

**Happy Automating!** 🤖✨
"""
        
        release_notes_file = self.public_repo_path / "RELEASE_NOTES.md"
        
        if not self.dry_run:
            with open(release_notes_file, 'w', encoding='utf-8') as f:
                f.write(release_notes_content)
        
        result["files_created"].append(str(release_notes_file))
        self.log(f"{'[DRY RUN] ' if self.dry_run else ''}Created release notes: RELEASE_NOTES.md")
        
        return result
    
    def create_version_file(self) -> Dict[str, Any]:
        """Create version file for the release."""
        task_name = "create_version_file"
        self.log("=== CREATING VERSION FILE ===")
        
        result = {
            "task_name": task_name,
            "success": True,
            "files_created": []
        }
        
        version_info = {
            "version": self.version,
            "release_date": datetime.now().isoformat(),
            "edition": "community",
            "build_info": {
                "python_version": "3.8+",
                "dependencies": "requirements.txt",
                "platform": "cross-platform"
            }
        }
        
        version_file = self.public_repo_path / "VERSION.json"
        
        if not self.dry_run:
            with open(version_file, 'w') as f:
                json.dump(version_info, f, indent=2)
        
        result["files_created"].append(str(version_file))
        self.log(f"{'[DRY RUN] ' if self.dry_run else ''}Created version file: VERSION.json")
        
        return result
    
    def setup_git_repository(self) -> Dict[str, Any]:
        """Set up Git repository with proper initial commit."""
        task_name = "setup_git_repository"
        self.log("=== SETTING UP GIT REPOSITORY ===")
        
        result = {
            "task_name": task_name,
            "success": True,
            "git_operations": [],
            "commit_hash": None
        }
        
        if self.dry_run:
            result["skipped"] = "Skipped in dry run mode"
            self.log("ℹ️ Git setup skipped in dry run mode")
            return result
        
        # Initialize git repository
        init_cmd = "git init"
        success, stdout, stderr = self.run_command(init_cmd, cwd=str(self.public_repo_path))
        result["git_operations"].append(f"git init: {'SUCCESS' if success else 'FAILED'}")
        
        if not success:
            result["success"] = False
            self.log(f"❌ Git init failed: {stderr}", "ERROR")
            return result
        
        # Set up git config (if not already set globally)
        config_commands = [
            'git config user.name "N8N_Builder Release"',
            'git config user.email "<EMAIL>"'
        ]
        
        for cmd in config_commands:
            success, stdout, stderr = self.run_command(cmd, cwd=str(self.public_repo_path))
            result["git_operations"].append(f"{cmd}: {'SUCCESS' if success else 'FAILED'}")
        
        # Add all files
        add_cmd = "git add ."
        success, stdout, stderr = self.run_command(add_cmd, cwd=str(self.public_repo_path))
        result["git_operations"].append(f"git add: {'SUCCESS' if success else 'FAILED'}")
        
        if not success:
            result["success"] = False
            self.log(f"❌ Git add failed: {stderr}", "ERROR")
            return result
        
        # Create initial commit
        commit_msg = f"🎉 Initial release of N8N_Builder Community Edition v{self.version}\\n\\n✨ Features:\\n- AI-powered workflow generation\\n- Local LLM integration\\n- REST API and web interface\\n- 200+ n8n integrations\\n- Complete privacy and local processing"
        commit_cmd = f'git commit -m "{commit_msg}"'
        success, stdout, stderr = self.run_command(commit_cmd, cwd=str(self.public_repo_path))
        result["git_operations"].append(f"git commit: {'SUCCESS' if success else 'FAILED'}")
        
        if success:
            # Get commit hash
            hash_cmd = "git rev-parse HEAD"
            success, stdout, stderr = self.run_command(hash_cmd, cwd=str(self.public_repo_path))
            if success:
                result["commit_hash"] = stdout.strip()
            
            self.log("✅ Git repository setup completed")
        else:
            result["success"] = False
            self.log(f"❌ Git commit failed: {stderr}", "ERROR")
        
        return result
    
    def prepare_release(self) -> Dict[str, Any]:
        """Prepare the complete public release."""
        self.log("🚀 Starting Public Release Preparation")
        self.log(f"Target: {self.public_repo_path}")
        self.log(f"Version: {self.version}")
        self.log(f"Dry Run: {self.dry_run}")
        
        tasks = [
            self.validate_public_repository,
            self.create_release_notes,
            self.create_version_file,
            self.setup_git_repository
        ]
        
        overall_success = True
        
        for task_method in tasks:
            try:
                task_result = task_method()
                task_name = task_result["task_name"]
                
                self.results["release_files"][task_name] = task_result
                
                if task_result["success"]:
                    self.results["preparation_metadata"]["tasks_completed"].append(task_name)
                    self.log(f"✅ Task '{task_name}' completed successfully", "SUCCESS")
                else:
                    self.results["preparation_metadata"]["tasks_failed"].append(task_name)
                    overall_success = False
                    self.log(f"❌ Task '{task_name}' failed", "ERROR")
                    
                    # Stop on critical failures
                    if task_name in ["validate_public_repository"]:
                        self.log("🛑 Critical task failed - stopping preparation", "ERROR")
                        break
            
            except Exception as e:
                self.log(f"❌ Task crashed: {task_method.__name__} - {e}", "ERROR")
                self.results["preparation_metadata"]["tasks_failed"].append(task_method.__name__)
                overall_success = False
                break
        
        # Update final metadata
        self.results["preparation_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["preparation_metadata"]["duration_seconds"] = (datetime.now() - self.preparation_start_time).total_seconds()
        self.results["preparation_metadata"]["overall_success"] = overall_success
        
        # Display summary
        self.display_preparation_summary()
        
        return self.results
    
    def display_preparation_summary(self):
        """Display preparation summary."""
        print("\n" + "="*70)
        print("🚀 PUBLIC RELEASE PREPARATION SUMMARY")
        print("="*70)
        
        metadata = self.results["preparation_metadata"]
        completed = len(metadata["tasks_completed"])
        failed = len(metadata["tasks_failed"])
        
        print(f"⏱️  Duration: {metadata.get('duration_seconds', 0):.2f} seconds")
        print(f"✅ Completed Tasks: {completed}")
        print(f"❌ Failed Tasks: {failed}")
        print(f"📦 Version: {metadata['version']}")
        
        if metadata["overall_success"]:
            print("\n🎉 RELEASE PREPARATION SUCCESSFUL!")
            print("✅ All tasks completed successfully")
            if not self.dry_run:
                print("🚀 Public repository is ready for GitHub publication!")
                print(f"📁 Location: {metadata['public_repo_path']}")
                print("📋 Next steps:")
                print("   1. Create new GitHub repository")
                print("   2. Add remote origin")
                print("   3. Push to GitHub")
                print("   4. Configure repository settings")
            else:
                print("ℹ️ This was a dry run - use without --dry-run to execute")
        else:
            print("\n⚠️ RELEASE PREPARATION FAILED!")
            print("❌ One or more tasks failed")
            print("🔧 Review task results and address issues")
        
        print("="*70)
    
    def export_results(self, output_file: str = "public_release_preparation_results.json"):
        """Export preparation results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Preparation results exported to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Prepare Public Release")
    parser.add_argument("--public-repo-path", default="../N8N_Builder_Community", help="Public repository path")
    parser.add_argument("--version", default="1.0.0", help="Release version")
    parser.add_argument("--dry-run", action="store_true", help="Dry run mode")
    parser.add_argument("--output", default="public_release_preparation_results.json", help="Output file")
    
    args = parser.parse_args()
    
    # Initialize preparator
    preparator = PublicReleasePreparator(args.public_repo_path, args.version, args.dry_run)
    
    # Prepare release
    results = preparator.prepare_release()
    
    # Export results
    preparator.export_results(args.output)
    
    # Exit with appropriate code
    if results["preparation_metadata"]["overall_success"]:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
