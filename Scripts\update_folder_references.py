#!/usr/bin/env python3
"""
Folder Reference Update Script
=============================
Finds and updates references to old folder names in code and documentation.
Specifically updates N8N_Builder_Community → N8N_Builder_Community references.

Usage: python update_folder_references.py [--dry-run] [--execute]
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import re
import argparse
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

class FolderReferenceUpdater:
    """Updates folder references in code and documentation files."""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.project_root = Path(".")
        self.old_name = "N8N_Builder_Community"
        self.new_name = "N8N_Builder_Community"
        self.updates_log = []
        
        # File extensions to search
        self.search_extensions = {
            '.py', '.ps1', '.md', '.txt', '.json', '.yaml', '.yml', 
            '.bat', '.sh', '.cfg', '.ini', '.log'
        }
        
        # Directories to exclude from search
        self.exclude_dirs = {
            '.git', '__pycache__', 'venv', '.venv', 'node_modules',
            'backup_naming_migration_*', '.augment'
        }
    
    def log_action(self, action: str, details: str = ""):
        """Log update action."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {action}"
        if details:
            log_entry += f": {details}"
        
        self.updates_log.append(log_entry)
        print(log_entry)
    
    def should_skip_directory(self, dir_path: Path) -> bool:
        """Check if directory should be skipped."""
        dir_name = dir_path.name
        
        # Skip exact matches
        if dir_name in self.exclude_dirs:
            return True
        
        # Skip pattern matches (like backup folders)
        for pattern in self.exclude_dirs:
            if '*' in pattern:
                pattern_regex = pattern.replace('*', '.*')
                if re.match(pattern_regex, dir_name):
                    return True
        
        return False
    
    def should_search_file(self, file_path: Path) -> bool:
        """Check if file should be searched."""
        return file_path.suffix.lower() in self.search_extensions
    
    def find_references_in_file(self, file_path: Path) -> List[Tuple[int, str, str]]:
        """Find references to old folder name in a file."""
        references = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                if self.old_name in line:
                    # Create updated line
                    updated_line = line.replace(self.old_name, self.new_name)
                    references.append((line_num, line.rstrip(), updated_line.rstrip()))
            
        except Exception as e:
            self.log_action("ERROR", f"Failed to read {file_path}: {e}")
        
        return references
    
    def update_file_references(self, file_path: Path, references: List[Tuple[int, str, str]]) -> bool:
        """Update references in a file."""
        if not references:
            return True
        
        try:
            # Read entire file
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Replace all occurrences
            updated_content = content.replace(self.old_name, self.new_name)
            
            # Write back if not dry run
            if not self.dry_run:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
            
            return True
            
        except Exception as e:
            self.log_action("ERROR", f"Failed to update {file_path}: {e}")
            return False
    
    def scan_project_files(self) -> Dict[str, List[Tuple[int, str, str]]]:
        """Scan all project files for references."""
        self.log_action("SCAN_START", f"Searching for '{self.old_name}' references")
        
        file_references = {}
        total_files_scanned = 0
        
        for file_path in self.project_root.rglob('*'):
            # Skip directories
            if file_path.is_dir():
                continue
            
            # Skip if in excluded directory
            skip = False
            for parent in file_path.parents:
                if self.should_skip_directory(parent):
                    skip = True
                    break
            if skip:
                continue
            
            # Skip if wrong file type
            if not self.should_search_file(file_path):
                continue
            
            total_files_scanned += 1
            
            # Find references in this file
            references = self.find_references_in_file(file_path)
            if references:
                relative_path = str(file_path.relative_to(self.project_root))
                file_references[relative_path] = references
        
        self.log_action("SCAN_COMPLETE", f"Scanned {total_files_scanned} files, found references in {len(file_references)} files")
        return file_references
    
    def display_references(self, file_references: Dict[str, List[Tuple[int, str, str]]]):
        """Display found references."""
        if not file_references:
            self.log_action("NO_REFERENCES", "No references to old folder name found")
            return
        
        print("\n" + "="*70)
        print(f"📁 FOLDER REFERENCE ANALYSIS: {self.old_name} → {self.new_name}")
        print("="*70)
        
        total_references = sum(len(refs) for refs in file_references.values())
        print(f"\n📊 Found {total_references} references in {len(file_references)} files:")
        
        for file_path, references in file_references.items():
            print(f"\n📄 {file_path} ({len(references)} references):")
            for line_num, old_line, new_line in references[:3]:  # Show first 3
                print(f"   Line {line_num}: {old_line[:60]}...")
                if not self.dry_run:
                    print(f"   Updated:   {new_line[:60]}...")
            
            if len(references) > 3:
                print(f"   ... and {len(references) - 3} more references")
        
        print("="*70)
    
    def execute_updates(self, file_references: Dict[str, List[Tuple[int, str, str]]]) -> bool:
        """Execute the reference updates."""
        if not file_references:
            return True
        
        self.log_action("UPDATE_START", f"Updating references in {len(file_references)} files")
        
        success_count = 0
        for file_path, references in file_references.items():
            full_path = self.project_root / file_path
            
            if self.update_file_references(full_path, references):
                action = "WOULD_UPDATE" if self.dry_run else "UPDATED"
                self.log_action(action, f"{file_path} ({len(references)} references)")
                success_count += 1
            else:
                self.log_action("FAILED", f"Could not update {file_path}")
        
        self.log_action("UPDATE_COMPLETE", f"Successfully updated {success_count}/{len(file_references)} files")
        return success_count == len(file_references)
    
    def run_update(self) -> bool:
        """Run complete reference update process."""
        mode = "DRY RUN" if self.dry_run else "EXECUTION"
        self.log_action("START", f"Folder reference update ({mode})")
        
        # Scan for references
        file_references = self.scan_project_files()
        
        # Display what was found
        self.display_references(file_references)
        
        # Execute updates
        success = self.execute_updates(file_references)
        
        if self.dry_run:
            self.log_action("DRY_RUN_COMPLETE", "Use --execute to perform actual updates")
        else:
            status = "SUCCESS" if success else "PARTIAL_FAILURE"
            self.log_action(status, "Reference update completed")
        
        return success
    
    def save_log(self, log_file: str = "folder_reference_updates.log"):
        """Save update log."""
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(self.updates_log))
        print(f"📄 Log saved to: {log_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Update folder references from N8N_Builder_Community to N8N_Builder_Community")
    parser.add_argument("--dry-run", action="store_true", default=True, help="Show what would be updated without executing")
    parser.add_argument("--execute", action="store_true", help="Actually execute the updates")
    
    args = parser.parse_args()
    
    # If --execute is specified, turn off dry-run
    dry_run = not args.execute
    
    updater = FolderReferenceUpdater(dry_run=dry_run)
    success = updater.run_update()
    updater.save_log()
    
    if dry_run:
        print("\n🔍 This was a DRY RUN. Use --execute to perform actual updates.")
    
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
