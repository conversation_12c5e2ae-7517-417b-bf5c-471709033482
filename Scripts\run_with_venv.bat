@echo off
echo ============================================================
echo N8N BUILDER - STARTING WITH VIRTUAL ENVIRONMENT
echo ============================================================

REM Check if virtual environment exists
if not exist "venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found!
    echo Please run: python -m venv venv
    echo Then run: .\venv\Scripts\python.exe -m pip install -r requirements.txt
    pause
    exit /b 1
)

echo Checking virtual environment dependencies...
venv\Scripts\python.exe -c "import pyodbc; print('OK')" >nul 2>&1
if errorlevel 1 (
    echo ERROR: pyodbc not found in virtual environment!
    echo Installing missing dependencies...
    venv\Scripts\python.exe -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
)

echo ✅ Virtual environment ready!
echo 🚀 Starting N8N Builder with virtual environment...

REM Run with virtual environment Python
venv\Scripts\python.exe run.py

echo N8N Builder has stopped.
pause
