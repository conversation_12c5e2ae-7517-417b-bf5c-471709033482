{"name": "ElthosRPG_Blog_Twitter", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 19}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-200, 140], "id": "0d1ae298-adc4-4b8b-a9b2-164da60edd33", "name": "Schedule Trigger"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "blogURL", "value": "={{ $('Random Selection').item.json.selectedPostURL }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-20, 400], "id": "34a040a5-f6cc-4249-8e16-6a3492e90623", "name": "Edit Field - blogURL"}, {"parameters": {"jsCode": "// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// Try different ways to get the blog URL\nlet blogUrl = null;\n\n// Method 1: Try to access the Edit Field node directly\ntry {\n  const blogUrlNode = $('Edit Field - blogURL');\n  if (blogUrlNode && blogUrlNode.first()) {\n    blogUrl = blogUrlNode.first().json.blogURL;\n    console.log('Got blog URL from direct access:', blogUrl);\n  }\n} catch (error) {\n  console.log('Direct access failed');\n}\n\n// Method 2: If that doesn't work, check if it's in the input data\nif (!blogUrl && $input.first().json.blogURL) {\n  blogUrl = $input.first().json.blogURL;\n  console.log('Got blog URL from input:', blogUrl);\n}\n\n// Method 3: Hardcode as fallback (replace with your actual URL)\nif (!blogUrl) {\n  blogUrl = 'https://ElthosRPG.Blogspot.com'; // Replace with your actual blog URL\n  console.log('Using hardcoded blog URL:', blogUrl);\n}\n\nconsole.log('LLM text:', text);\nconsole.log('Final blog URL:', blogUrl);\n\n// Remove <think>...</think> and everything in between\nconst cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Clean whitespace\ntext = cleaned.replace(/^\\s+|\\s+$/g, '');\n\nif (text.length >= 2 && text.charAt(0) === '\"' && text.charAt(text.length - 1) === '\"') {\n    text = text.substring(1, text.length - 1);\n}\n\n// Twitter character limit\nconst TARGET_LIMIT = 280;\n\n// Try the full text + URL first\nconst testTweet = text + ' ' + blogUrl;\nconsole.log('Test tweet length:', testTweet.length);\n\n// If it fits within limit, use it as-is\nif (testTweet.length <= TARGET_LIMIT) {\n    console.log('Tweet fits within limit, using full text');\n    return [{ json: { text: testTweet } }];\n}\n\n// If too long, try to preserve hashtags by truncating main content\nconst hashtagRegex = /#\\w+/g;\nconst hashtags = text.match(hashtagRegex) || [];\nconst hashtagsText = hashtags.join(' ');\n\n// Remove hashtags and em-dashes from main text to see content length\nconst contentWithoutHashtags = text.replace(hashtagRegex, '').replace(/—/g, '').replace(/\\s+/g, ' ').trim();\n\n// More precise calculation: content + \" \" + hashtags + \" \" + URL = TARGET_LIMIT\n// So: content = TARGET_LIMIT - hashtags.length - URL.length - 2 spaces - 3 for \"...\"\nconst spacesNeeded = 2; // one before hashtags, one before URL\nconst ellipsisLength = 3;\nconst availableForContent = TARGET_LIMIT - hashtagsText.length - blogUrl.length - spacesNeeded - ellipsisLength;\n\nconsole.log('Available space for content:', availableForContent);\nconsole.log('Content without hashtags:', contentWithoutHashtags);\nconsole.log('Hashtags found:', hashtags);\nconsole.log('Hashtags text length:', hashtagsText.length);\n\n// If we have enough space, use truncated content + hashtags\nif (availableForContent > 15 && hashtags.length > 0) {\n    const truncatedContent = contentWithoutHashtags.substring(0, availableForContent) + '...';\n    const finalTweet = truncatedContent + ' ' + hashtagsText + ' ' + blogUrl;\n    console.log('Using truncated content with hashtags');\n    console.log('Final tweet length:', finalTweet.length);\n    return [{ json: { text: finalTweet } }];\n}\n\n// Fallback: just truncate everything\nconst maxTextLength = TARGET_LIMIT - blogUrl.length - 1 - 3;\nconst truncatedText = text.substring(0, maxTextLength) + '...';\nconst fallbackTweet = truncatedText + ' ' + blogUrl;\n\nreturn [{ json: { text: fallbackTweet } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, 400], "id": "77829054-6fde-45c9-b38d-41714db1bbca", "name": "Code - Clean for Tweet"}, {"parameters": {"jsCode": "// Replace \"content\" with the actual field name from ScrapeNinja output\nconst content = $input.first().json.content;\n\n// Split into paragraphs by double line breaks\nconst paragraphs = content.split(/\\n\\s*\\n/);\n\n// Get the first paragraph\nlet firstParagraph = paragraphs[2] || \"\";\n\n// Limit to 3000 words\nconst words = firstParagraph.split(/\\s+/).slice(0, 3000);\nconst limitedParagraph = words.join(\" \");\n\n// PRESERVE the blogURL field from input\nconst blogURL =   $('Random Selection').first().json.selectedPostURL \n\nconsole.log('blogURL: ' + blogURL);\n\nreturn [{ json: { \n    firstParagraph: limitedParagraph,\n    blogURL: blogURL \n}}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [340, 400], "id": "48201c3a-929e-4146-b334-1e559d5199ea", "name": "Code - Get Paragraph 1"}, {"parameters": {"text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [1060, 400], "id": "c9a10aba-b039-4ec9-a53e-7a261d433a59", "name": "Create Tweet", "credentials": {"twitterOAuth2Api": {"id": "QMuVcnYLarrzm0jp", "name": "X account"}}}, {"parameters": {"operation": "extract-content", "html": "={{ $('Fetch Post').item.json.data }}", "outputMarkdown": true}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [160, 400], "id": "e30d9001-fc2e-4121-88c3-a063a301a4c8", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"model": {"__rl": true, "value": "deepseek-r1-distill-llama-8b", "mode": "list", "cachedResultName": "deepseek-r1-distill-llama-8b"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [540, 580], "id": "********-c9f1-4ad5-83c9-7d83f15a31e2", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "cqdpm9ID0q2zjSkV", "name": "LM Studio"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.firstParagraph }}", "messages": {"messageValues": [{"message": "You are a twitter guru who can take the best content from the blog entry and make the perfect tweet from it that is no more than 60 characters long. Use Text format. Include hash tags #IndieRPG #Elthos #TTRPG. Be concise and engaging, and sprinkle in your <PERSON> sense of humor."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [540, 400], "id": "9de9c97f-f612-4cf4-8873-435186553c58", "name": "Basic LLM Chain", "alwaysOutputData": false}, {"parameters": {"jsCode": "// Get input data\nconst postUrls = $input.first().json.postUrls;\nconst totalPosts = $input.first().json.totalPosts;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log(`Starting selection from ${totalPosts} available posts...`);\n\n// Use n8n's workflow static data to store exclusion list\n// This persists between workflow executions\nlet excludedUrls = [];\n\n// Try to get existing exclusion list from workflow static data\ntry {\n  const staticData = this.getWorkflowStaticData('global');\n  if (staticData.excludedBlogPosts && Array.isArray(staticData.excludedBlogPosts)) {\n    excludedUrls = staticData.excludedBlogPosts;\n    console.log(`Loaded ${excludedUrls.length} excluded URLs from workflow data`);\n  } else {\n    console.log('No exclusion list found in workflow data, starting fresh');\n    staticData.excludedBlogPosts = [];\n  }\n} catch (error) {\n  console.log('Error accessing workflow static data:', error.message);\n}\n\n// Filter out excluded URLs\nconst availableUrls = postUrls.filter(url => !excludedUrls.includes(url));\nconsole.log(`Available posts after exclusion: ${availableUrls.length}`);\n\n// If no available URLs (all excluded), reset the exclusion list\nif (availableUrls.length === 0) {\n  console.log('All posts excluded! Resetting exclusion list...');\n  excludedUrls = [];\n  availableUrls.push(...postUrls);\n}\n\n// Randomly select from available URLs\nconst randomIndex = Math.floor(Math.random() * availableUrls.length);\nconst selectedPostURL = availableUrls[randomIndex];\n\nconsole.log(`Selected post: ${selectedPostURL}`);\n\n// Add selected URL to exclusion list\nexcludedUrls.push(selectedPostURL);\n\n// Keep only the most recent 20 entries\nif (excludedUrls.length > 20) {\n  excludedUrls = excludedUrls.slice(-20);\n}\n\n// Save updated exclusion list back to workflow static data\ntry {\n  const staticData = this.getWorkflowStaticData('global');\n  staticData.excludedBlogPosts = excludedUrls;\n  console.log(`Updated exclusion list with ${excludedUrls.length} entries`);\n} catch (error) {\n  console.log('Error saving to workflow static data:', error.message);\n}\n\n// Return the selected URL along with metadata\nreturn [{ json: { \n  selectedPostURL: selectedPostURL,\n  randomIndex: randomIndex,\n  totalPosts: totalPosts,\n  availablePosts: availableUrls.length,\n  excludedCount: excludedUrls.length,\n  mainBlogURL: mainBlogURL\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 140], "id": "4e81d726-d7da-4ddd-9d3a-9afa49ec3460", "name": "Random Selection"}, {"parameters": {"jsCode": "// Get RSS XML from Fetch RSS Feed\nconst rssXml = $node['Fetch RSS Feed'].json.data;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log('Extracting URLs from RSS XML...');\nconsole.log('RSS XML length:', rssXml.length);\n\n// Parse RSS XML to extract blog post URLs\n// Look for <link> tags that contain blog post URLs\nconst linkMatches = rssXml.match(/<link[^>]*>([^<]+)<\\/link>/g) || [];\nconsole.log('Found', linkMatches.length, 'link tags');\n\n// Also look for alternate link format in entries\nconst entryMatches = rssXml.match(/<entry[\\s\\S]*?<\\/entry>/g) || [];\nconsole.log('Found', entryMatches.length, 'entry blocks');\n\nlet postUrls = [];\n\n// Extract URLs from link tags\nlinkMatches.forEach(match => {\n  const url = match.replace(/<link[^>]*>/, '').replace(/<\\/link>/, '').trim();\n  if (url.includes('elthosrpg.blogspot.com') && url.includes('/20') && url.endsWith('.html') && !postUrls.includes(url)) {\n    postUrls.push(url);\n    console.log('Added URL from link:', url);\n  }\n});\n\n// Extract URLs from entry blocks (alternative method)\nentryMatches.forEach(entry => {\n  // Look for alternate link with rel=\"alternate\"\n  const altLinkMatch = entry.match(/<link[^>]+rel=[\"']alternate[\"'][^>]+href=[\"']([^\"']+)[\"']/i);\n  if (altLinkMatch) {\n    const url = altLinkMatch[1];\n    if (url.includes('elthosrpg.blogspot.com') && url.includes('/20') && url.endsWith('.html') && !postUrls.includes(url)) {\n      postUrls.push(url);\n      console.log('Added URL from entry:', url);\n    }\n  }\n});\n\nconsole.log('Total unique URLs extracted:', postUrls.length);\n\n// If no URLs found, show debug info\nif (postUrls.length === 0) {\n  console.log('No URLs found! RSS XML preview:', rssXml.substring(0, 1000));\n  return [{ \n    postUrls: [],\n    totalPosts: 0,\n    mainBlogURL: mainBlogURL,\n    error: 'No URLs found in RSS feed',\n    rssPreview: rssXml.substring(0, 1000)\n  }];\n}\n\nreturn [{ \n  postUrls: postUrls,\n  totalPosts: postUrls.length,\n  mainBlogURL: mainBlogURL,\n  extractionMethod: 'rss_feed'\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, 140], "id": "ef96967c-4faa-4fc0-9b14-f9198d091657", "name": "Extract Post URLs"}, {"parameters": {"url": "={{ $json.rssURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch RSS Feed", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [160, 140], "id": "f512af73-609b-4438-ab5a-44a9f4eaab62"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "mainBlogURL", "value": "https://elthosrpg.blogspot.com/", "type": "string"}, {"id": "456721e2-5dce-41fa-9118-1891bca27395", "name": "rssURL", "value": "https://elthosrpg.blogspot.com/feeds/posts/default?max-results=999", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-20, 140], "id": "6fa123ae-3fb2-4faa-9453-2b98368e86e2", "name": "Set Main Blog URL"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-560, 140], "id": "b930b65c-1945-4cdc-a01a-1ffc83955a22", "name": "When clicking 'Execute workflow'", "disabled": true}, {"parameters": {"url": "={{ $json.selectedPostURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [-200, 400], "id": "a5333e70-f6fe-426a-83a4-f9b6ede66bdd"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Set Main Blog URL", "type": "main", "index": 0}]]}, "Edit Field - blogURL": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Code - Get Paragraph 1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Code - Clean for Tweet": {"main": [[{"node": "Create Tweet", "type": "main", "index": 0}]]}, "ScrapeNinja": {"main": [[{"node": "Code - Get Paragraph 1", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Code - Clean for Tweet", "type": "main", "index": 0}]]}, "Random Selection": {"main": [[{"node": "Fetch Post", "type": "main", "index": 0}]]}, "Fetch Post": {"main": [[{"node": "Edit Field - blogURL", "type": "main", "index": 0}]]}, "Extract Post URLs": {"main": [[{"node": "Random Selection", "type": "main", "index": 0}]]}, "Fetch RSS Feed": {"main": [[{"node": "Extract Post URLs", "type": "main", "index": 0}]]}, "Set Main Blog URL": {"main": [[{"node": "Fetch RSS Feed", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "6f47eaff-a726-4b63-9a43-3c06c88ef405", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "M4PfByDgjUvSFPqZ", "tags": []}