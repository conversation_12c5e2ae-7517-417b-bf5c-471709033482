# Fix VS Code PATH Priority
# This script ensures VS Code takes precedence over <PERSON><PERSON><PERSON> in the PATH

Write-Host "Fixing VS Code PATH Priority" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

# Check current PATH order
Write-Host "`nCurrent 'code' command locations:" -ForegroundColor Yellow
$codeLocations = & where code 2>$null
if ($codeLocations) {
    for ($i = 0; $i -lt $codeLocations.Count; $i++) {
        $priority = if ($i -eq 0) { " (CURRENT)" } else { "" }
        Write-Host "  $($i+1). $($codeLocations[$i])$priority" -ForegroundColor White
    }
} else {
    Write-Host "  No 'code' command found in PATH" -ForegroundColor Red
}

# Check VS Code version directly
$vscodePath = "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin\code"
Write-Host "`nChecking VS Code version directly..." -ForegroundColor Yellow

if (Test-Path $vscodePath) {
    try {
        $vscodeVersion = & $vscodePath --version 2>$null | Select-Object -First 1
        Write-Host "VS Code version: $vscodeVersion" -ForegroundColor Green
        
        # Parse version to check MCP support
        if ($vscodeVersion -match "^(\d+)\.(\d+)") {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            
            if ($major -eq 1 -and $minor -ge 102) {
                Write-Host "✅ VS Code supports MCP!" -ForegroundColor Green
                $mcpSupported = $true
            } else {
                Write-Host "❌ VS Code version too old for MCP (requires 1.102+)" -ForegroundColor Red
                $mcpSupported = $false
            }
        }
    } catch {
        Write-Host "Error checking VS Code version: $($_.Exception.Message)" -ForegroundColor Red
        $mcpSupported = $false
    }
} else {
    Write-Host "VS Code not found at expected location: $vscodePath" -ForegroundColor Red
    $mcpSupported = $false
}

# Create VS Code alias/function for current session
Write-Host "`nCreating VS Code alias for current session..." -ForegroundColor Yellow
if (Test-Path $vscodePath) {
    # Create function to override 'code' command
    $functionDef = @"
function code {
    & '$vscodePath' @args
}
"@
    
    Invoke-Expression $functionDef
    Write-Host "✅ VS Code alias created for current session" -ForegroundColor Green
    
    # Test the alias
    try {
        $aliasVersion = & code --version 2>$null | Select-Object -First 1
        Write-Host "Alias test - VS Code version: $aliasVersion" -ForegroundColor Green
    } catch {
        Write-Host "Alias test failed" -ForegroundColor Red
    }
} else {
    Write-Host "Cannot create alias - VS Code not found" -ForegroundColor Red
}

# Offer to fix PATH permanently
Write-Host "`nPATH Priority Fix Options:" -ForegroundColor Cyan
Write-Host "1. Current session only (already done above)" -ForegroundColor White
Write-Host "2. Permanent fix (modify user PATH)" -ForegroundColor White
Write-Host "3. Create batch file shortcut" -ForegroundColor White

$choice = Read-Host "`nChoose option (1-3, or Enter to skip)"

switch ($choice) {
    "2" {
        Write-Host "`nModifying user PATH..." -ForegroundColor Yellow
        
        # Get current user PATH
        $userPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        $vscodeBinPath = "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin"
        
        if ($userPath -notlike "*$vscodeBinPath*") {
            # Add VS Code to beginning of user PATH
            $newPath = "$vscodeBinPath;$userPath"
            [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
            Write-Host "✅ VS Code added to user PATH" -ForegroundColor Green
            Write-Host "⚠️  Restart terminal for changes to take effect" -ForegroundColor Yellow
        } else {
            Write-Host "VS Code already in user PATH" -ForegroundColor Yellow
            
            # Check if it's at the beginning
            if ($userPath.StartsWith($vscodeBinPath)) {
                Write-Host "✅ VS Code already has priority in PATH" -ForegroundColor Green
            } else {
                Write-Host "Moving VS Code to beginning of PATH..." -ForegroundColor Yellow
                $pathParts = $userPath -split ";"
                $filteredParts = $pathParts | Where-Object { $_ -ne $vscodeBinPath }
                $newPath = "$vscodeBinPath;" + ($filteredParts -join ";")
                [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
                Write-Host "✅ VS Code moved to beginning of PATH" -ForegroundColor Green
                Write-Host "⚠️  Restart terminal for changes to take effect" -ForegroundColor Yellow
            }
        }
    }
    
    "3" {
        Write-Host "`nCreating VS Code batch shortcut..." -ForegroundColor Yellow
        
        $batchContent = @"
@echo off
"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin\code" %*
"@
        
        $batchPath = "Scripts\vscode.bat"
        $batchContent | Out-File -FilePath $batchPath -Encoding ASCII
        Write-Host "✅ Created batch file: $batchPath" -ForegroundColor Green
        Write-Host "Use 'Scripts\vscode.bat' instead of 'code' command" -ForegroundColor Gray
    }
    
    default {
        Write-Host "Skipping permanent PATH fix" -ForegroundColor Gray
    }
}

# Summary
Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "========" -ForegroundColor Cyan

if ($mcpSupported) {
    Write-Host "✅ VS Code version supports MCP" -ForegroundColor Green
    Write-Host "✅ Ready to proceed with MCP setup" -ForegroundColor Green
    
    Write-Host "`nNext steps:" -ForegroundColor White
    Write-Host "1. Continue with MCP dependency installation" -ForegroundColor Gray
    Write-Host "2. Test MCP servers" -ForegroundColor Gray
    Write-Host "3. Configure VS Code MCP integration" -ForegroundColor Gray
} else {
    Write-Host "❌ VS Code version issue detected" -ForegroundColor Red
    Write-Host "Please ensure VS Code 1.102+ is properly installed" -ForegroundColor Yellow
}

return $mcpSupported
