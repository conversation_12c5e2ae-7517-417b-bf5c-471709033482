# LocalTunnel Integration Validation Script
# This script validates the LocalTunnel setup and OAuth callback functionality

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
param(
    [switch]$TestOAuth = $false,
    [string]$TunnelUrl = "",
    [switch]$Verbose = $false
)

Write-Host "🔍 LocalTunnel Integration Validation" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Function to check if SSH process is running
function Test-LocalTunnelProcess {
    Write-Host "`n📡 Checking LocalTunnel SSH Process..." -ForegroundColor Yellow
    
    try {
        $sshProcesses = Get-Process -Name "ssh" -ErrorAction SilentlyContinue | Where-Object { 
            $_.CommandLine -like "*localhost.run*" 
        }
        
        if ($sshProcesses) {
            Write-Host "✅ LocalTunnel SSH process found" -ForegroundColor Green
            if ($Verbose) {
                $sshProcesses | ForEach-Object {
                    Write-Host "   Process ID: $($_.Id)" -ForegroundColor Gray
                    Write-Host "   Command: $($_.CommandLine)" -ForegroundColor Gray
                }
            }
            return $true
        } else {
            Write-Host "❌ LocalTunnel SSH process not found" -ForegroundColor Red
            Write-Host "💡 Start LocalTunnel with: ssh -R 80:localhost:5678 <EMAIL>" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ Error checking SSH processes: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check if n8n is running
function Test-N8NService {
    Write-Host "`n🐳 Checking N8N Service..." -ForegroundColor Yellow
    
    try {
        # Check if n8n container is running
        $dockerStatus = docker-compose ps n8n 2>$null
        if ($LASTEXITCODE -eq 0 -and $dockerStatus -match "Up") {
            Write-Host "✅ N8N Docker container is running" -ForegroundColor Green
            
            # Test local n8n access
            try {
                Invoke-WebRequest -Uri "http://localhost:5678" -TimeoutSec 5 -ErrorAction Stop | Out-Null
                Write-Host "✅ N8N is accessible on localhost:5678" -ForegroundColor Green
                return $true
            } catch {
                Write-Host "⚠️  N8N container running but not accessible on localhost:5678" -ForegroundColor Yellow
                Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
                return $false
            }
        } else {
            Write-Host "❌ N8N Docker container is not running" -ForegroundColor Red
            Write-Host "💡 Start N8N with: docker-compose up -d n8n" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ Error checking N8N service: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Make sure you're in the n8n-docker directory" -ForegroundColor Yellow
        return $false
    }
}

# Function to validate tunnel URL format
function Test-TunnelUrlFormat {
    param([string]$Url)
    
    Write-Host "`n🔗 Validating Tunnel URL Format..." -ForegroundColor Yellow
    
    if (-not $Url) {
        Write-Host "⚠️  No tunnel URL provided for validation" -ForegroundColor Yellow
        return $false
    }
    
    # Check if URL matches LocalTunnel format
    if ($Url -match "^https://[a-f0-9]+\.lhr\.life$") {
        Write-Host "✅ Tunnel URL format is valid: $Url" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ Invalid tunnel URL format: $Url" -ForegroundColor Red
        Write-Host "💡 Expected format: https://[random-id].lhr.life" -ForegroundColor Yellow
        return $false
    }
}

# Function to test tunnel connectivity
function Test-TunnelConnectivity {
    param([string]$Url)
    
    Write-Host "`n🌐 Testing Tunnel Connectivity..." -ForegroundColor Yellow
    
    if (-not $Url) {
        Write-Host "⚠️  No tunnel URL provided for connectivity test" -ForegroundColor Yellow
        return $false
    }
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -ErrorAction Stop
        Write-Host "✅ Tunnel is accessible: $Url" -ForegroundColor Green
        Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor Gray
        return $true
    } catch {
        Write-Host "❌ Tunnel connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Check if LocalTunnel SSH process is still running" -ForegroundColor Yellow
        return $false
    }
}

# Function to test OAuth callback URL
function Test-OAuthCallback {
    param([string]$Url)
    
    Write-Host "`n🔐 Testing OAuth Callback URL..." -ForegroundColor Yellow
    
    if (-not $Url) {
        Write-Host "⚠️  No tunnel URL provided for OAuth callback test" -ForegroundColor Yellow
        return $false
    }
    
    $callbackUrl = "$Url/rest/oauth2-credential/callback"
    
    try {
        # Test if the OAuth callback endpoint exists
        Invoke-WebRequest -Uri $callbackUrl -TimeoutSec 10 -ErrorAction Stop | Out-Null
        Write-Host "✅ OAuth callback URL is accessible: $callbackUrl" -ForegroundColor Green
        return $true
    } catch {
        if ($_.Exception.Response.StatusCode -eq 404) {
            Write-Host "✅ OAuth callback endpoint exists (404 expected without OAuth flow)" -ForegroundColor Green
            Write-Host "   URL: $callbackUrl" -ForegroundColor Gray
            return $true
        } else {
            Write-Host "❌ OAuth callback test failed: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
}

# Function to check docker-compose configuration
function Test-DockerComposeConfig {
    Write-Host "`n📋 Checking Docker Compose Configuration..." -ForegroundColor Yellow
    
    $dockerComposePath = "n8n-docker\docker-compose.yml"
    
    if (-not (Test-Path $dockerComposePath)) {
        Write-Host "❌ Docker compose file not found: $dockerComposePath" -ForegroundColor Red
        return $false
    }
    
    try {
        $dockerComposeContent = Get-Content $dockerComposePath -Raw
        
        # Check for webhook URL configuration
        if ($dockerComposeContent -match "WEBHOOK_URL=") {
            Write-Host "✅ WEBHOOK_URL configuration found in docker-compose.yml" -ForegroundColor Green
            
            # Extract current webhook URL
            if ($dockerComposeContent -match "WEBHOOK_URL=([^\s]+)") {
                $currentWebhookUrl = $matches[1]
                Write-Host "   Current WEBHOOK_URL: $currentWebhookUrl" -ForegroundColor Gray
                
                if ($currentWebhookUrl -match "\.lhr\.life") {
                    Write-Host "✅ WEBHOOK_URL is configured for LocalTunnel" -ForegroundColor Green
                } else {
                    Write-Host "⚠️  WEBHOOK_URL may need updating for current tunnel" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "⚠️  WEBHOOK_URL not found in docker-compose.yml" -ForegroundColor Yellow
        }
        
        # Check for editor base URL
        if ($dockerComposeContent -match "N8N_EDITOR_BASE_URL=") {
            Write-Host "✅ N8N_EDITOR_BASE_URL configuration found" -ForegroundColor Green
        } else {
            Write-Host "⚠️  N8N_EDITOR_BASE_URL not found in docker-compose.yml" -ForegroundColor Yellow
        }
        
        return $true
    } catch {
        Write-Host "❌ Error reading docker-compose.yml: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main validation logic
Write-Host "`n🚀 Starting LocalTunnel Integration Validation..." -ForegroundColor Green

$results = @{
    SSHProcess = Test-LocalTunnelProcess
    N8NService = Test-N8NService
    DockerConfig = Test-DockerComposeConfig
}

# If tunnel URL provided, test it
if ($TunnelUrl) {
    $results.TunnelFormat = Test-TunnelUrlFormat -Url $TunnelUrl
    $results.TunnelConnectivity = Test-TunnelConnectivity -Url $TunnelUrl
    
    if ($TestOAuth) {
        $results.OAuthCallback = Test-OAuthCallback -Url $TunnelUrl
    }
} else {
    Write-Host "`n⚠️  No tunnel URL provided. Skipping URL-specific tests." -ForegroundColor Yellow
    Write-Host "💡 Run with -TunnelUrl parameter to test specific tunnel" -ForegroundColor Cyan
}

# Summary
Write-Host "`n📊 Validation Summary" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

$passedTests = 0
$totalTests = 0

foreach ($test in $results.GetEnumerator()) {
    $totalTests++
    if ($test.Value) {
        Write-Host "✅ $($test.Key): PASSED" -ForegroundColor Green
        $passedTests++
    } else {
        Write-Host "❌ $($test.Key): FAILED" -ForegroundColor Red
    }
}

Write-Host "`nOverall Result: $passedTests/$totalTests tests passed" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

# Recommendations
Write-Host "`n💡 Recommendations:" -ForegroundColor Cyan

if (-not $results.SSHProcess) {
    Write-Host "• Start LocalTunnel: ssh -R 80:localhost:5678 <EMAIL>" -ForegroundColor White
}

if (-not $results.N8NService) {
    Write-Host "• Start N8N service: cd n8n-docker && docker-compose up -d n8n" -ForegroundColor White
}

if ($TunnelUrl -and (-not $results.TunnelConnectivity)) {
    Write-Host "• Check if SSH tunnel is still active" -ForegroundColor White
    Write-Host "• Restart tunnel if connection dropped" -ForegroundColor White
}

if (-not $TunnelUrl) {
    Write-Host "• Get tunnel URL from SSH terminal output" -ForegroundColor White
    Write-Host "• Update docker-compose.yml with current tunnel URL" -ForegroundColor White
    Write-Host "• Restart n8n container after updating configuration" -ForegroundColor White
}

Write-Host "`n🔗 Useful Commands:" -ForegroundColor Blue
Write-Host "• Start tunnel: ssh -R 80:localhost:5678 <EMAIL>" -ForegroundColor White
Write-Host "• Check processes: Get-Process -Name ssh" -ForegroundColor White
Write-Host "• Test local n8n: curl http://localhost:5678" -ForegroundColor White
Write-Host "• Update config: Edit n8n-docker\docker-compose.yml" -ForegroundColor White
Write-Host "• Restart n8n: docker-compose stop n8n && docker-compose up -d n8n" -ForegroundColor White

Write-Host "`n📚 Documentation:" -ForegroundColor Blue
Write-Host "• Setup Guide: Documentation\ReadMe_TunnelSetup.md" -ForegroundColor White
Write-Host "• Blogger Setup: n8n-docker\scripts\setup-blogger-credentials.ps1" -ForegroundColor White

if ($passedTests -eq $totalTests) {
    Write-Host "`n🎉 All tests passed! LocalTunnel integration is working correctly." -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some tests failed. Please address the issues above." -ForegroundColor Yellow
}
