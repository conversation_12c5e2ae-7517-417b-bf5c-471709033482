{"name": "ElthosRPG_Blog_Twitter", "nodes": [{"parameters": {"url": "={{ $json.blogURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [-1920, 640], "id": "cbb0eee5-417c-4f80-9f8b-2a196aa18a79"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2280, 640], "id": "8c6d047c-d72b-40f6-a219-a940651e2fae", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"promptType": "define", "text": "={{ $json.firstParagraph }}", "messages": {"messageValues": [{"message": "You are a twitter expert who can take the best content from the blog entry and make the perfect tweet from it that is no more than 60 characters long. Use Text format. Include hash tags #IndieRPG #Elthos #TTRPG. Be concise and engaging."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-1400, 600], "id": "32909486-f58b-412a-bdcf-f7fa8119962f", "name": "Basic LLM Chain", "alwaysOutputData": false}, {"parameters": {"model": {"__rl": true, "value": "deepseek-r1-distill-llama-8b", "mode": "list", "cachedResultName": "deepseek-r1-distill-llama-8b"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1320, 820], "id": "8fc87bd3-3751-4e7a-81e2-6a3fe3e13f4b", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "cqdpm9ID0q2zjSkV", "name": "LM Studio"}}}, {"parameters": {"operation": "extract-content", "html": "={{ $('Fetch Post').item.json.data }}", "outputMarkdown": true}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [-1760, 580], "id": "0c4939c9-b550-4cc3-84ca-e153628900a7", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [-900, 600], "id": "5a8f4dd2-37cf-4a56-a8d1-2ef60887c738", "name": "Create Tweet", "credentials": {"twitterOAuth2Api": {"id": "QMuVcnYLarrzm0jp", "name": "X account"}}}, {"parameters": {"jsCode": "// Debug via return data - see what ScrapeNinja actually passes through\nconst debugInfo = {\n    inputFields: Object.keys($input.first().json),\n    blogURLFromInput: $input.first().json.blogURL,\n    allInputData: $input.first().json\n};\n\n// Replace \"content\" with the actual field name from ScrapeNinja output\nconst content = $input.first().json.content;\n\n// Split into paragraphs by double line breaks\nconst paragraphs = content.split(/\\n\\s*\\n/);\n\n// Get the first paragraph\nlet firstParagraph = paragraphs[2] || \"\";\n\n// Limit to 3000 words\nconst words = firstParagraph.split(/\\s+/).slice(0, 3000);\nconst limitedParagraph = words.join(\" \");\n\n// Try to get blogURL from different sources\nlet blogURL = $input.first().json.blogURL; // From ScrapeNinja input\n\n// If not available, try to get from the Set node directly\nif (!blogURL) {\n    try {\n        blogURL = $node[\"Edit Field - blogURL\"].json.blogURL;\n    } catch (e) {\n        blogURL = \"URL_NOT_FOUND\";\n    }\n}\n\nreturn [{ json: { \n    firstParagraph: limitedParagraph,\n    blogURL: blogURL,\n    debug: debugInfo\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1580, 640], "id": "b6311eac-659d-4e8f-9484-5401c4d61165", "name": "Code - Get Paragraph 1", "notesInFlow": false}, {"parameters": {"jsCode": "// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// DEBUG: Let's see what's actually available\nconsole.log('=== DEBUG: Available input data ===');\nconsole.log('ALL FIELDS:', Object.keys($input.first().json));\nconsole.log('FULL INPUT:', JSON.stringify($input.first().json, null, 2));\nconsole.log('blogURL value:', $input.first().json.blogURL);\nconsole.log('blogURL type:', typeof $input.first().json.blogURL);\n\n// Stop here for debugging\nreturn [{ json: { \n    debug: 'Check console for available fields',\n    availableFields: Object.keys($input.first().json),\n    blogURLValue: $input.first().json.blogURL\n}}];\n\n\n// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// Get the blog post URL (now preserved through the flow)\nconst blogUrl = $input.first().json.blogURL;\n\n// Remove <think>...</think> and everything in between\nconst cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Clean whitespace\ntext = cleaned.replace(/^\\s+|\\s+$/g, '');\n\n// Twitter character limit\nconst TARGET_LIMIT = 120;\n\n// Calculate available space for text (limit - URL length - 1 space)\nconst availableSpace = TARGET_LIMIT - blogUrl.length - 1;\n\n// Handle different URL length scenarios\nif (availableSpace < 20) {\n    text = text.substring(0, 15) + '...';\n} else if (availableSpace < 40) {\n    text = text.substring(0, availableSpace - 5) + '...';\n} else if (text.length > availableSpace) {\n    text = text.substring(0, availableSpace - 3) + '...';\n}\n\n// Append URL with space\nconst finalTweet = text + ' ' + blogUrl;\n\n// Safety check\nif (finalTweet.length > TARGET_LIMIT) {\n    const maxTextLength = TARGET_LIMIT - blogUrl.length - 1 - 3;\n    text = text.substring(0, maxTextLength) + '...';\n    const safeTweet = text + ' ' + blogUrl;\n    return [{ json: { text: safeTweet } }];\n}\n\nreturn [{ json: { text: finalTweet } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1080, 660], "id": "2435ef8b-8f24-4e37-9dcc-865ae42ded0c", "name": "Code - Clean for Tweet"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "blogURL", "value": "=https://elthosrpg.blogspot.com/2022/10/woaf-game-session-39.html", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-2100, 600], "id": "27991487-2290-4f63-8a0c-c06a876c1b02", "name": "Edit Field - blogURL"}], "pinData": {}, "connections": {"Fetch Post": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "Edit Field - blogURL", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Code - Clean for Tweet", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "ScrapeNinja": {"main": [[{"node": "Code - Get Paragraph 1", "type": "main", "index": 0}]]}, "Code - Get Paragraph 1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Code - Clean for Tweet": {"main": [[{"node": "Create Tweet", "type": "main", "index": 0}]]}, "Edit Field - blogURL": {"main": [[{"node": "Fetch Post", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "368a2e80-739e-443e-be56-1ee2950b5f12", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "M4PfByDgjUvSFPqZ", "tags": []}