#!/usr/bin/env python3
"""
Restart N8N with Clean Logs
This script restarts n8n and shows clean logs without telemetry noise
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(cmd, cwd=None, capture_output=False):
    """Run a command and return the result"""
    try:
        if capture_output:
            result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
            return result.returncode == 0, result.stdout, result.stderr
        else:
            result = subprocess.run(cmd, shell=True, cwd=cwd)
            return result.returncode == 0, "", ""
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False, "", str(e)

def print_header(text, color=""):
    """Print a formatted header"""
    print(f"\n{text}")
    print("=" * len(text))

def main():
    print("🔄 Restarting N8N with Updated Configuration...")
    print("=" * 50)
    
    # Find n8n-docker directory
    script_dir = Path(__file__).parent.parent
    n8n_docker_path = script_dir / "n8n-docker"
    
    if not n8n_docker_path.exists():
        print(f"❌ ERROR: n8n-docker directory not found at: {n8n_docker_path}")
        sys.exit(1)
    
    print(f"📁 Working directory: {n8n_docker_path}")
    
    # Check if Docker is running
    print("🐳 Checking Docker...")
    success, _, _ = run_command("docker info", capture_output=True)
    if not success:
        print("❌ ERROR: Docker is not running. Please start Docker Desktop.")
        sys.exit(1)
    print("✅ Docker is running")
    
    # Stop current containers
    print("\n🛑 Stopping current n8n containers...")
    success, _, _ = run_command("docker-compose down", cwd=n8n_docker_path)
    if success:
        print("✅ Containers stopped")
    else:
        print("⚠️ Warning: Error stopping containers (may not be running)")
    
    # Start containers with updated configuration
    print("\n🚀 Starting n8n with updated configuration...")
    success, _, _ = run_command("docker-compose up -d", cwd=n8n_docker_path)
    if not success:
        print("❌ ERROR: Failed to start containers")
        sys.exit(1)
    print("✅ Containers started")
    
    # Wait for startup
    print("\n⏳ Waiting for n8n to start...")
    time.sleep(10)
    
    # Show status
    print_header("📊 Container Status:")
    run_command("docker-compose ps", cwd=n8n_docker_path)
    
    # Show recent logs (filtered for important messages)
    print_header("📋 Recent Logs (filtered):")
    success, stdout, _ = run_command("docker logs n8n --tail 20", capture_output=True)
    
    if success and stdout:
        # Filter out noise from logs
        filtered_lines = []
        for line in stdout.split('\n'):
            if not any(noise in line for noise in [
                "Rudder", "ECONNREFUSED", "typescript", "devDependency"
            ]):
                filtered_lines.append(line)
        
        for line in filtered_lines[-15:]:  # Show last 15 filtered lines
            if line.strip():
                print(line)
    
    print("\n✅ N8N Restart Complete!")
    print("🌐 Access n8n at: http://localhost:5678")
    print("🔗 Stable URL: http://localhost:8080")
    print("\n💡 To view live logs: docker logs n8n -f")

if __name__ == "__main__":
    main()
