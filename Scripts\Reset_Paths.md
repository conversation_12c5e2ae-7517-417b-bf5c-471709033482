# Script Path Analysis Report

Analysis of Scripts folder to identify files that need path adjustments
after being moved from root to Scripts subfolder.

## Summary
- **Total Scripts Analyzed**: 76
- **Scripts Needing Updates**: 60
- **Total Path Issues Found**: 300

## Scripts Requiring Path Updates

### Scripts\analyze_documentation.py
**File Type**: .py
**Issues Found**: 1

**project_folder_reference (Documentation)**:
- Line 235: `"data/documentation_analysis_report.md"`
  ```
  output_file = "data/documentation_analysis_report.md"
  ```

### Scripts\analyze_project_files.py
**File Type**: .py
**Issues Found**: 12

**root_file_reference (run.py)**:
- Line 38: `'run.py'`
  ```
  'run.py',
  ```

**project_folder_reference (n8n_builder)**:
- Line 40: `'n8n_builder/app.py'`
  ```
  'n8n_builder/app.py',
  ```
- Line 189: `"n8n_builder/{imp.replace('`
  ```
  f"n8n_builder/{imp.replace('.', '/')}.py"
  ```
- Line 271: `"n8n_builder/{imp.replace('`
  ```
  f"n8n_builder/{imp.replace('.', '/')}.py"
  ```
- Line 320: `'n8n_builder/settings.py'`
  ```
  'n8n_builder/settings.py',
  ```
- Line 321: `'n8n_builder/__init__.py'`
  ```
  'n8n_builder/__init__.py',
  ```

**root_file_reference (setup.py)**:
- Line 42: `'setup.py'`
  ```
  'setup.py'
  ```
- Line 288: `'setup.py'`
  ```
  'setup.py',  # Installation script
  ```

**root_file_reference (requirements.txt)**:
- Line 289: `'requirements.txt'`
  ```
  'requirements.txt',  # Dependencies
  ```

**root_file_reference (README.md)**:
- Line 290: `'README.md'`
  ```
  'README.md',  # Documentation
  ```

**project_folder_reference (Documentation)**:
- Line 291: `'Documentation/'`
  ```
  'Documentation/',  # Documentation files
  ```

**project_folder_reference (config)**:
- Line 319: `'n8n_builder/config.py'`
  ```
  'n8n_builder/config.py',
  ```

### Scripts\analyze_script_paths.py
**File Type**: .py
**Issues Found**: 7

**root_file_reference (README.md)**:
- Line 32: `'README.md'`
  ```
  'README.md', 'requirements.txt', 'setup.py', 'run.py',
  ```

**root_file_reference (requirements.txt)**:
- Line 32: `'requirements.txt'`
  ```
  'README.md', 'requirements.txt', 'setup.py', 'run.py',
  ```

**root_file_reference (setup.py)**:
- Line 32: `'setup.py'`
  ```
  'README.md', 'requirements.txt', 'setup.py', 'run.py',
  ```

**root_file_reference (run.py)**:
- Line 32: `'run.py'`
  ```
  'README.md', 'requirements.txt', 'setup.py', 'run.py',
  ```

**root_file_reference (.gitignore)**:
- Line 33: `'.gitignore'`
  ```
  '.gitignore', 'docker-compose.yml', 'Dockerfile'
  ```

**root_file_reference (docker-compose.yml)**:
- Line 33: `'docker-compose.yml'`
  ```
  '.gitignore', 'docker-compose.yml', 'Dockerfile'
  ```

**root_file_reference (Dockerfile)**:
- Line 33: `'Dockerfile'`
  ```
  '.gitignore', 'docker-compose.yml', 'Dockerfile'
  ```

### Scripts\analyze_venv_files.py
**File Type**: .py
**Issues Found**: 3

**potential_path_issue**:
- Line 14: `Path("venv"`
  ```
  venv_path = Path("venv")
  ```

**root_file_reference (requirements.txt)**:
- Line 166: `"requirements.txt"`
  ```
  requirements_file = Path("requirements.txt")
  ```
- Line 166: `Path("requirements.txt"`
  ```
  requirements_file = Path("requirements.txt")
  ```

### Scripts\analyze_workspace_folders.py
**File Type**: .py
**Issues Found**: 10

**potential_path_issue**:
- Line 21: `Path(".."`
  ```
  self.workspace_root = Path("..").resolve()
  ```
- Line 22: `Path("."`
  ```
  self.current_project = Path(".").resolve()
  ```

**root_file_reference (README.md)**:
- Line 58: `"README.md"`
  ```
  "README.md": (folder_path / "README.md").exists(),
  ```
- Line 58: `"README.md"`
  ```
  "README.md": (folder_path / "README.md").exists(),
  ```

**root_file_reference (requirements.txt)**:
- Line 59: `"requirements.txt"`
  ```
  "requirements.txt": (folder_path / "requirements.txt").exists(),
  ```
- Line 59: `"requirements.txt"`
  ```
  "requirements.txt": (folder_path / "requirements.txt").exists(),
  ```

**root_file_reference (run.py)**:
- Line 60: `"run.py"`
  ```
  "run.py": (folder_path / "run.py").exists(),
  ```
- Line 60: `"run.py"`
  ```
  "run.py": (folder_path / "run.py").exists(),
  ```

**root_file_reference (setup.py)**:
- Line 61: `"setup.py"`
  ```
  "setup.py": (folder_path / "setup.py").exists(),
  ```
- Line 61: `"setup.py"`
  ```
  "setup.py": (folder_path / "setup.py").exists(),
  ```

### Scripts\auto-commit-clean.ps1
**File Type**: .ps1
**Issues Found**: 8

**potential_path_issue**:
- Line 4: `$CommitMessage = ""`
  ```
  [string]$CommitMessage = "",
  ```
- Line 5: `$CommunityMessage = ""`
  ```
  [string]$CommunityMessage = "",
  ```
- Line 35: `$defaultMessage = ""`
  ```
  $defaultMessage = ""
  ```
- Line 43: `$defaultMessage = "Update core functionality"`
  ```
  else { $defaultMessage = "Update core functionality" }
  ```
- Line 49: `$defaultMessage = "Update project files"`
  ```
  if (-not $defaultMessage) { $defaultMessage = "Update project files" }
  ```
- Line 126: `Test-Path "sync-public.ps1"`
  ```
  if (Test-Path "sync-public.ps1") {
  ```

**project_folder_reference (Scripts)**:
- Line 39: `$defaultMessage = "Update scripts"`
  ```
  else { $defaultMessage = "Update scripts" }
  ```

**project_folder_reference (config)**:
- Line 47: `$defaultMessage = "Update configuration"`
  ```
  else { $defaultMessage = "Update configuration" }
  ```

### Scripts\clean_commit_messages.py
**File Type**: .py
**Issues Found**: 1

**project_folder_reference (Scripts)**:
- Line 197: `"Scripts/commit_analysis.json"`
  ```
  analysis_path = "Scripts/commit_analysis.json"
  ```

### Scripts\cleanup-root-folder.ps1
**File Type**: .ps1
**Issues Found**: 5

**potential_path_issue**:
- Line 13: `$LogFile = "cleanup-root-folder.log"`
  ```
  $LogFile = "cleanup-root-folder.log"
  ```
- Line 16: `$Level = "INFO"`
  ```
  param([string]$Message, [string]$Level = "INFO")
  ```
- Line 18: `$logEntry = "[$timestamp] [$Level] $Message"`
  ```
  $logEntry = "[$timestamp] [$Level] $Message"
  ```
- Line 171: `Test-Path "Self-Healer"`
  ```
  if (Test-Path "Self-Healer") {
  ```

**project_folder_reference (Self_Healer)**:
- Line 138: `Test-Path "Self_Healer"`
  ```
  if (-not (Test-Path "Self_Healer")) {
  ```

### Scripts\cleanup_markdown_refs.ps1
**File Type**: .ps1
**Issues Found**: 2

**project_folder_reference (KnowledgeBase)**:
- Line 12: `Get-Content 'projects\knowledgebase1\README.md'`
  ```
  $content = Get-Content 'projects\knowledgebase1\README.md' -Raw
  ```
- Line 19: `Set-Content 'projects\knowledgebase1\README.md'`
  ```
  $content | Set-Content 'projects\knowledgebase1\README.md' -NoNewline
  ```

### Scripts\commit-and-sync-community.ps1
**File Type**: .ps1
**Issues Found**: 7

**potential_path_issue**:
- Line 5: `$CommitMessage = ""`
  ```
  [string]$CommitMessage = "",
  ```
- Line 6: `$CommunityMessage = ""`
  ```
  [string]$CommunityMessage = "",
  ```
- Line 11: `$ErrorActionPreference = "Continue"`
  ```
  $ErrorActionPreference = "Continue"
  ```
- Line 16: `$Color = "White"`
  ```
  [string]$Color = "White"
  ```
- Line 112: `Test-Path "sync-public.ps1"`
  ```
  if (-not (Test-Path "sync-public.ps1")) {
  ```

**project_folder_reference (n8n_builder)**:
- Line 52: `Test-Path "n8n_builder"`
  ```
  if (-not (Test-Path "n8n_builder") -or -not (Test-Path "Scripts")) {
  ```

**project_folder_reference (Scripts)**:
- Line 52: `Test-Path "Scripts"`
  ```
  if (-not (Test-Path "n8n_builder") -or -not (Test-Path "Scripts")) {
  ```

### Scripts\commit-and-sync-simple.ps1
**File Type**: .ps1
**Issues Found**: 2

**potential_path_issue**:
- Line 4: `$CommitMessage = ""`
  ```
  [string]$CommitMessage = "",
  ```
- Line 5: `$CommunityMessage = ""`
  ```
  [string]$CommunityMessage = ""
  ```

### Scripts\compare_readme_files.py
**File Type**: .py
**Issues Found**: 3

**root_file_reference (README.md)**:
- Line 21: `"README.md"`
  ```
  self.main_readme = Path("README.md")
  ```
- Line 21: `Path("README.md"`
  ```
  self.main_readme = Path("README.md")
  ```

**potential_path_issue**:
- Line 22: `Path("README_community.md"`
  ```
  self.community_readme = Path("README_community.md")
  ```

### Scripts\comprehensive-audit.ps1
**File Type**: .ps1
**Issues Found**: 3

**potential_path_issue**:
- Line 9: `$RepositoryPath = "."`
  ```
  [string]$RepositoryPath = ".",
  ```
- Line 10: `$OutputFile = "private-component-audit.json"`
  ```
  [string]$OutputFile = "private-component-audit.json"
  ```
- Line 67: `$Level = "INFO"`
  ```
  param([string]$Message, [string]$Level = "INFO")
  ```

### Scripts\comprehensive_repo_scan.py
**File Type**: .py
**Issues Found**: 1

**project_folder_reference (config)**:
- Line 146: `"Scripts/public_repo_config.json"`
  ```
  "sync-public.ps1", "Scripts/public_repo_config.json",
  ```

### Scripts\consolidate-self-healer.ps1
**File Type**: .ps1
**Issues Found**: 4

**potential_path_issue**:
- Line 13: `$Level = "INFO"`
  ```
  param([string]$Message, [string]$Level = "INFO")
  ```
- Line 30: `$hyphenDir = "Self-Healer"`
  ```
  $hyphenDir = "Self-Healer"
  ```

**project_folder_reference (Self_Healer)**:
- Line 31: `$underscoreDir = "Self_Healer"`
  ```
  $underscoreDir = "Self_Healer"
  ```
- Line 39: `"Self_Healer/ directory not found - creating it"`
  ```
  Write-ConsolidateLog "Self_Healer/ directory not found - creating it" "WARN"
  ```

### Scripts\create_documentation_consolidation_plan.py
**File Type**: .py
**Issues Found**: 8

**root_file_reference (README.md)**:
- Line 35: `'README.md'`
  ```
  'files': ['README.md', 'GETTING_STARTED.md', 'FEATURES.md'],
  ```
- Line 48: `'README.md'`
  ```
  'files': ['README.md'],
  ```
- Line 52: `'README.md'`
  ```
  'files': ['README.md', 'SETUP.md'],
  ```

**project_folder_reference (Documentation)**:
- Line 190: `'Documentation/Architecture.md'`
  ```
  'target_file': 'Documentation/Architecture.md',
  ```
- Line 202: `'Documentation/guides/Troubleshooting.md'`
  ```
  'target_file': 'Documentation/guides/Troubleshooting.md',
  ```
- Line 203: `'Documentation/guides'`
  ```
  'target_location': 'Documentation/guides',
  ```
- Line 309: `"data/documentation_analysis_report.md"`
  ```
  analysis_report = "data/documentation_analysis_report.md"
  ```
- Line 310: `"data/documentation_consolidation_plan.json"`
  ```
  output_file = "data/documentation_consolidation_plan.json"
  ```

### Scripts\debug_analyze_files.py
**File Type**: .py
**Issues Found**: 3

**root_file_reference (README.md)**:
- Line 21: `'README.md'`
  ```
  expected_files = ['README.md', 'requirements.txt', 'setup.py']
  ```

**root_file_reference (requirements.txt)**:
- Line 21: `'requirements.txt'`
  ```
  expected_files = ['README.md', 'requirements.txt', 'setup.py']
  ```

**root_file_reference (setup.py)**:
- Line 21: `'setup.py'`
  ```
  expected_files = ['README.md', 'requirements.txt', 'setup.py']
  ```

### Scripts\delete_obsolete_files.py
**File Type**: .py
**Issues Found**: 7

**project_folder_reference (data)**:
- Line 20: `"data/streamlined_cleanup_analysis.json"`
  ```
  analysis_file = "data/streamlined_cleanup_analysis.json"
  ```
- Line 92: `"data/file_deletion_report.md"`
  ```
  report_file = "data/file_deletion_report.md"
  ```

**project_folder_reference (Documentation)**:
- Line 60: `"Documentation/api"`
  ```
  "Documentation/api",
  ```
- Line 61: `"Documentation/guides"`
  ```
  "Documentation/guides",
  ```
- Line 62: `"Documentation/technical"`
  ```
  "Documentation/technical",
  ```
- Line 71: `"Self_Healer/Documentation/DB_Admin"`
  ```
  "Self_Healer/Documentation/DB_Admin",
  ```
- Line 72: `"Self_Healer/Documentation"`
  ```
  "Self_Healer/Documentation"
  ```

### Scripts\deploy_public.ps1
**File Type**: .ps1
**Issues Found**: 16

**project_folder_reference (n8n_builder)**:
- Line 7: `$OutputDir = "N8N_Builder_Community"`
  ```
  [string]$OutputDir = "N8N_Builder_Community"
  ```
- Line 22: `"n8n_builder/"`
  ```
  "n8n_builder/",
  ```

**project_folder_reference (Documentation)**:
- Line 23: `"Documentation/"`
  ```
  "Documentation/",
  ```

**project_folder_reference (Scripts)**:
- Line 24: `"Scripts/"`
  ```
  "Scripts/",
  ```

**project_folder_reference (tests)**:
- Line 25: `"tests/"`
  ```
  "tests/",
  ```

**project_folder_reference (Self_Healer)**:
- Line 42: `"Self_Healer/"`
  ```
  "Self_Healer/",
  ```

**project_folder_reference (KnowledgeBase)**:
- Line 44: `"KnowledgeBase/"`
  ```
  "KnowledgeBase/",
  ```

**root_file_reference (run.py)**:
- Line 52: `"run.py"`
  ```
  "run.py",  # Use run_public.py instead
  ```
- Line 143: `"run.py"`
  ```
  "run_public.py" = "run.py"
  ```

**root_file_reference (requirements.txt)**:
- Line 53: `"requirements.txt"`
  ```
  "requirements.txt",  # Use requirements_public.txt instead
  ```
- Line 144: `"requirements.txt"`
  ```
  "requirements_public.txt" = "requirements.txt"
  ```

**root_file_reference (README.md)**:
- Line 54: `"README.md"`
  ```
  "README.md",  # Use README_public.md instead
  ```
- Line 145: `"README.md"`
  ```
  "README_public.md" = "README.md"
  ```

**root_file_reference (setup.py)**:
- Line 55: `"setup.py"`
  ```
  "setup.py",  # Use setup_public.py instead
  ```
- Line 146: `"setup.py"`
  ```
  "setup_public.py" = "setup.py"
  ```

**root_file_reference (.gitignore)**:
- Line 148: `".gitignore"`
  ```
  ".gitignore_public" = ".gitignore"
  ```

### Scripts\detect-private-components.ps1
**File Type**: .ps1
**Issues Found**: 5

**potential_path_issue**:
- Line 11: `$Path = "."`
  ```
  [string]$Path = ".",
  ```
- Line 12: `$OutputFile = "private-component-detection-report.json"`
  ```
  [string]$OutputFile = "private-component-detection-report.json",
  ```
- Line 118: `$Level = "INFO"`
  ```
  param([string]$Message, [string]$Level = "INFO")
  ```
- Line 120: `$logEntry = "[$timestamp] [$Level] $Message"`
  ```
  $logEntry = "[$timestamp] [$Level] $Message"
  ```
- Line 246: `$markdown = "# Private Component Detection Report`n`n"`
  ```
  $markdown = "# Private Component Detection Report`n`n"
  ```

### Scripts\dev_publish.py
**File Type**: .py
**Issues Found**: 7

**project_folder_reference (n8n_builder)**:
- Line 24: `Path("../N8N_Builder_Community"`
  ```
  self.public_repo_path = Path("../N8N_Builder_Community")
  ```

**project_folder_reference (config)**:
- Line 31: `"Scripts/dev_config.json"`
  ```
  config_file = Path("Scripts/dev_config.json")
  ```
- Line 31: `Path("Scripts/dev_config.json"`
  ```
  config_file = Path("Scripts/dev_config.json")
  ```

**potential_path_issue**:
- Line 174: `Path("final_verification.json"`
  ```
  if success and Path("final_verification.json").exists():
  ```
- Line 176: `open("final_verification.json"`
  ```
  with open("final_verification.json", 'r') as f:
  ```
- Line 176: `with open("final_verification.json"`
  ```
  with open("final_verification.json", 'r') as f:
  ```
- Line 184: `Path("final_verification.json"`
  ```
  Path("final_verification.json").unlink()
  ```

### Scripts\direct_commit_cleanup.ps1
**File Type**: .ps1
**Issues Found**: 2

**potential_path_issue**:
- Line 8: `$backupBranch = "backup-$(Get-Date -Format '`
  ```
  $backupBranch = "backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
  ```
- Line 53: `$filterFile = "temp_filter.ps1"`
  ```
  $filterFile = "temp_filter.ps1"
  ```

### Scripts\execute_separation.py
**File Type**: .py
**Issues Found**: 10

**potential_path_issue**:
- Line 128: `Path("separation_detection.json"`
  ```
  if success and Path("separation_detection.json").exists():
  ```
- Line 130: `open("separation_detection.json"`
  ```
  with open("separation_detection.json", 'r') as f:
  ```
- Line 130: `with open("separation_detection.json"`
  ```
  with open("separation_detection.json", 'r') as f:
  ```
- Line 216: `Path("post_sync_detection.json"`
  ```
  if success and Path("post_sync_detection.json").exists():
  ```
- Line 218: `open("post_sync_detection.json"`
  ```
  with open("post_sync_detection.json", 'r') as f:
  ```
- Line 218: `with open("post_sync_detection.json"`
  ```
  with open("post_sync_detection.json", 'r') as f:
  ```

**root_file_reference (README.md)**:
- Line 266: `"README.md"`
  ```
  required_files = ["README.md", "requirements.txt", "run.py"]
  ```

**root_file_reference (requirements.txt)**:
- Line 266: `"requirements.txt"`
  ```
  required_files = ["README.md", "requirements.txt", "run.py"]
  ```

**root_file_reference (run.py)**:
- Line 266: `"run.py"`
  ```
  required_files = ["README.md", "requirements.txt", "run.py"]
  ```
- Line 286: `"run.py"`
  ```
  run_py = self.public_repo_path / "run.py"
  ```

### Scripts\final_documentation_validation.py
**File Type**: .py
**Issues Found**: 8

**root_file_reference (README.md)**:
- Line 37: `"README.md"`
  ```
  "README.md",
  ```
- Line 88: `"README.md"`
  ```
  ("README.md", "Has quick start table", r"Time Available.*Start Here"),
  ```
- Line 184: `"README.md"`
  ```
  "README.md",
  ```

**project_folder_reference (Documentation)**:
- Line 40: `"Documentation/ReadMe_TunnelSetup.md"`
  ```
  "Documentation/ReadMe_TunnelSetup.md"
  ```
- Line 90: `"Documentation/DEVELOPER_QUICK_REFERENCE.md"`
  ```
  ("Documentation/DEVELOPER_QUICK_REFERENCE.md", "Quick reference exists", r"Essential Commands"),
  ```
- Line 186: `"Documentation/DEVELOPER_QUICK_REFERENCE.md"`
  ```
  "Documentation/DEVELOPER_QUICK_REFERENCE.md",
  ```
- Line 311: `"data/final_documentation_validation.json"`
  ```
  results_path = Path("data/final_documentation_validation.json")
  ```
- Line 311: `Path("data/final_documentation_validation.json"`
  ```
  results_path = Path("data/final_documentation_validation.json")
  ```

### Scripts\fix_analyze_files.py
**File Type**: .py
**Issues Found**: 1

**potential_path_issue**:
- Line 246: `Path("safe_analyze_project_files.py"`
  ```
  safe_script_path = Path("safe_analyze_project_files.py")
  ```

### Scripts\fix_script_references.py
**File Type**: .py
**Issues Found**: 9

**project_folder_reference (Scripts)**:
- Line 24: `Path("Scripts"`
  ```
  self.scripts_dir = Path("Scripts")
  ```
- Line 34: `"Scripts/script.py"`
  ```
  r'"Scripts[/\\]([a-zA-Z0-9_\-\.]+\.(?:py|ps1|bat|sh))"',  # "Scripts/script.py"
  ```
- Line 35: `'Scripts/script.py'`
  ```
  r"'Scripts[/\\]([a-zA-Z0-9_\-\.]+\.(?:py|ps1|bat|sh))'",  # 'Scripts/script.py'
  ```
- Line 91: `'Scripts/{script_name}'`
  ```
  replacement = full_match.replace(f'Scripts{os.sep}{script_name}', script_name).replace(f'Scripts/{script_name}', script_name)
  ```

**potential_path_issue**:
- Line 25: `Path("."`
  ```
  self.project_root = Path(".")
  ```
- Line 124: `Path(".."`
  ```
  (r'Path\("\."\)', 'Path("..")'),  # Python Path(".") should be Path("..")
  ```
- Line 124: `Path("."`
  ```
  (r'Path\("\."\)', 'Path("..")'),  # Python Path(".") should be Path("..")
  ```
- Line 124: `Path(".."`
  ```
  (r'Path\("\."\)', 'Path("..")'),  # Python Path(".") should be Path("..")
  ```
- Line 125: `Path(".."`
  ```
  (r'os\.getcwd\(\)', 'Path("..").resolve()'),  # getcwd() might need adjustment
  ```

### Scripts\generate_consolidation_report.py
**File Type**: .py
**Issues Found**: 2

**project_folder_reference (Documentation)**:
- Line 157: `"data/documentation_consolidation_plan.json"`
  ```
  json_file = "data/documentation_consolidation_plan.json"
  ```
- Line 158: `"data/Documentation_Consolidation_Report.md"`
  ```
  output_file = "data/Documentation_Consolidation_Report.md"
  ```

### Scripts\get-blogger-blog-id.ps1
**File Type**: .ps1
**Issues Found**: 1

**potential_path_issue**:
- Line 6: `$NgrokUrl = ""`
  ```
  [string]$NgrokUrl = ""
  ```

### Scripts\github_repository_setup.py
**File Type**: .py
**Issues Found**: 6

**project_folder_reference (config)**:
- Line 42: `"Scripts/public_repo_config.json"`
  ```
  config_file = Path("Scripts/public_repo_config.json")
  ```
- Line 42: `Path("Scripts/public_repo_config.json"`
  ```
  config_file = Path("Scripts/public_repo_config.json")
  ```

**potential_path_issue**:
- Line 88: `Path(".github"`
  ```
  github_dir = Path(".github")
  ```
- Line 262: `Path(".github"`
  ```
  github_dir = Path(".github")
  ```
- Line 475: `Path("CONTRIBUTING.md"`
  ```
  file_path = Path("CONTRIBUTING.md")
  ```
- Line 615: `Path(".github/workflows"`
  ```
  workflows_dir = Path(".github/workflows")
  ```

### Scripts\improve_documentation.py
**File Type**: .py
**Issues Found**: 7

**project_folder_reference (Documentation)**:
- Line 35: `"data/documentation_analysis_report.md"`
  ```
  "data/documentation_analysis_report.md",
  ```
- Line 375: `"Documentation/DEVELOPER_QUICK_REFERENCE.md"`
  ```
  quick_ref_path = self.project_root / "Documentation/DEVELOPER_QUICK_REFERENCE.md"
  ```

**project_folder_reference (data)**:
- Line 36: `"data/file_deletion_report.md"`
  ```
  "data/file_deletion_report.md",
  ```
- Line 37: `"data/streamlined_cleanup_plan.md"`
  ```
  "data/streamlined_cleanup_plan.md"
  ```

**project_folder_reference (n8n_builder)**:
- Line 192: `"n8n_builder/README.md"`
  ```
  ("n8n_builder/README.md", "N8N_Builder Module"),
  ```

**project_folder_reference (Scripts)**:
- Line 193: `"Scripts/README.md"`
  ```
  ("Scripts/README.md", "N8N_Builder Scripts"),
  ```

**project_folder_reference (tests)**:
- Line 194: `"tests/README.md"`
  ```
  ("tests/README.md", "N8N_Builder Testing")
  ```

### Scripts\migrate_naming_convention.py
**File Type**: .py
**Issues Found**: 2

**potential_path_issue**:
- Line 25: `Path("."`
  ```
  self.project_root = Path(".")
  ```

**project_folder_reference (config)**:
- Line 114: `"Scripts/public_repo_config.json"`
  ```
  "Scripts/public_repo_config.json"
  ```

### Scripts\pre_commit_cleanup.py
**File Type**: .py
**Issues Found**: 3

**project_folder_reference (archive)**:
- Line 102: `'Archive/'`
  ```
  'Archive/',
  ```

**project_folder_reference (Scripts)**:
- Line 133: `'Scripts/*.exe'`
  ```
  'Scripts/*.exe',  # Virtual environment executables
  ```

**root_file_reference (.gitignore)**:
- Line 349: `'.gitignore'`
  ```
  gitignore_path = self.project_root / '.gitignore'
  ```

### Scripts\pre_execution_verification.py
**File Type**: .py
**Issues Found**: 10

**project_folder_reference (Documentation)**:
- Line 96: `"Documentation/MANUAL_REVIEW_CHECKLIST.md"`
  ```
  checklist_file = "Documentation/MANUAL_REVIEW_CHECKLIST.md"
  ```

**project_folder_reference (config)**:
- Line 150: `"Scripts/public_repo_config.json"`
  ```
  config_file = "Scripts/public_repo_config.json"
  ```

**root_file_reference (README.md)**:
- Line 200: `"README.md"`
  ```
  "README_public.md": "README.md",
  ```

**root_file_reference (requirements.txt)**:
- Line 201: `"requirements.txt"`
  ```
  "requirements_public.txt": "requirements.txt",
  ```

**root_file_reference (run.py)**:
- Line 202: `"run.py"`
  ```
  "run_public.py": "run.py",
  ```

**root_file_reference (setup.py)**:
- Line 203: `"setup.py"`
  ```
  "setup_public.py": "setup.py",
  ```

**root_file_reference (.gitignore)**:
- Line 205: `".gitignore"`
  ```
  ".gitignore_public": ".gitignore",
  ```

**potential_path_issue**:
- Line 258: `Path("pre_execution_detection.json"`
  ```
  if success and Path("pre_execution_detection.json").exists():
  ```
- Line 260: `open("pre_execution_detection.json"`
  ```
  with open("pre_execution_detection.json", 'r') as f:
  ```
- Line 260: `with open("pre_execution_detection.json"`
  ```
  with open("pre_execution_detection.json", 'r') as f:
  ```

### Scripts\prepare_public_release.py
**File Type**: .py
**Issues Found**: 5

**root_file_reference (README.md)**:
- Line 83: `"README.md"`
  ```
  "README.md",
  ```

**root_file_reference (requirements.txt)**:
- Line 84: `"requirements.txt"`
  ```
  "requirements.txt",
  ```
- Line 284: `"requirements.txt"`
  ```
  "dependencies": "requirements.txt",
  ```

**root_file_reference (run.py)**:
- Line 85: `"run.py"`
  ```
  "run.py",
  ```

**root_file_reference (setup.py)**:
- Line 86: `"setup.py"`
  ```
  "setup.py",
  ```

### Scripts\project_cleanup_manager.py
**File Type**: .py
**Issues Found**: 4

**project_folder_reference (Scripts)**:
- Line 25: `"Scripts/project_analysis_report.json"`
  ```
  self.analysis_file = analysis_file or Path("Scripts/project_analysis_report.json")
  ```
- Line 25: `Path("Scripts/project_analysis_report.json"`
  ```
  self.analysis_file = analysis_file or Path("Scripts/project_analysis_report.json")
  ```

**potential_path_issue**:
- Line 52: `Path("logs/project_cleanup.log"`
  ```
  log_file = Path("logs/project_cleanup.log")
  ```
- Line 311: `Path("logs/project_cleanup_report.json"`
  ```
  report_file = Path("logs/project_cleanup_report.json")
  ```

### Scripts\restore_n8n_setup.ps1
**File Type**: .ps1
**Issues Found**: 1

**potential_path_issue**:
- Line 111: `Test-Path "n8n-docker"`
  ```
  if (-not (Test-Path "n8n-docker")) {
  ```

### Scripts\rewrite_commit_messages.ps1
**File Type**: .ps1
**Issues Found**: 1

**potential_path_issue**:
- Line 65: `$filterCommand = "if [ `"`
  ```
  $filterCommand = "if [ `"`$1`" = `"$escapedOld`" ]; then echo `"$escapedNew`"; else echo `"`$1`"; fi"
  ```

### Scripts\run_cleanup.ps1
**File Type**: .ps1
**Issues Found**: 1

**potential_path_issue**:
- Line 7: `$backupName = "backup-cleanup-$(Get-Date -Format '`
  ```
  $backupName = "backup-cleanup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
  ```

### Scripts\run_with_venv.ps1
**File Type**: .ps1
**Issues Found**: 1

**project_folder_reference (Scripts)**:
- Line 11: `Test-Path "venv\Scripts\python.exe"`
  ```
  if (-not (Test-Path "venv\Scripts\python.exe")) {
  ```

### Scripts\sanitize_documentation.py
**File Type**: .py
**Issues Found**: 1

**root_file_reference (README.md)**:
- Line 168: `'readme.md'`
  ```
  if file_path.name.lower() == 'readme.md':
  ```

### Scripts\scan_md_for_private_refs.py
**File Type**: .py
**Issues Found**: 3

**potential_path_issue**:
- Line 135: `Get-Content '{file_path}'`
  ```
  script_lines.append(f"$content = Get-Content '{file_path}' -Raw")
  ```
- Line 146: `Set-Content '{file_path}'`
  ```
  f"$content | Set-Content '{file_path}' -NoNewline",
  ```

**project_folder_reference (Scripts)**:
- Line 226: `"Scripts/markdown_analysis.json"`
  ```
  analysis_path = "Scripts/markdown_analysis.json"
  ```

### Scripts\setup_log_rotation.py
**File Type**: .py
**Issues Found**: 1

**potential_path_issue**:
- Line 25: `Path("logs"`
  ```
  logs_dir = Path("logs")
  ```

### Scripts\shutdown.py
**File Type**: .py
**Issues Found**: 1

**root_file_reference (run.py)**:
- Line 33: `'run.py'`
  ```
  'run.py', 'n8n_builder', 'self-healer', 'dashboard.py'
  ```

### Scripts\simple_commit_cleanup.ps1
**File Type**: .ps1
**Issues Found**: 3

**potential_path_issue**:
- Line 9: `Test-Path ".git"`
  ```
  if (-not (Test-Path ".git")) {
  ```
- Line 61: `$backupBranch = "backup-before-cleanup-$(Get-Date -Format '`
  ```
  $backupBranch = "backup-before-cleanup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
  ```
- Line 78: `$sedScript = "temp_sed_script.txt"`
  ```
  $sedScript = "temp_sed_script.txt"
  ```

### Scripts\streamlined_documentation_cleanup.py
**File Type**: .py
**Issues Found**: 17

**root_file_reference (README.md)**:
- Line 28: `'README.md'`
  ```
  'README.md',
  ```

**project_folder_reference (Documentation)**:
- Line 33: `'Documentation/DevelopersWorkflow.md'`
  ```
  'Documentation/DevelopersWorkflow.md',
  ```
- Line 38: `'Documentation/ARCHITECTURE.md'`
  ```
  'Documentation/ARCHITECTURE.md': 'Documentation/Architecture.md',
  ```
- Line 38: `'Documentation/Architecture.md'`
  ```
  'Documentation/ARCHITECTURE.md': 'Documentation/Architecture.md',
  ```
- Line 39: `'Documentation/TROUBLESHOOTING.md'`
  ```
  'Documentation/TROUBLESHOOTING.md': 'Documentation/guides/Troubleshooting.md',
  ```
- Line 39: `'Documentation/guides/Troubleshooting.md'`
  ```
  'Documentation/TROUBLESHOOTING.md': 'Documentation/guides/Troubleshooting.md',
  ```
- Line 40: `'Documentation/guides/INTEGRATION_SETUP.md'`
  ```
  'Documentation/guides/INTEGRATION_SETUP.md': 'Documentation/guides/Integration.md',
  ```
- Line 40: `'Documentation/guides/Integration.md'`
  ```
  'Documentation/guides/INTEGRATION_SETUP.md': 'Documentation/guides/Integration.md',
  ```
- Line 41: `'Documentation/api/API_DOCUMENTATION.md'`
  ```
  'Documentation/api/API_DOCUMENTATION.md': 'Documentation/api/API_Reference.md'
  ```
- Line 41: `'Documentation/api/API_Reference.md'`
  ```
  'Documentation/api/API_DOCUMENTATION.md': 'Documentation/api/API_Reference.md'
  ```
- Line 46: `'Documentation/DesignPrinciples.md'`
  ```
  'Documentation/DesignPrinciples.md',
  ```
- Line 47: `'Documentation/technical/Specifications.md'`
  ```
  'Documentation/technical/Specifications.md'
  ```
- Line 52: `'Documentation/ProcessFlow.md'`
  ```
  'Documentation/ProcessFlow.md',  # Auto-generated, might be useful
  ```
- Line 53: `'Documentation/SQLConventions.md'`
  ```
  'Documentation/SQLConventions.md',  # Specific technical content
  ```

**project_folder_reference (Scripts)**:
- Line 54: `'Scripts/README.md'`
  ```
  'Scripts/README.md'  # Script documentation
  ```

**project_folder_reference (data)**:
- Line 247: `"data/streamlined_cleanup_plan.md"`
  ```
  plan_file = "data/streamlined_cleanup_plan.md"
  ```
- Line 248: `"data/streamlined_cleanup_analysis.json"`
  ```
  analysis_file = "data/streamlined_cleanup_analysis.json"
  ```

### Scripts\sync-community-only.ps1
**File Type**: .ps1
**Issues Found**: 6

**potential_path_issue**:
- Line 5: `$CommunityMessage = ""`
  ```
  [string]$CommunityMessage = ""
  ```
- Line 9: `$ErrorActionPreference = "Stop"`
  ```
  $ErrorActionPreference = "Stop"
  ```
- Line 14: `$Color = "White"`
  ```
  [string]$Color = "White"
  ```
- Line 70: `Test-Path "sync-public.ps1"`
  ```
  if (-not (Test-Path "sync-public.ps1")) {
  ```

**project_folder_reference (n8n_builder)**:
- Line 50: `Test-Path "n8n_builder"`
  ```
  if (-not (Test-Path "n8n_builder") -or -not (Test-Path "Scripts")) {
  ```

**project_folder_reference (Scripts)**:
- Line 50: `Test-Path "Scripts"`
  ```
  if (-not (Test-Path "n8n_builder") -or -not (Test-Path "Scripts")) {
  ```

### Scripts\sync-public.ps1
**File Type**: .ps1
**Issues Found**: 21

**project_folder_reference (n8n_builder)**:
- Line 18: `$PublicRepoPath = "..\N8N_Builder_Community"`
  ```
  [string]$PublicRepoPath = "..\N8N_Builder_Community",
  ```

**potential_path_issue**:
- Line 23: `$LogLevel = "INFO"`
  ```
  [string]$LogLevel = "INFO"
  ```
- Line 29: `$BackupPath = "$PublicRepoPath.backup"`
  ```
  $BackupPath = "$PublicRepoPath.backup"
  ```
- Line 30: `$VerificationScript = "verification_pipeline.py"`
  ```
  $VerificationScript = "verification_pipeline.py"
  ```
- Line 31: `$DetectionScript = "detect_private_components.py"`
  ```
  $DetectionScript = "detect_private_components.py"
  ```
- Line 37: `$Level = "INFO"`
  ```
  param([string]$Message, [string]$Level = "INFO")
  ```
- Line 39: `$logEntry = "[$timestamp] [$Level] $Message"`
  ```
  $logEntry = "[$timestamp] [$Level] $Message"
  ```
- Line 184: `$detectionCmd = "python `"`
  ```
  $detectionCmd = "python `"$DetectionScript`" --path `"$SourceRepo`" --output `"pre_sync_detection.json`""
  ```
- Line 189: `Test-Path "pre_sync_detection.json"`
  ```
  if (Test-Path "pre_sync_detection.json") {
  ```
- Line 190: `Get-Content "pre_sync_detection.json"`
  ```
  $detectionResults = Get-Content "pre_sync_detection.json" | ConvertFrom-Json
  ```
- Line 227: `$detectionCmd = "python `"`
  ```
  $detectionCmd = "python `"$DetectionScript`" --path `"$PublicRepoPath`" --output `"post_sync_detection.json`""
  ```
- Line 232: `Test-Path "post_sync_detection.json"`
  ```
  if (Test-Path "post_sync_detection.json") {
  ```
- Line 233: `Get-Content "post_sync_detection.json"`
  ```
  $detectionResults = Get-Content "post_sync_detection.json" | ConvertFrom-Json
  ```
- Line 257: `$backupPath = "$PublicRepoPath.backup.$(Get-Date -Format '`
  ```
  $backupPath = "$PublicRepoPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
  ```
- Line 455: `Test-Path ".git"`
  ```
  if (-not (Test-Path ".git")) {
  ```

**project_folder_reference (Scripts)**:
- Line 28: `$LogFile = "Scripts\sync-public.log"`
  ```
  $LogFile = "Scripts\sync-public.log"
  ```

**root_file_reference (.gitignore)**:
- Line 64: `".gitignore"`
  ```
  ".gitignore_public" = ".gitignore"
  ```

**root_file_reference (README.md)**:
- Line 65: `"README.md"`
  ```
  "README_public.md" = "README.md"
  ```

**root_file_reference (requirements.txt)**:
- Line 66: `"requirements.txt"`
  ```
  "requirements_public.txt" = "requirements.txt"
  ```

**root_file_reference (run.py)**:
- Line 67: `"run.py"`
  ```
  "run_public.py" = "run.py"
  ```

**root_file_reference (setup.py)**:
- Line 68: `"setup.py"`
  ```
  "setup_public.py" = "setup.py"
  ```

### Scripts\test-blogger-api.ps1
**File Type**: .ps1
**Issues Found**: 3

**potential_path_issue**:
- Line 5: `$BlogId = ""`
  ```
  [string]$BlogId = "",
  ```
- Line 6: `$AccessToken = ""`
  ```
  [string]$AccessToken = "",
  ```
- Line 108: `$workflowPath = "projects\elthosdb1\ElthosRPG_Blog_Twitter_BloggerAPI.json"`
  ```
  $workflowPath = "projects\elthosdb1\ElthosRPG_Blog_Twitter_BloggerAPI.json"
  ```

### Scripts\test-detection.ps1
**File Type**: .ps1
**Issues Found**: 1

**project_folder_reference (n8n_builder)**:
- Line 3: `$TestFile = "n8n_builder\optional_integrations.py"`
  ```
  [string]$TestFile = "n8n_builder\optional_integrations.py"
  ```

### Scripts\test_detection_simple.py
**File Type**: .py
**Issues Found**: 2

**project_folder_reference (n8n_builder)**:
- Line 16: `"n8n_builder/optional_integrations.py"`
  ```
  test_file = Path("n8n_builder/optional_integrations.py")
  ```
- Line 16: `Path("n8n_builder/optional_integrations.py"`
  ```
  test_file = Path("n8n_builder/optional_integrations.py")
  ```

### Scripts\test_enhanced_sync.py
**File Type**: .py
**Issues Found**: 5

**potential_path_issue**:
- Line 86: `Path("sync-public.ps1"`
  ```
  sync_script = Path("sync-public.ps1")
  ```
- Line 222: `Path("README_community.md"`
  ```
  readme_file = Path("README_community.md")
  ```

**project_folder_reference (config)**:
- Line 133: `"Scripts/public_repo_config.json"`
  ```
  config_file = Path("Scripts/public_repo_config.json")
  ```
- Line 133: `Path("Scripts/public_repo_config.json"`
  ```
  config_file = Path("Scripts/public_repo_config.json")
  ```

**project_folder_reference (Documentation)**:
- Line 185: `Path("sanitize_documentation.py"`
  ```
  sanitizer_script = Path("sanitize_documentation.py")
  ```

### Scripts\test_knowledgebase_procedures.ps1
**File Type**: .ps1
**Issues Found**: 1

**potential_path_issue**:
- Line 6: `$VenvPath = ".\venv"`
  ```
  [string]$VenvPath = ".\venv",
  ```

### Scripts\test_safe_cleanup.py
**File Type**: .py
**Issues Found**: 4

**project_folder_reference (tests)**:
- Line 38: `'tests/__pycache__/test.cpython-311.pyc'`
  ```
  'tests/__pycache__/test.cpython-311.pyc': b'test cache',
  ```
- Line 53: `'tests/test_main.py'`
  ```
  'tests/test_main.py': 'def test_main(): pass',
  ```

**root_file_reference (README.md)**:
- Line 54: `'README.md'`
  ```
  'README.md': '# Test Project',
  ```

**root_file_reference (requirements.txt)**:
- Line 55: `'requirements.txt'`
  ```
  'requirements.txt': 'requests>=2.0.0',
  ```

### Scripts\test_verification_systems.py
**File Type**: .py
**Issues Found**: 10

**potential_path_issue**:
- Line 72: `Path("detect_private_components.py"`
  ```
  python_script = Path("detect_private_components.py")
  ```
- Line 76: `Path("detect-private-components.ps1"`
  ```
  ps_script = Path("detect-private-components.ps1")
  ```
- Line 144: `Path("test_private_references.py"`
  ```
  test_file = Path("test_private_references.py")
  ```
- Line 162: `Path("test_detection_output.json"`
  ```
  if success and Path("test_detection_output.json").exists():
  ```
- Line 163: `open("test_detection_output.json"`
  ```
  with open("test_detection_output.json", 'r') as f:
  ```
- Line 163: `with open("test_detection_output.json"`
  ```
  with open("test_detection_output.json", 'r') as f:
  ```
- Line 201: `Path("verification_pipeline.py"`
  ```
  pipeline_script = Path("verification_pipeline.py")
  ```
- Line 291: `Path("sync-public.ps1"`
  ```
  sync_script = Path("sync-public.ps1")
  ```

**project_folder_reference (Documentation)**:
- Line 245: `"Documentation/MANUAL_REVIEW_CHECKLIST.md"`
  ```
  checklist_file = Path("Documentation/MANUAL_REVIEW_CHECKLIST.md")
  ```
- Line 245: `Path("Documentation/MANUAL_REVIEW_CHECKLIST.md"`
  ```
  checklist_file = Path("Documentation/MANUAL_REVIEW_CHECKLIST.md")
  ```

### Scripts\update_folder_references.py
**File Type**: .py
**Issues Found**: 1

**potential_path_issue**:
- Line 23: `Path("."`
  ```
  self.project_root = Path(".")
  ```

### Scripts\validate-localtunnel-integration.ps1
**File Type**: .ps1
**Issues Found**: 3

**potential_path_issue**:
- Line 6: `$TunnelUrl = ""`
  ```
  [string]$TunnelUrl = "",
  ```
- Line 130: `$callbackUrl = "$Url/rest/oauth2-credential/callback"`
  ```
  $callbackUrl = "$Url/rest/oauth2-credential/callback"
  ```

**root_file_reference (docker-compose.yml)**:
- Line 153: `$dockerComposePath = "n8n-docker\docker-compose.yml"`
  ```
  $dockerComposePath = "n8n-docker\docker-compose.yml"
  ```

### Scripts\validate_documentation_links.py
**File Type**: .py
**Issues Found**: 10

**root_file_reference (README.md)**:
- Line 24: `'README.md'`
  ```
  'README.md',
  ```

**project_folder_reference (Documentation)**:
- Line 27: `'Documentation/Architecture.md'`
  ```
  'Documentation/Architecture.md',
  ```
- Line 28: `'Documentation/DesignPrinciples.md'`
  ```
  'Documentation/DesignPrinciples.md',
  ```
- Line 29: `'Documentation/DevelopersWorkflow.md'`
  ```
  'Documentation/DevelopersWorkflow.md',
  ```
- Line 30: `'Documentation/guides/Integration.md'`
  ```
  'Documentation/guides/Integration.md',
  ```
- Line 31: `'Documentation/guides/Troubleshooting.md'`
  ```
  'Documentation/guides/Troubleshooting.md',
  ```
- Line 32: `'Documentation/technical/Specifications.md'`
  ```
  'Documentation/technical/Specifications.md',
  ```
- Line 33: `'Documentation/api/API_Reference.md'`
  ```
  'Documentation/api/API_Reference.md',
  ```

**project_folder_reference (Scripts)**:
- Line 34: `'Scripts/README.md'`
  ```
  'Scripts/README.md'
  ```

**project_folder_reference (data)**:
- Line 154: `"data/link_validation_report.md"`
  ```
  report_file = "data/link_validation_report.md"
  ```

### Scripts\validate_documentation_quality.py
**File Type**: .py
**Issues Found**: 5

**root_file_reference (README.md)**:
- Line 254: `"README.md"`
  ```
  key_files = ["README.md", "GETTING_STARTED.md"]
  ```

**project_folder_reference (Documentation)**:
- Line 416: `"data/documentation_quality_report.json"`
  ```
  report_path = Path("data/documentation_quality_report.json")
  ```
- Line 416: `Path("data/documentation_quality_report.json"`
  ```
  report_path = Path("data/documentation_quality_report.json")
  ```
- Line 423: `"data/documentation_quality_summary.md"`
  ```
  summary_path = Path("data/documentation_quality_summary.md")
  ```
- Line 423: `Path("data/documentation_quality_summary.md"`
  ```
  summary_path = Path("data/documentation_quality_summary.md")
  ```

### Scripts\verification_pipeline.py
**File Type**: .py
**Issues Found**: 6

**potential_path_issue**:
- Line 113: `Path("pre_sync_detection.json"`
  ```
  if Path("pre_sync_detection.json").exists():
  ```
- Line 114: `open("pre_sync_detection.json"`
  ```
  with open("pre_sync_detection.json", 'r') as f:
  ```
- Line 114: `with open("pre_sync_detection.json"`
  ```
  with open("pre_sync_detection.json", 'r') as f:
  ```
- Line 216: `Path("post_sync_detection.json"`
  ```
  if Path("post_sync_detection.json").exists():
  ```
- Line 217: `open("post_sync_detection.json"`
  ```
  with open("post_sync_detection.json", 'r') as f:
  ```
- Line 217: `with open("post_sync_detection.json"`
  ```
  with open("post_sync_detection.json", 'r') as f:
  ```

### Scripts\verify-public-clean.ps1
**File Type**: .ps1
**Issues Found**: 7

**project_folder_reference (n8n_builder)**:
- Line 8: `$PublicRepoPath = "..\N8N_Builder_Community"`
  ```
  [string]$PublicRepoPath = "..\N8N_Builder_Community"
  ```

**potential_path_issue**:
- Line 15: `$Status = "INFO"`
  ```
  param([string]$Message, [string]$Status = "INFO")
  ```

**root_file_reference (README.md)**:
- Line 60: `"README.md"`
  ```
  "README.md",
  ```

**root_file_reference (requirements.txt)**:
- Line 61: `"requirements.txt"`
  ```
  "requirements.txt",
  ```

**root_file_reference (run.py)**:
- Line 62: `"run.py"`
  ```
  "run.py",
  ```

**root_file_reference (setup.py)**:
- Line 63: `"setup.py"`
  ```
  "setup.py",
  ```

**root_file_reference (.gitignore)**:
- Line 65: `".gitignore"`
  ```
  ".gitignore"
  ```

## Recommended Path Fix Pattern

For Python scripts, add this at the beginning:
```python
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Optional: change working directory
```

For PowerShell scripts, add this at the beginning:
```powershell
# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Optional: change working directory
```
