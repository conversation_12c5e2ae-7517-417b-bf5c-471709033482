# n8n Docker Environment Configuration Template
# Copy this file to .env and customize for your environment
# Development Environment Settings

# Basic n8n Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_LISTEN_ADDRESS=0.0.0.0

# Security Configuration
# IMPORTANT: Generate a secure encryption key for production!
# You can use: openssl rand -base64 32
N8N_ENCRYPTION_KEY=CHANGE-THIS-TO-A-SECURE-RANDOM-KEY

# Database Configuration - PostgreSQL (recommended for Docker)
DB_TYPE=postgresdb
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_HOST=postgres
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_USER=n8n
DB_POSTGRESDB_PASSWORD=CHANGE-THIS-PASSWORD
DB_POSTGRESDB_SCHEMA=public

# Authentication (Basic Auth for development)
# IMPORTANT: Change these default credentials!
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=CHANGE-THIS-PASSWORD

# Workflow and Execution Settings
EXECUTIONS_PROCESS=main
EXECUTIONS_MODE=regular
EXECUTIONS_TIMEOUT=3600
EXECUTIONS_TIMEOUT_MAX=7200
EXECUTIONS_DATA_SAVE_ON_ERROR=all
EXECUTIONS_DATA_SAVE_ON_SUCCESS=all
EXECUTIONS_DATA_SAVE_ON_PROGRESS=false
EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=true

# Logging
N8N_LOG_LEVEL=info
N8N_LOG_OUTPUT=console

# Editor Configuration
# Keep local for editor access, but webhooks will use nGrok URL
N8N_EDITOR_BASE_URL=http://localhost:5678

# Webhook Configuration
# This will be automatically updated by the automation scripts
WEBHOOK_URL=https://your-ngrok-url.ngrok-free.app/

# Generic OAuth Configuration (if needed)
# N8N_GENERIC_OAUTH_CLIENT_ID=your-oauth-client-id
# N8N_GENERIC_OAUTH_CLIENT_SECRET=your-oauth-client-secret

# Email Configuration (for notifications - optional)
# N8N_EMAIL_MODE=smtp
# N8N_SMTP_HOST=smtp.gmail.com
# N8N_SMTP_PORT=587
# N8N_SMTP_USER=<EMAIL>
# N8N_SMTP_PASS=your-app-password
# N8N_SMTP_SENDER=<EMAIL>

# Development Settings
NODE_ENV=development
N8N_METRICS=false

# File System Permissions
N8N_USER_FOLDER=/home/<USER>/.n8n
N8N_USER_ID=1000
N8N_GROUP_ID=1000

# Timezone
GENERIC_TIMEZONE=America/New_York

# SSL/TLS Configuration (for production)
# N8N_PROTOCOL=https
# N8N_SSL_KEY=/etc/ssl/certs/privkey.pem
# N8N_SSL_CERT=/etc/ssl/certs/fullchain.pem

# External Hooks (for custom integrations)
# N8N_EXTERNAL_HOOK_FILES=/home/<USER>/.n8n/hooks/hook.js

# Performance Settings
N8N_PAYLOAD_SIZE_MAX=16
N8N_BINARY_DATA_TTL=60
N8N_BINARY_DATA_MODE=filesystem

# Queue Mode Settings (for scaling)
# QUEUE_BULL_REDIS_HOST=redis
# QUEUE_BULL_REDIS_PORT=6379
# QUEUE_BULL_REDIS_DB=0
# QUEUE_BULL_REDIS_PASSWORD=
# EXECUTIONS_PROCESS=queue
