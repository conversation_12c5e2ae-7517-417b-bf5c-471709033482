services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # Load environment variables from .env file
      - N8N_HOST=${N8N_HOST}
      - N8N_PORT=${N8N_PORT}
      - N8N_PROTOCOL=${N8N_PROTOCOL}
      - N8N_LISTEN_ADDRESS=${N8N_LISTEN_ADDRESS}
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - DB_TYPE=${DB_TYPE}
      - DB_POSTGRESDB_DATABASE=${DB_POSTGRESDB_DATABASE}
      - DB_POSTGRESDB_HOST=${DB_POSTGRESDB_HOST}
      - DB_POSTGRESDB_PORT=${DB_POSTGRESDB_PORT}
      - DB_POSTGRESDB_USER=${DB_POSTGRESDB_USER}
      - DB_POSTGRESDB_PASSWORD=${DB_POSTGRESDB_PASSWORD}
      - DB_POSTGRESDB_SCHEMA=${DB_POSTGRESDB_SCHEMA}
      - N8N_BASIC_AUTH_ACTIVE=${N8N_BASIC_AUTH_ACTIVE}
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD}
      - EXECUTIONS_MODE=${EXECUTIONS_MODE}
      - EXECUTIONS_TIMEOUT=${EXECUTIONS_TIMEOUT}
      - EXECUTIONS_TIMEOUT_MAX=${EXECUTIONS_TIMEOUT_MAX}
      - EXECUTIONS_DATA_SAVE_ON_ERROR=${EXECUTIONS_DATA_SAVE_ON_ERROR}
      - EXECUTIONS_DATA_SAVE_ON_SUCCESS=${EXECUTIONS_DATA_SAVE_ON_SUCCESS}
      - EXECUTIONS_DATA_SAVE_ON_PROGRESS=${EXECUTIONS_DATA_SAVE_ON_PROGRESS}
      - EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=${EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS}
      - N8N_LOG_LEVEL=${N8N_LOG_LEVEL}
      - N8N_LOG_OUTPUT=${N8N_LOG_OUTPUT}
      - N8N_EDITOR_BASE_URL=https://7cf5d5df8e061d.lhr.life
      - WEBHOOK_URL=https://7cf5d5df8e061d.lhr.life/
      - NODE_ENV=${NODE_ENV}
      - N8N_METRICS=${N8N_METRICS}
      - N8N_USER_FOLDER=${N8N_USER_FOLDER}
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
      - N8N_PAYLOAD_SIZE_MAX=${N8N_PAYLOAD_SIZE_MAX}
      - N8N_BINARY_DATA_MODE=${N8N_BINARY_DATA_MODE}
      - N8N_RUNNERS_ENABLED=${N8N_RUNNERS_ENABLED}
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=${N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS}
    volumes:
      # Persistent data storage
      - n8n_data:/home/<USER>/.n8n
      # Mount local projects folder for development
      - ../projects:/home/<USER>/projects:ro
      # Mount SSL certificates (if using HTTPS)
      - ./ssl:/etc/ssl/certs:ro
    networks:
      - n8n-network
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_POSTGRESDB_DATABASE:-n8n}
      - POSTGRES_USER=${DB_POSTGRESDB_USER:-n8n}
      - POSTGRES_PASSWORD=${DB_POSTGRESDB_PASSWORD:-n8n_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_POSTGRESDB_USER:-n8n}"]
      interval: 10s
      timeout: 5s
      retries: 5
    # Uncomment the following lines if you want to access PostgreSQL directly
    # ports:
    #   - "5432:5432"

  # Redis (optional - for queue mode scaling)
  # redis:
  #   image: redis:7-alpine
  #   container_name: n8n-redis
  #   restart: unless-stopped
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - n8n-network
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

networks:
  n8n-network:
    driver: bridge
    name: n8n-network

volumes:
  n8n_data:
    external: true
    name: n8n_data
  postgres_data:
    external: true
    name: n8n_postgres_data
  # redis_data:
  #   external: true
  #   name: n8n_redis_data
