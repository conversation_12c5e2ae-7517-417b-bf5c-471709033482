# Direct commit message cleanup using git filter-branch
# This is the most reliable approach for Windows

Write-Host "🔧 Direct Commit Message Cleanup" -ForegroundColor Yellow
Write-Host ""

# Create backup first
$backupBranch = "backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Write-Host "📋 Creating backup branch: $backupBranch" -ForegroundColor Cyan
git branch $backupBranch

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to create backup branch" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Backup created successfully" -ForegroundColor Green
Write-Host ""

# Suppress filter-branch warning
$env:FILTER_BRANCH_SQUELCH_WARNING = 1

Write-Host "🔄 Starting commit message rewrite..." -ForegroundColor Cyan

# Use a simple PowerShell-based filter
$filterScript = @'
$message = $args[0]
switch ($message) {
    "Remove additional private Self-Healer and KnowledgeBase development files" { "Remove additional private development files" }
    "Enhanced .gitignore to comprehensively exclude all private Self-Healer and KnowledgeBase components" { "Enhanced .gitignore to exclude private components" }
    "Remove private Self-Healer and KnowledgeBase components from public repository" { "Remove private components from public repository" }
    "update gitignore for self-healer and knowledgebase" { "update gitignore for private components" }
    "Updates for Self-Healer Separation Finalization" { "Updates for system separation finalization" }
    "Updates to KnowledgeBase Table structures and SP" { "Updates to database table structures and SP" }
    "Updates to Self-Healer" { "Updates to system components" }
    "Self-Healder - KnowledgeBase Fix" { "System component fixes" }
    "Self-Healer & KnowledgeBase Updates 4" { "System component updates 4" }
    "Self-Healer & KnowledgeBase Updates 3" { "System component updates 3" }
    "Self-Healer & KnowledgeBse Integration Updates 2" { "System integration updates 2" }
    "Self-Healer & KnowledgeBase Integration Updates" { "System integration updates" }
    "KnowledgeBase Updates" { "Database component updates" }
    "Update to Full version of Self-Healer" { "Update to full system version" }
    "Fixes to Self-Healer and Documentation" { "Fixes to system and documentation" }
    "Updates to Self-Healer / KnowledgeBase" { "Updates to system components" }
    "Self-Healer Finalized and Integrated with KnowledgeBase - First Pass" { "System integration finalized - first pass" }
    "Self Healer Updates" { "System component updates" }
    "Self-Healer" { "System component implementation" }
    default { $message }
}
'@

# Save the filter script
$filterFile = "temp_filter.ps1"
$filterScript | Out-File -FilePath $filterFile -Encoding UTF8

try {
    Write-Host "📝 Created PowerShell filter script" -ForegroundColor Green
    
    # Run git filter-branch with PowerShell filter
    git filter-branch -f --msg-filter "powershell -File $filterFile" -- --all
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Successfully rewrote all commit messages!" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "🎉 Cleanup completed!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Next steps:" -ForegroundColor Cyan
        Write-Host "1. Verify changes: git log --oneline | head -20" -ForegroundColor White
        Write-Host "2. Force push: git push --force-with-lease" -ForegroundColor White
        Write-Host "3. If problems: git reset --hard $backupBranch" -ForegroundColor White
        
    } else {
        Write-Host "❌ Filter-branch failed" -ForegroundColor Red
        Write-Host "💡 Restore with: git reset --hard $backupBranch" -ForegroundColor Yellow
    }
    
} finally {
    # Cleanup
    if (Test-Path $filterFile) {
        Remove-Item $filterFile
    }
}
