#!/usr/bin/env python3
"""
README File Comparison Script
============================
Compares README.md and README_community.md to identify overlaps and differences.
Helps determine if files are redundant or serve different purposes.

Usage: python compare_readme_files.py
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import re
import difflib
from pathlib import Path
from typing import List, Dict, Tuple

class ReadmeComparator:
    """Compares README files to identify overlaps and differences."""
    
    def __init__(self):
        self.main_readme = Path("README.md")
        self.community_readme = Path("README_community.md")
    
    def read_file_sections(self, file_path: Path) -> Dict[str, str]:
        """Parse README file into sections based on headers."""
        if not file_path.exists():
            return {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        sections = {}
        current_section = "header"
        current_content = []
        
        for line in content.split('\n'):
            # Check if line is a header (starts with #)
            if line.strip().startswith('#'):
                # Save previous section
                if current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                
                # Start new section
                current_section = line.strip()
                current_content = []
            else:
                current_content.append(line)
        
        # Save last section
        if current_content:
            sections[current_section] = '\n'.join(current_content).strip()
        
        return sections
    
    def get_file_stats(self, file_path: Path) -> Dict[str, any]:
        """Get basic statistics about a file."""
        if not file_path.exists():
            return {"exists": False}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        return {
            "exists": True,
            "size_bytes": len(content.encode('utf-8')),
            "line_count": len(lines),
            "char_count": len(content),
            "word_count": len(content.split()),
            "header_count": len([line for line in lines if line.strip().startswith('#')]),
            "empty_lines": len([line for line in lines if not line.strip()])
        }
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity percentage between two texts."""
        if not text1 or not text2:
            return 0.0
        
        # Use difflib to calculate similarity
        similarity = difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
        return similarity * 100
    
    def find_common_sections(self, sections1: Dict, sections2: Dict) -> List[Tuple[str, str, float]]:
        """Find sections that appear in both files and their similarity."""
        common_sections = []
        
        for header1, content1 in sections1.items():
            for header2, content2 in sections2.items():
                # Check if headers are similar
                header_similarity = self.calculate_similarity(header1, header2)
                content_similarity = self.calculate_similarity(content1, content2)
                
                # Consider it a match if headers are very similar or content is very similar
                if header_similarity > 70 or content_similarity > 60:
                    common_sections.append((header1, header2, max(header_similarity, content_similarity)))
        
        return sorted(common_sections, key=lambda x: x[2], reverse=True)
    
    def analyze_purpose_differences(self, sections1: Dict, sections2: Dict) -> Dict[str, List[str]]:
        """Analyze what purposes each file serves."""
        analysis = {
            "main_readme_unique": [],
            "community_readme_unique": [],
            "shared_content": []
        }
        
        # Get all headers from both files
        headers1 = set(sections1.keys())
        headers2 = set(sections2.keys())
        
        # Find unique sections
        for header in headers1:
            if not any(self.calculate_similarity(header, h2) > 70 for h2 in headers2):
                analysis["main_readme_unique"].append(header)
        
        for header in headers2:
            if not any(self.calculate_similarity(header, h1) > 70 for h1 in headers1):
                analysis["community_readme_unique"].append(header)
        
        # Find shared content
        common_sections = self.find_common_sections(sections1, sections2)
        for h1, h2, similarity in common_sections:
            if similarity > 70:
                analysis["shared_content"].append(f"{h1} ≈ {h2} ({similarity:.1f}% similar)")
        
        return analysis
    
    def generate_recommendations(self, stats1: Dict, stats2: Dict, purpose_analysis: Dict) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Check file sizes
        if stats1.get("exists") and stats2.get("exists"):
            size_diff = abs(stats1["size_bytes"] - stats2["size_bytes"])
            size_ratio = size_diff / max(stats1["size_bytes"], stats2["size_bytes"])
            
            if size_ratio < 0.2:  # Files are very similar in size
                recommendations.append("FILES ARE SIMILAR SIZE - Potential redundancy")
            
            # Check content overlap
            shared_count = len(purpose_analysis["shared_content"])
            unique_main = len(purpose_analysis["main_readme_unique"])
            unique_community = len(purpose_analysis["community_readme_unique"])
            
            if shared_count > unique_main and shared_count > unique_community:
                recommendations.append("HIGH OVERLAP DETECTED - Consider consolidating files")
            
            if unique_main == 0:
                recommendations.append("MAIN README HAS NO UNIQUE CONTENT - Consider removing")
            
            if unique_community == 0:
                recommendations.append("COMMUNITY README HAS NO UNIQUE CONTENT - Consider removing")
            
            if shared_count == 0:
                recommendations.append("NO OVERLAP - Files serve different purposes, keep both")
        
        return recommendations
    
    def run_comparison(self) -> Dict:
        """Run complete comparison analysis."""
        print("🔍 Comparing README files...")
        
        # Get file statistics
        main_stats = self.get_file_stats(self.main_readme)
        community_stats = self.get_file_stats(self.community_readme)
        
        # Parse sections
        main_sections = self.read_file_sections(self.main_readme) if main_stats["exists"] else {}
        community_sections = self.read_file_sections(self.community_readme) if community_stats["exists"] else {}
        
        # Analyze purposes
        purpose_analysis = self.analyze_purpose_differences(main_sections, community_sections)
        
        # Generate recommendations
        recommendations = self.generate_recommendations(main_stats, community_stats, purpose_analysis)
        
        return {
            "main_readme_stats": main_stats,
            "community_readme_stats": community_stats,
            "main_sections": list(main_sections.keys()),
            "community_sections": list(community_sections.keys()),
            "purpose_analysis": purpose_analysis,
            "recommendations": recommendations
        }
    
    def display_results(self, results: Dict):
        """Display comparison results."""
        print("\n" + "="*70)
        print("📊 README FILE COMPARISON RESULTS")
        print("="*70)
        
        # File statistics
        print(f"\n📄 README.md:")
        if results["main_readme_stats"]["exists"]:
            stats = results["main_readme_stats"]
            print(f"   • Size: {stats['size_bytes']:,} bytes")
            print(f"   • Lines: {stats['line_count']:,}")
            print(f"   • Words: {stats['word_count']:,}")
            print(f"   • Headers: {stats['header_count']}")
        else:
            print("   • File does not exist")
        
        print(f"\n📄 README_community.md:")
        if results["community_readme_stats"]["exists"]:
            stats = results["community_readme_stats"]
            print(f"   • Size: {stats['size_bytes']:,} bytes")
            print(f"   • Lines: {stats['line_count']:,}")
            print(f"   • Words: {stats['word_count']:,}")
            print(f"   • Headers: {stats['header_count']}")
        else:
            print("   • File does not exist")
        
        # Section analysis
        print(f"\n📋 Content Analysis:")
        purpose = results["purpose_analysis"]
        
        print(f"   • Shared Content: {len(purpose['shared_content'])} sections")
        for shared in purpose["shared_content"]:
            print(f"     - {shared}")
        
        print(f"   • README.md Unique: {len(purpose['main_readme_unique'])} sections")
        for unique in purpose["main_readme_unique"][:3]:  # Show first 3
            print(f"     - {unique}")
        
        print(f"   • README_community.md Unique: {len(purpose['community_readme_unique'])} sections")
        for unique in purpose["community_readme_unique"][:3]:  # Show first 3
            print(f"     - {unique}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(results["recommendations"], 1):
            print(f"   {i}. {rec}")
        
        print("="*70)

def main():
    """Main execution function."""
    comparator = ReadmeComparator()
    results = comparator.run_comparison()
    comparator.display_results(results)

if __name__ == "__main__":
    main()
