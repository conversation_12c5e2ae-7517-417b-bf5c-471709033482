# ============================================================================
# COMPREHENSIVE PRIVATE COMPONENT AUDIT SCRIPT
# ============================================================================
# This script performs an exhaustive audit of ALL private components
# to ensure NOTHING is missed in the separation process.
# ============================================================================

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
param(
    [string]$RepositoryPath = ".",
    [string]$OutputFile = "private-component-audit.json"
)

# Private terms to search for (comprehensive list)
$PrivateTerms = @(
    # Direct component names
    "Self-Healer", "Self_Healer", "SelfHealer", "self-healer", "self_healer", "selfhealer",
    "KnowledgeBase", "Knowledge_Base", "knowledgebase", "knowledge_base", "knowledge-base",
    
    # Configuration and file patterns
    "healer_config", "healer-config", "healerconfig",
    "config_private", "config-private", "configprivate",
    "private_config", "private-config", "privateconfig",
    
    # Database and procedure patterns
    "healer_", "_healer", "healer-", "-healer",
    "knowledge_", "_knowledge", "knowledge-", "-knowledge",
    
    # Advanced/Enterprise patterns
    "advanced_", "_advanced", "advanced-", "-advanced",
    "enterprise_", "_enterprise", "enterprise-", "-enterprise",
    "proprietary_", "_proprietary", "proprietary-", "-proprietary",
    
    # Debug and test patterns
    "debug_self_healer", "debug_healer", "debug_knowledge",
    "test_self_healer", "test_healer", "test_knowledge",
    
    # Import and module patterns
    "from Self_Healer", "import Self_Healer", "from KnowledgeBase", "import KnowledgeBase",
    "Self_Healer.", "KnowledgeBase.", "self_healer.", "knowledgebase.",
    
    # Class and function patterns
    "SelfHealerManager", "HealerManager", "KnowledgeBaseManager",
    "HealingSession", "ErrorHealer", "KnowledgeRetriever"
)

# File extensions to scan
$FileExtensions = @(
    "*.py", "*.md", "*.txt", "*.yml", "*.yaml", "*.json", "*.ps1", "*.bat", "*.sh",
    "*.html", "*.css", "*.js", "*.sql", "*.xml", "*.ini", "*.cfg", "*.conf"
)

# Directories to exclude from scanning
$ExcludeDirectories = @(
    ".git", "__pycache__", "venv", "env", ".venv", "node_modules", "cache", "logs"
)

$AuditResults = @{
    "scan_timestamp" = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "repository_path" = (Resolve-Path $RepositoryPath).Path
    "private_files" = @()
    "private_directories" = @()
    "files_with_references" = @()
    "summary" = @{}
}

function Write-AuditLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "FOUND" { "Magenta" }
            default { "White" }
        }
    )
}

function Test-ShouldExcludeDirectory {
    param([string]$DirectoryPath)
    
    $dirName = Split-Path $DirectoryPath -Leaf
    foreach ($exclude in $ExcludeDirectories) {
        if ($dirName -like $exclude) {
            return $true
        }
    }
    return $false
}

function Find-PrivateDirectories {
    Write-AuditLog "Scanning for private directories..."
    
    $directories = Get-ChildItem -Path $RepositoryPath -Directory -Recurse -ErrorAction SilentlyContinue |
        Where-Object { -not (Test-ShouldExcludeDirectory $_.FullName) }
    
    foreach ($dir in $directories) {
        $dirName = $dir.Name.ToLower()
        $relativePath = $dir.FullName.Replace($RepositoryPath, "").TrimStart('\')
        
        # Check if directory name contains private terms
        foreach ($term in $PrivateTerms) {
            if ($dirName -like "*$($term.ToLower())*") {
                Write-AuditLog "FOUND private directory: $relativePath" "FOUND"
                $AuditResults.private_directories += @{
                    "path" = $relativePath
                    "full_path" = $dir.FullName
                    "matched_term" = $term
                    "directory_name" = $dir.Name
                }
                break
            }
        }
    }
}

function Find-PrivateFiles {
    Write-AuditLog "Scanning for private files by name..."
    
    foreach ($extension in $FileExtensions) {
        $files = Get-ChildItem -Path $RepositoryPath -Filter $extension -Recurse -ErrorAction SilentlyContinue |
            Where-Object { -not (Test-ShouldExcludeDirectory $_.Directory.FullName) }
        
        foreach ($file in $files) {
            $fileName = $file.BaseName.ToLower()
            $relativePath = $file.FullName.Replace($RepositoryPath, "").TrimStart('\')
            
            # Check if filename contains private terms
            foreach ($term in $PrivateTerms) {
                if ($fileName -like "*$($term.ToLower())*") {
                    Write-AuditLog "FOUND private file: $relativePath" "FOUND"
                    $AuditResults.private_files += @{
                        "path" = $relativePath
                        "full_path" = $file.FullName
                        "matched_term" = $term
                        "file_name" = $file.Name
                        "size_bytes" = $file.Length
                    }
                    break
                }
            }
        }
    }
}

function Find-FilesWithPrivateReferences {
    Write-AuditLog "Scanning file contents for private references..."
    
    foreach ($extension in $FileExtensions) {
        $files = Get-ChildItem -Path $RepositoryPath -Filter $extension -Recurse -ErrorAction SilentlyContinue |
            Where-Object { 
                -not (Test-ShouldExcludeDirectory $_.Directory.FullName) -and
                $_.Length -lt 10MB  # Skip very large files
            }
        
        foreach ($file in $files) {
            try {
                $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
                if ($content) {
                    $relativePath = $file.FullName.Replace($RepositoryPath, "").TrimStart('\')
                    $foundTerms = @()
                    $lineMatches = @()
                    
                    foreach ($term in $PrivateTerms) {
                        if ($content -match [regex]::Escape($term)) {
                            $foundTerms += $term
                            
                            # Find line numbers where term appears
                            $lines = $content -split "`n"
                            for ($i = 0; $i -lt $lines.Length; $i++) {
                                if ($lines[$i] -match [regex]::Escape($term)) {
                                    $lineMatches += @{
                                        "line_number" = $i + 1
                                        "line_content" = $lines[$i].Trim()
                                        "matched_term" = $term
                                    }
                                }
                            }
                        }
                    }
                    
                    if ($foundTerms.Count -gt 0) {
                        Write-AuditLog "FOUND references in: $relativePath (Terms: $($foundTerms -join ', '))" "FOUND"
                        $AuditResults.files_with_references += @{
                            "path" = $relativePath
                            "full_path" = $file.FullName
                            "matched_terms" = $foundTerms
                            "line_matches" = $lineMatches
                            "file_size" = $file.Length
                            "match_count" = $lineMatches.Count
                        }
                    }
                }
            }
            catch {
                Write-AuditLog "ERROR scanning file: $($file.FullName) - $($_.Exception.Message)" "ERROR"
            }
        }
    }
}

function Generate-AuditSummary {
    Write-AuditLog "Generating audit summary..."
    
    $AuditResults.summary = @{
        "total_private_directories" = $AuditResults.private_directories.Count
        "total_private_files" = $AuditResults.private_files.Count
        "total_files_with_references" = $AuditResults.files_with_references.Count
        "total_reference_matches" = ($AuditResults.files_with_references | ForEach-Object { $_.match_count } | Measure-Object -Sum).Sum
        "most_common_terms" = @{}
        "file_types_affected" = @{}
    }
    
    # Count term frequency
    $allTerms = @()
    $allTerms += $AuditResults.private_files | ForEach-Object { $_.matched_term }
    $allTerms += $AuditResults.private_directories | ForEach-Object { $_.matched_term }
    $allTerms += $AuditResults.files_with_references | ForEach-Object { $_.matched_terms } | ForEach-Object { $_ }
    
    $termCounts = $allTerms | Group-Object | Sort-Object Count -Descending
    foreach ($termGroup in $termCounts) {
        $AuditResults.summary.most_common_terms[$termGroup.Name] = $termGroup.Count
    }
    
    # Count affected file types
    $allFiles = @()
    $allFiles += $AuditResults.private_files | ForEach-Object { [System.IO.Path]::GetExtension($_.path) }
    $allFiles += $AuditResults.files_with_references | ForEach-Object { [System.IO.Path]::GetExtension($_.path) }
    
    $fileTypeCounts = $allFiles | Group-Object | Sort-Object Count -Descending
    foreach ($typeGroup in $fileTypeCounts) {
        $AuditResults.summary.file_types_affected[$typeGroup.Name] = $typeGroup.Count
    }
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

Write-AuditLog "Starting comprehensive private component audit..."
Write-AuditLog "Repository: $RepositoryPath"
Write-AuditLog "Scanning for $($PrivateTerms.Count) private terms across $($FileExtensions.Count) file types"

Find-PrivateDirectories
Find-PrivateFiles
Find-FilesWithPrivateReferences
Generate-AuditSummary

# Save results to JSON file
$jsonOutput = $AuditResults | ConvertTo-Json -Depth 10
$jsonOutput | Out-File -FilePath $OutputFile -Encoding UTF8

Write-AuditLog "Audit completed! Results saved to: $OutputFile"
Write-AuditLog "SUMMARY:"
Write-AuditLog "  Private Directories: $($AuditResults.summary.total_private_directories)"
Write-AuditLog "  Private Files: $($AuditResults.summary.total_private_files)"
Write-AuditLog "  Files with References: $($AuditResults.summary.total_files_with_references)"
Write-AuditLog "  Total Reference Matches: $($AuditResults.summary.total_reference_matches)"

if ($AuditResults.summary.total_private_directories -gt 0 -or 
    $AuditResults.summary.total_private_files -gt 0 -or 
    $AuditResults.summary.total_files_with_references -gt 0) {
    Write-AuditLog "❌ PRIVATE COMPONENTS FOUND - Repository is NOT clean for public release" "ERROR"
    exit 1
} else {
    Write-AuditLog "✅ No private components found - Repository appears clean" "INFO"
    exit 0
}
