﻿{
    "files_to_move":  {
                          "data":  [
                                       "feedback_log.json",
                                       "pre_commit_cleanup_summary.json",
                                       "private-component-audit.json",
                                       "project_analysis_report.json",
                                       "safe_project_analysis_20250627_232355.json",
                                       "safe_project_analysis_20250627_232355.md",
                                       "safe_project_analysis_20250628_014002.json",
                                       "safe_project_analysis_20250628_014002.md",
                                       "sync-public.log",
                                       "cleanup-root-folder.log"
                                   ],
                          "Documentation":  [
                                                "ARCHITECTURE.md",
                                                "GITHUB_SETUP_INSTRUCTIONS.md",
                                                "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md",
                                                "SYSTEMATIC_REMEDIATION_PLAN.md"
                                            ],
                          "config":  [
                                         "ngrok-config-enhanced.yml",
                                         "ngrok-config.yml.template"
                                     ],
                          "Scripts":  [
                                          "cleanup-root-folder.ps1",
                                          "comprehensive-audit.ps1",
                                          "consolidate-self-healer.ps1",
                                          "deploy_public.ps1",
                                          "Emergency-Shutdown.ps1",
                                          "restore_n8n_setup.ps1",
                                          "sync-public.ps1",
                                          "verify-public-clean.ps1",
                                          "run_with_venv.bat",
                                          "run_with_venv.ps1",
                                          "shutdown.bat",
                                          "shutdown.py",
                                          "emergency_shutdown.bat"
                                      ],
                          "n8n_builder":  [
                                              "example_enhanced_workflow.py"
                                          ],
                          "archive":  [
                                          "2.5.2",
                                          "ngrok-setup",
                                          "zrok-config"
                                      ]
                      },
    "config_references":  [
                              {
                                  "new_location":  "config\\ngrok-config-enhanced.yml",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_config":  "ngrok-config-enhanced.yml"
                              },
                              {
                                  "new_location":  "config\\ngrok-config.yml.template",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_config":  "ngrok-config.yml.template"
                              },
                              {
                                  "new_location":  "config\\ngrok-config-enhanced.yml",
                                  "file":  "project_analysis_report.json",
                                  "referenced_config":  "ngrok-config-enhanced.yml"
                              },
                              {
                                  "new_location":  "config\\ngrok-config.yml.template",
                                  "file":  "project_analysis_report.json",
                                  "referenced_config":  "ngrok-config.yml.template"
                              },
                              {
                                  "new_location":  "config\\ngrok-config-enhanced.yml",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_config":  "ngrok-config-enhanced.yml"
                              },
                              {
                                  "new_location":  "config\\ngrok-config.yml.template",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_config":  "ngrok-config.yml.template"
                              },
                              {
                                  "new_location":  "config\\ngrok-config-enhanced.yml",
                                  "file":  "safe_project_analysis_20250627_232355.json",
                                  "referenced_config":  "ngrok-config-enhanced.yml"
                              },
                              {
                                  "new_location":  "config\\ngrok-config.yml.template",
                                  "file":  "safe_project_analysis_20250627_232355.json",
                                  "referenced_config":  "ngrok-config.yml.template"
                              },
                              {
                                  "new_location":  "config\\ngrok-config-enhanced.yml",
                                  "file":  "safe_project_analysis_20250628_014002.json",
                                  "referenced_config":  "ngrok-config-enhanced.yml"
                              },
                              {
                                  "new_location":  "config\\ngrok-config.yml.template",
                                  "file":  "safe_project_analysis_20250628_014002.json",
                                  "referenced_config":  "ngrok-config.yml.template"
                              },
                              {
                                  "new_location":  "config\\ngrok-config.yml.template",
                                  "file":  "sync-public.ps1",
                                  "referenced_config":  "ngrok-config.yml.template"
                              }
                          ],
    "path_references":  [

                        ],
    "documentation_links":  [
                                {
                                    "linked_document":  "ARCHITECTURE.md",
                                    "new_location":  "Documentation\\ARCHITECTURE.md",
                                    "file":  "Self_Healer\\Documentation\\DesignPrincipals.md"
                                },
                                {
                                    "linked_document":  "ARCHITECTURE.md",
                                    "new_location":  "Documentation\\ARCHITECTURE.md",
                                    "file":  "Self_Healer\\Documentation\\INDEX.md"
                                },
                                {
                                    "linked_document":  "ARCHITECTURE.md",
                                    "new_location":  "Documentation\\ARCHITECTURE.md",
                                    "file":  "Self_Healer\\Documentation\\KnowledgeBaseReadMe.md"
                                },
                                {
                                    "linked_document":  "ARCHITECTURE.md",
                                    "new_location":  "Documentation\\ARCHITECTURE.md",
                                    "file":  "Self_Healer\\Documentation\\README.md"
                                },
                                {
                                    "linked_document":  "ARCHITECTURE.md",
                                    "new_location":  "Documentation\\ARCHITECTURE.md",
                                    "file":  "Self_Healer\\ARCHITECTURE.md"
                                },
                                {
                                    "linked_document":  "ARCHITECTURE.md",
                                    "new_location":  "Documentation\\ARCHITECTURE.md",
                                    "file":  "Self_Healer\\README.md"
                                },
                                {
                                    "linked_document":  "GITHUB_SETUP_INSTRUCTIONS.md",
                                    "new_location":  "Documentation\\GITHUB_SETUP_INSTRUCTIONS.md",
                                    "file":  "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md"
                                },
                                {
                                    "linked_document":  "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md",
                                    "new_location":  "Documentation\\PUBLIC_PRIVATE_SEPARATION_COMPLETE.md",
                                    "file":  "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md"
                                }
                            ],
    "summary":  {
                    "total_python_imports":  0,
                    "total_config_references":  11,
                    "total_script_references":  51,
                    "files_requiring_updates":  [
                                                    "analyze-file-dependencies.ps1",
                                                    "cleanup-root-folder.ps1",
                                                    "deploy_public.ps1",
                                                    "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md",
                                                    "Emergency-Shutdown.ps1",
                                                    "GETTING_STARTED.md",
                                                    "GITHUB_SETUP_INSTRUCTIONS.md",
                                                    "project_analysis_report.json",
                                                    "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md",
                                                    "reorganize-root-folder.ps1",
                                                    "restore_n8n_setup.ps1",
                                                    "safe_project_analysis_20250627_232355.json",
                                                    "safe_project_analysis_20250628_014002.json",
                                                    "Self_Healer\\ARCHITECTURE.md",
                                                    "Self_Healer\\Documentation\\DesignPrincipals.md",
                                                    "Self_Healer\\Documentation\\INDEX.md",
                                                    "Self_Healer\\Documentation\\KnowledgeBaseReadMe.md",
                                                    "Self_Healer\\Documentation\\README.md",
                                                    "Self_Healer\\README.md",
                                                    "shutdown.bat",
                                                    "sync-public.ps1",
                                                    "SYSTEMATIC_REMEDIATION_PLAN.md"
                                                ],
                    "categories_affected":  {
                                                "Scripts":  51,
                                                "data":  0,
                                                "config":  11,
                                                "Documentation":  8,
                                                "archive":  0,
                                                "n8n_builder":  0
                                            },
                    "total_documentation_links":  8
                },
    "scan_timestamp":  "2025-07-04 16:20:04",
    "python_imports":  [

                       ],
    "script_references":  [
                              {
                                  "new_location":  "Scripts\\run_with_venv.bat",
                                  "reference_pattern":  "run_with_venv.bat",
                                  "file":  "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md",
                                  "referenced_script":  "run_with_venv.bat"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.ps1",
                                  "reference_pattern":  "run_with_venv.ps1",
                                  "file":  "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md",
                                  "referenced_script":  "run_with_venv.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\cleanup-root-folder.ps1",
                                  "reference_pattern":  "cleanup-root-folder.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "cleanup-root-folder.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\comprehensive-audit.ps1",
                                  "reference_pattern":  "comprehensive-audit.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "comprehensive-audit.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\consolidate-self-healer.ps1",
                                  "reference_pattern":  "consolidate-self-healer.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "consolidate-self-healer.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\deploy_public.ps1",
                                  "reference_pattern":  "deploy_public.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "deploy_public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\Emergency-Shutdown.ps1",
                                  "reference_pattern":  "Emergency-Shutdown.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "Emergency-Shutdown.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\restore_n8n_setup.ps1",
                                  "reference_pattern":  "restore_n8n_setup.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "restore_n8n_setup.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\sync-public.ps1",
                                  "reference_pattern":  "sync-public.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "sync-public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\verify-public-clean.ps1",
                                  "reference_pattern":  "verify-public-clean.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "verify-public-clean.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.bat",
                                  "reference_pattern":  "run_with_venv.bat",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "run_with_venv.bat"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.ps1",
                                  "reference_pattern":  "run_with_venv.ps1",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "run_with_venv.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.bat",
                                  "reference_pattern":  "shutdown.bat",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.py",
                                  "reference_pattern":  "shutdown.py",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "shutdown.py"
                              },
                              {
                                  "new_location":  "Scripts\\emergency_shutdown.bat",
                                  "reference_pattern":  "emergency_shutdown.bat",
                                  "file":  "analyze-file-dependencies.ps1",
                                  "referenced_script":  "emergency_shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\comprehensive-audit.ps1",
                                  "reference_pattern":  "comprehensive-audit.ps1",
                                  "file":  "cleanup-root-folder.ps1",
                                  "referenced_script":  "comprehensive-audit.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\deploy_public.ps1",
                                  "reference_pattern":  "deploy_public.ps1",
                                  "file":  "deploy_public.ps1",
                                  "referenced_script":  "deploy_public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\Emergency-Shutdown.ps1",
                                  "reference_pattern":  "Emergency-Shutdown.ps1",
                                  "file":  "Emergency-Shutdown.ps1",
                                  "referenced_script":  "Emergency-Shutdown.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.bat",
                                  "reference_pattern":  "run_with_venv.bat",
                                  "file":  "GETTING_STARTED.md",
                                  "referenced_script":  "run_with_venv.bat"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.ps1",
                                  "reference_pattern":  "run_with_venv.ps1",
                                  "file":  "GETTING_STARTED.md",
                                  "referenced_script":  "run_with_venv.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\sync-public.ps1",
                                  "reference_pattern":  "sync-public.ps1",
                                  "file":  "GITHUB_SETUP_INSTRUCTIONS.md",
                                  "referenced_script":  "sync-public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\verify-public-clean.ps1",
                                  "reference_pattern":  "verify-public-clean.ps1",
                                  "file":  "GITHUB_SETUP_INSTRUCTIONS.md",
                                  "referenced_script":  "verify-public-clean.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\sync-public.ps1",
                                  "reference_pattern":  "sync-public.ps1",
                                  "file":  "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md",
                                  "referenced_script":  "sync-public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\verify-public-clean.ps1",
                                  "reference_pattern":  "verify-public-clean.ps1",
                                  "file":  "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md",
                                  "referenced_script":  "verify-public-clean.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\cleanup-root-folder.ps1",
                                  "reference_pattern":  "cleanup-root-folder.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "cleanup-root-folder.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\comprehensive-audit.ps1",
                                  "reference_pattern":  "comprehensive-audit.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "comprehensive-audit.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\consolidate-self-healer.ps1",
                                  "reference_pattern":  "consolidate-self-healer.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "consolidate-self-healer.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\deploy_public.ps1",
                                  "reference_pattern":  "deploy_public.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "deploy_public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\Emergency-Shutdown.ps1",
                                  "reference_pattern":  "Emergency-Shutdown.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "Emergency-Shutdown.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\restore_n8n_setup.ps1",
                                  "reference_pattern":  "restore_n8n_setup.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "restore_n8n_setup.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\sync-public.ps1",
                                  "reference_pattern":  "sync-public.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "sync-public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\verify-public-clean.ps1",
                                  "reference_pattern":  "verify-public-clean.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "verify-public-clean.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.bat",
                                  "reference_pattern":  "run_with_venv.bat",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "run_with_venv.bat"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.ps1",
                                  "reference_pattern":  "run_with_venv.ps1",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "run_with_venv.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.bat",
                                  "reference_pattern":  "shutdown.bat",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.py",
                                  "reference_pattern":  "shutdown.py",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "shutdown.py"
                              },
                              {
                                  "new_location":  "Scripts\\emergency_shutdown.bat",
                                  "reference_pattern":  "emergency_shutdown.bat",
                                  "file":  "reorganize-root-folder.ps1",
                                  "referenced_script":  "emergency_shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\restore_n8n_setup.ps1",
                                  "reference_pattern":  "restore_n8n_setup.ps1",
                                  "file":  "restore_n8n_setup.ps1",
                                  "referenced_script":  "restore_n8n_setup.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.bat",
                                  "reference_pattern":  "shutdown.bat",
                                  "file":  "shutdown.bat",
                                  "referenced_script":  "shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.py",
                                  "reference_pattern":  "shutdown.py",
                                  "file":  "shutdown.bat",
                                  "referenced_script":  "shutdown.py"
                              },
                              {
                                  "new_location":  "Scripts\\emergency_shutdown.bat",
                                  "reference_pattern":  "emergency_shutdown.bat",
                                  "file":  "shutdown.bat",
                                  "referenced_script":  "emergency_shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\Emergency-Shutdown.ps1",
                                  "reference_pattern":  "Emergency-Shutdown.ps1",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "Emergency-Shutdown.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\restore_n8n_setup.ps1",
                                  "reference_pattern":  "restore_n8n_setup.ps1",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "restore_n8n_setup.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\sync-public.ps1",
                                  "reference_pattern":  "sync-public.ps1",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "sync-public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.bat",
                                  "reference_pattern":  "run_with_venv.bat",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "run_with_venv.bat"
                              },
                              {
                                  "new_location":  "Scripts\\run_with_venv.ps1",
                                  "reference_pattern":  "run_with_venv.ps1",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "run_with_venv.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.bat",
                                  "reference_pattern":  "shutdown.bat",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\shutdown.py",
                                  "reference_pattern":  "shutdown.py",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "shutdown.py"
                              },
                              {
                                  "new_location":  "Scripts\\emergency_shutdown.bat",
                                  "reference_pattern":  "emergency_shutdown.bat",
                                  "file":  "sync-public.ps1",
                                  "referenced_script":  "emergency_shutdown.bat"
                              },
                              {
                                  "new_location":  "Scripts\\sync-public.ps1",
                                  "reference_pattern":  "sync-public.ps1",
                                  "file":  "SYSTEMATIC_REMEDIATION_PLAN.md",
                                  "referenced_script":  "sync-public.ps1"
                              },
                              {
                                  "new_location":  "Scripts\\verify-public-clean.ps1",
                                  "reference_pattern":  "verify-public-clean.ps1",
                                  "file":  "SYSTEMATIC_REMEDIATION_PLAN.md",
                                  "referenced_script":  "verify-public-clean.ps1"
                              }
                          ]
}
