# ============================================================================
# CONSOLIDATE SELF-HEALER DIRECTORIES
# ============================================================================
# Moves contents from Self-Healer/ (hyphen) to Self_Healer/ (underscore)
# and removes the duplicate directory
# ============================================================================

param(
    [switch]$DryRun = $false
)

function Write-ConsolidateLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "MOVE" { "Cyan" }
            default { "White" }
        }
    )
}

Write-ConsolidateLog "Consolidating Self-Healer directories..."
Write-ConsolidateLog "Dry Run: $DryRun"

# Check if both directories exist
$hyphenDir = "Self-Healer"
$underscoreDir = "Self_Healer"

if (-not (Test-Path $hyphenDir)) {
    Write-ConsolidateLog "Self-Healer/ directory not found - nothing to consolidate" "INFO"
    exit 0
}

if (-not (Test-Path $underscoreDir)) {
    Write-ConsolidateLog "Self_Healer/ directory not found - creating it" "WARN"
    if (-not $DryRun) {
        New-Item -Path $underscoreDir -ItemType Directory -Force | Out-Null
    }
}

# Get contents of hyphen directory
$hyphenContents = Get-ChildItem -Path $hyphenDir -Recurse

Write-ConsolidateLog "Found $($hyphenContents.Count) items in Self-Healer/ to move"

foreach ($item in $hyphenContents) {
    $relativePath = $item.FullName.Substring((Get-Item $hyphenDir).FullName.Length + 1)
    $targetPath = Join-Path $underscoreDir $relativePath
    
    if ($item.PSIsContainer) {
        # It's a directory
        if ($DryRun) {
            Write-ConsolidateLog "[DRY RUN] Would create directory: $targetPath" "MOVE"
        } else {
            if (-not (Test-Path $targetPath)) {
                New-Item -Path $targetPath -ItemType Directory -Force | Out-Null
                Write-ConsolidateLog "Created directory: $relativePath" "SUCCESS"
            }
        }
    } else {
        # It's a file
        if ($DryRun) {
            Write-ConsolidateLog "[DRY RUN] Would move file: $relativePath" "MOVE"
        } else {
            $targetDir = Split-Path $targetPath -Parent
            if (-not (Test-Path $targetDir)) {
                New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
            }
            
            if (Test-Path $targetPath) {
                Write-ConsolidateLog "Target file exists, overwriting: $relativePath" "WARN"
            }
            
            Copy-Item -Path $item.FullName -Destination $targetPath -Force
            Write-ConsolidateLog "Moved file: $relativePath" "SUCCESS"
        }
    }
}

# Remove the hyphen directory
if ($DryRun) {
    Write-ConsolidateLog "[DRY RUN] Would remove Self-Healer/ directory" "MOVE"
} else {
    Remove-Item -Path $hyphenDir -Recurse -Force
    Write-ConsolidateLog "Removed Self-Healer/ directory" "SUCCESS"
}

Write-ConsolidateLog "Consolidation completed!"

if ($DryRun) {
    Write-ConsolidateLog "This was a dry run. Use without -DryRun to perform actual consolidation." "INFO"
}
