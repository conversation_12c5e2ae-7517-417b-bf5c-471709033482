#!/usr/bin/env python3
"""
Naming Convention Migration Script
==================================
Executes the migration from _public to _community naming convention.
Based on analysis from analyze_naming_convention.py.

Usage: python migrate_naming_convention.py [--dry-run] [--force]
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import json
import shutil
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List

class NamingConventionMigrator:
    """Executes naming convention migration from _public to _community."""
    
    def __init__(self, dry_run: bool = True, force: bool = False):
        self.dry_run = dry_run
        self.force = force
        self.project_root = Path(".")
        self.backup_dir = Path(f"backup_naming_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.migration_log = []
    
    def log_action(self, action: str, details: str = ""):
        """Log migration action."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {action}"
        if details:
            log_entry += f": {details}"
        
        self.migration_log.append(log_entry)
        print(log_entry)
    
    def create_backup(self, file_path: Path) -> bool:
        """Create backup of file before migration."""
        if not file_path.exists():
            return False
        
        if not self.backup_dir.exists():
            if not self.dry_run:
                self.backup_dir.mkdir()
            self.log_action("CREATE_BACKUP_DIR", str(self.backup_dir))
        
        backup_path = self.backup_dir / file_path.name
        
        if not self.dry_run:
            shutil.copy2(file_path, backup_path)
        
        self.log_action("BACKUP", f"{file_path} → {backup_path}")
        return True
    
    def rename_file(self, old_path: Path, new_path: Path) -> bool:
        """Rename file with backup."""
        if not old_path.exists():
            self.log_action("ERROR", f"Source file does not exist: {old_path}")
            return False
        
        if new_path.exists() and not self.force:
            self.log_action("ERROR", f"Target file already exists: {new_path} (use --force to overwrite)")
            return False
        
        # Create backup
        self.create_backup(old_path)
        
        # Perform rename
        if not self.dry_run:
            old_path.rename(new_path)
        
        self.log_action("RENAME", f"{old_path} → {new_path}")
        return True
    
    def load_analysis(self, analysis_file: str = "naming_convention_analysis.json") -> Dict:
        """Load analysis results."""
        try:
            with open(analysis_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.log_action("ERROR", f"Analysis file not found: {analysis_file}")
            self.log_action("INFO", "Run: python analyze_naming_convention.py first")
            return {}
    
    def execute_renames(self, analysis: Dict) -> bool:
        """Execute file renames based on analysis."""
        if not analysis:
            return False
        
        migration_plan = analysis.get("migration_plan", {})
        success_count = 0
        total_renames = 0
        
        for base_name, plan in migration_plan.items():
            action = plan["recommended_action"]
            
            if action == "rename_public_to_community":
                total_renames += 1
                public_path = Path(plan["public_file"]["path"])
                new_path = Path(public_path.name.replace("_public", "_community"))
                
                if self.rename_file(public_path, new_path):
                    success_count += 1
        
        self.log_action("SUMMARY", f"Renamed {success_count}/{total_renames} files")
        return success_count == total_renames
    
    def update_sync_scripts(self) -> bool:
        """Update sync scripts to use _community files instead of _public."""
        scripts_to_update = [
            "sync-public.ps1",
            "Scripts/public_repo_config.json"
        ]
        
        updates_made = 0
        
        for script_path in scripts_to_update:
            script_file = Path(script_path)
            if script_file.exists():
                self.log_action("UPDATE_SCRIPT", f"Would update {script_path} to use _community files")
                updates_made += 1
            else:
                self.log_action("WARNING", f"Script not found: {script_path}")
        
        return updates_made > 0
    
    def verify_migration(self) -> bool:
        """Verify migration was successful."""
        # Check that _public files are gone and _community files exist
        expected_community_files = [
            "requirements_community.txt",
            "run_community.py", 
            "setup_community.py",
            "config_community.yaml"
        ]
        
        verification_passed = True
        
        for file_name in expected_community_files:
            file_path = Path(file_name)
            if self.dry_run:
                self.log_action("VERIFY", f"Would check existence of {file_path}")
            else:
                if file_path.exists():
                    self.log_action("VERIFY_OK", str(file_path))
                else:
                    self.log_action("VERIFY_FAIL", f"Missing: {file_path}")
                    verification_passed = False
        
        return verification_passed
    
    def run_migration(self) -> bool:
        """Run complete migration process."""
        self.log_action("START", f"Naming convention migration (dry_run={self.dry_run})")
        
        # Load analysis
        analysis = self.load_analysis()
        if not analysis:
            return False
        
        # Execute renames
        if not self.execute_renames(analysis):
            self.log_action("ERROR", "File renames failed")
            return False
        
        # Update scripts (placeholder for now)
        self.update_sync_scripts()
        
        # Verify migration
        if not self.verify_migration():
            self.log_action("ERROR", "Migration verification failed")
            return False
        
        self.log_action("SUCCESS", "Migration completed successfully")
        return True
    
    def save_log(self, log_file: str = "naming_migration.log"):
        """Save migration log."""
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(self.migration_log))
        print(f"[{datetime.now().strftime('%H:%M:%S')}] LOG_SAVED: {log_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Migrate naming convention from _public to _community")
    parser.add_argument("--dry-run", action="store_true", default=True, help="Show what would be done without executing")
    parser.add_argument("--execute", action="store_true", help="Actually execute the migration")
    parser.add_argument("--force", action="store_true", help="Overwrite existing files")
    
    args = parser.parse_args()
    
    # If --execute is specified, turn off dry-run
    dry_run = not args.execute
    
    migrator = NamingConventionMigrator(dry_run=dry_run, force=args.force)
    success = migrator.run_migration()
    migrator.save_log()
    
    if dry_run:
        print("\n🔍 This was a DRY RUN. Use --execute to perform actual migration.")
    
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
