#!/usr/bin/env python3
"""
Documentation Consolidation Report Generator

Converts the JSON consolidation plan into a human-readable markdown report
with actionable tasks and recommendations.

Author: N8N_Builder Development Team
Date: 2025-07-08
"""

import json
from pathlib import Path
from datetime import datetime

def generate_markdown_report(json_file: str, output_file: str):
    """Generate a markdown report from the JSON consolidation plan"""
    
    with open(json_file, 'r', encoding='utf-8') as f:
        plan = json.load(f)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# N8N_Builder Documentation Consolidation Plan\n\n")
        f.write(f"**Generated:** {plan['analysis_date']}\n\n")
        
        # Executive Summary
        summary = plan['summary']
        f.write("## 📊 Executive Summary\n\n")
        f.write("### Current State\n")
        f.write("- **75 Markdown files** across 23 folders\n")
        f.write("- **Nearly 3,000 headers** with significant redundancy\n")
        f.write("- **345,000+ bullet points** indicating documentation bloat\n\n")
        
        f.write("### Issues Identified\n")
        f.write(f"- **{summary['total_redundancy_issues']} redundancy issues** found\n")
        f.write(f"- **{summary['high_priority_issues']} immediate priority** items\n")
        f.write(f"- **{summary['consolidation_groups']} consolidation groups** identified\n")
        f.write(f"- **{summary['total_action_items']} action items** total\n")
        f.write(f"- **{summary['estimated_total_time']} minutes** estimated work ({summary['estimated_total_time']//60} hours)\n\n")
        
        # Critical Issues
        f.write("## 🚨 Critical Redundancy Issues (Immediate Action Required)\n\n")
        immediate_issues = [issue for issue in plan['issues_identified'] 
                          if issue['consolidation_priority'] == 'immediate']
        
        for issue in immediate_issues:
            f.write(f"### {issue['header'].title()}\n")
            f.write(f"- **Occurrences:** {issue['occurrences']} times\n")
            f.write(f"- **Impact:** High - Creates user confusion\n")
            f.write(f"- **Action:** Consolidate into single authoritative section\n\n")
        
        # Consolidation Groups
        f.write("## 📋 Consolidation Groups\n\n")
        for i, group in enumerate(plan['consolidation_groups'], 1):
            f.write(f"### {i}. {group['group_name']}\n")
            f.write(f"- **Target:** `{group['target_file']}`\n")
            f.write(f"- **Priority:** {group['priority'].title()}\n")
            f.write(f"- **Files to merge:** {len(group['files_to_merge'])}\n")
            f.write(f"- **Rationale:** {group['rationale']}\n\n")
            
            f.write("**Files involved:**\n")
            for file_info in group['files_to_merge'][:10]:  # Show first 10
                f.write(f"- `{file_info['path']}` ({file_info['headers']} headers)\n")
            if len(group['files_to_merge']) > 10:
                f.write(f"- ... and {len(group['files_to_merge']) - 10} more files\n")
            f.write("\n")
        
        # Action Items by Priority
        f.write("## ✅ Action Items by Priority\n\n")
        
        # Group action items by priority
        high_priority = [item for item in plan['action_items'] if item['priority'] == 'high']
        medium_priority = [item for item in plan['action_items'] if item['priority'] == 'medium']
        low_priority = [item for item in plan['action_items'] if item['priority'] == 'low']
        
        f.write("### High Priority (Do First)\n\n")
        for i, item in enumerate(high_priority, 1):
            f.write(f"{i}. **{item['task']}**\n")
            f.write(f"   - {item['description']}\n")
            f.write(f"   - Time: {item['estimated_time']}\n")
            f.write(f"   - Type: {item['type'].replace('_', ' ').title()}\n")
            if 'files_involved' in item:
                f.write(f"   - Files: {len(item['files_involved'])} files\n")
            f.write("\n")
        
        if medium_priority:
            f.write("### Medium Priority (Do Second)\n\n")
            for i, item in enumerate(medium_priority, 1):
                f.write(f"{i}. **{item['task']}**\n")
                f.write(f"   - {item['description']}\n")
                f.write(f"   - Time: {item['estimated_time']}\n\n")
        
        if low_priority:
            f.write("### Low Priority (Do Last)\n\n")
            for i, item in enumerate(low_priority, 1):
                f.write(f"{i}. **{item['task']}**\n")
                f.write(f"   - {item['description']}\n")
                f.write(f"   - Time: {item['estimated_time']}\n\n")
        
        # Recommended Approach
        f.write("## 🎯 Recommended Consolidation Approach\n\n")
        f.write("### Phase 1: Critical Redundancy (Week 1)\n")
        f.write("1. **Consolidate README sections** - Create single authoritative README\n")
        f.write("2. **Merge Prerequisites** - Single prerequisites section in GETTING_STARTED.md\n")
        f.write("3. **Unify Overview sections** - Consistent project overview\n")
        f.write("4. **Consolidate Common Issues** - Single troubleshooting guide\n\n")
        
        f.write("### Phase 2: File Consolidation (Week 2)\n")
        f.write("1. **Setup Documentation** - Merge all setup guides into GETTING_STARTED.md\n")
        f.write("2. **Technical Documentation** - Consolidate into Documentation/Architecture.md\n")
        f.write("3. **Troubleshooting** - Create comprehensive troubleshooting guide\n\n")
        
        f.write("### Phase 3: Cleanup (Week 3)\n")
        f.write("1. **Remove obsolete files** - Delete minimal/duplicate content\n")
        f.write("2. **Update cross-references** - Fix broken links\n")
        f.write("3. **Validate structure** - Ensure hierarchical organization\n\n")
        
        # Target Structure
        f.write("## 🏗️ Target Documentation Structure\n\n")
        f.write("```\n")
        f.write("ROOT/\n")
        f.write("├── README.md                    # Project overview & quick start\n")
        f.write("├── GETTING_STARTED.md          # Comprehensive setup guide\n")
        f.write("├── FEATURES.md                 # Feature overview\n")
        f.write("└── Documentation/\n")
        f.write("    ├── Architecture.md         # Technical architecture\n")
        f.write("    ├── DesignPrinciples.md    # Design philosophy\n")
        f.write("    ├── DevelopersWorkflow.md   # Developer guide\n")
        f.write("    ├── guides/\n")
        f.write("    │   ├── Troubleshooting.md  # Consolidated troubleshooting\n")
        f.write("    │   └── Integration.md      # Integration guides\n")
        f.write("    ├── technical/\n")
        f.write("    │   └── Specifications.md   # Detailed technical specs\n")
        f.write("    └── api/\n")
        f.write("        └── API_Reference.md    # API documentation\n")
        f.write("```\n\n")
        
        # Success Metrics
        f.write("## 📈 Success Metrics\n\n")
        f.write("**Target Reduction:**\n")
        f.write("- Files: 75 → ~15 (80% reduction)\n")
        f.write("- Headers: 3,000 → ~200 (93% reduction)\n")
        f.write("- Redundant sections: 58 → 0 (100% elimination)\n\n")
        
        f.write("**Quality Improvements:**\n")
        f.write("- Single source of truth for each topic\n")
        f.write("- Clear hierarchical structure\n")
        f.write("- Reduced maintenance overhead\n")
        f.write("- Improved user experience\n\n")
        
        f.write("---\n\n")
        f.write("*This report was generated automatically from the documentation analysis.*\n")
        f.write("*Review and adjust priorities based on current project needs.*\n")

def main():
    """Main execution function"""
    json_file = "data/documentation_consolidation_plan.json"
    output_file = "data/Documentation_Consolidation_Report.md"
    
    if not Path(json_file).exists():
        print(f"Error: Plan file not found at {json_file}")
        return
    
    print("Generating human-readable consolidation report...")
    generate_markdown_report(json_file, output_file)
    print(f"Report generated: {output_file}")

if __name__ == "__main__":
    main()
