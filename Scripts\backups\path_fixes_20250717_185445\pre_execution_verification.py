#!/usr/bin/env python3
"""
Pre-Execution Verification for Phase 3 Separation
=================================================
Comprehensive verification to ensure all systems are ready for separation execution.

Usage: python pre_execution_verification.py [--verbose] [--fix-issues]
"""

import os
import json
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class PreExecutionVerifier:
    """Comprehensive pre-execution verification system."""
    
    def __init__(self, verbose: bool = False, fix_issues: bool = False):
        self.verbose = verbose
        self.fix_issues = fix_issues
        self.verification_start_time = datetime.now()
        self.results = {
            "verification_metadata": {
                "start_time": self.verification_start_time.isoformat(),
                "fix_issues_enabled": fix_issues,
                "checks_run": [],
                "checks_passed": [],
                "checks_failed": [],
                "issues_found": [],
                "issues_fixed": []
            },
            "verification_results": {}
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        if self.verbose or level in ["WARNING", "ERROR", "SUCCESS"]:
            print(log_entry)
    
    def run_command(self, command: str, cwd: str = None, timeout: int = 60) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", f"Command timed out after {timeout} seconds"
        except Exception as e:
            return False, "", str(e)
    
    def check_phase1_components(self) -> Dict[str, Any]:
        """Verify Phase 1 verification system components."""
        check_name = "phase1_components"
        self.log("=== CHECKING PHASE 1 COMPONENTS ===")
        
        result = {
            "check_name": check_name,
            "success": True,
            "details": {},
            "issues": []
        }
        
        # Check detection scripts
        detection_files = [
            "detect_private_components.py",
            "detect-private-components.ps1"
        ]
        
        for file_path in detection_files:
            exists = Path(file_path).exists()
            result["details"][f"{file_path}_exists"] = exists
            if not exists:
                result["success"] = False
                result["issues"].append(f"Missing detection script: {file_path}")
        
        # Check verification pipeline
        pipeline_file = "verification_pipeline.py"
        exists = Path(pipeline_file).exists()
        result["details"]["verification_pipeline_exists"] = exists
        if not exists:
            result["success"] = False
            result["issues"].append(f"Missing verification pipeline: {pipeline_file}")
        
        # Check manual review checklist
        checklist_file = "Documentation/MANUAL_REVIEW_CHECKLIST.md"
        exists = Path(checklist_file).exists()
        result["details"]["manual_checklist_exists"] = exists
        if not exists:
            result["success"] = False
            result["issues"].append(f"Missing manual checklist: {checklist_file}")
        
        if result["success"]:
            self.log("✅ Phase 1 components verification passed")
        else:
            self.log(f"❌ Phase 1 components verification failed: {len(result['issues'])} issues", "ERROR")
        
        return result
    
    def check_phase2_components(self) -> Dict[str, Any]:
        """Verify Phase 2 enhanced sync components."""
        check_name = "phase2_components"
        self.log("=== CHECKING PHASE 2 COMPONENTS ===")
        
        result = {
            "check_name": check_name,
            "success": True,
            "details": {},
            "issues": []
        }
        
        # Check enhanced sync script
        sync_script = "sync-public.ps1"
        if Path(sync_script).exists():
            with open(sync_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for enhanced features
            required_features = [
                "Invoke-PreSyncVerification",
                "Invoke-PostSyncVerification", 
                "Remove-PrivateReferences",
                "New-BackupIfExists"
            ]
            
            missing_features = []
            for feature in required_features:
                if feature not in content:
                    missing_features.append(feature)
            
            result["details"]["sync_script_enhanced"] = len(missing_features) == 0
            if missing_features:
                result["success"] = False
                result["issues"].append(f"Sync script missing features: {missing_features}")
        else:
            result["success"] = False
            result["issues"].append("Enhanced sync script not found")
        
        # Check public repo configuration
        config_file = "Scripts/public_repo_config.json"
        if Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                result["details"]["config_valid"] = True
            except json.JSONDecodeError:
                result["success"] = False
                result["issues"].append("Public repo config has invalid JSON")
        else:
            result["success"] = False
            result["issues"].append("Public repo configuration not found")
        
        # Check documentation sanitizer
        sanitizer_file = "sanitize_documentation.py"
        exists = Path(sanitizer_file).exists()
        result["details"]["sanitizer_exists"] = exists
        if not exists:
            result["success"] = False
            result["issues"].append("Documentation sanitizer not found")
        
        # Check Community Edition README
        readme_file = "README_community.md"
        exists = Path(readme_file).exists()
        result["details"]["community_readme_exists"] = exists
        if not exists:
            result["success"] = False
            result["issues"].append("Community Edition README not found")
        
        if result["success"]:
            self.log("✅ Phase 2 components verification passed")
        else:
            self.log(f"❌ Phase 2 components verification failed: {len(result['issues'])} issues", "ERROR")
        
        return result
    
    def check_public_files_ready(self) -> Dict[str, Any]:
        """Verify all public files are ready for sync."""
        check_name = "public_files_ready"
        self.log("=== CHECKING PUBLIC FILES READINESS ===")
        
        result = {
            "check_name": check_name,
            "success": True,
            "details": {},
            "issues": []
        }
        
        # Check public suffix files
        public_suffix_files = {
            "README_public.md": "README.md",
            "requirements_public.txt": "requirements.txt", 
            "run_public.py": "run.py",
            "setup_public.py": "setup.py",
            "config_public.yaml": "config.yaml",
            ".gitignore_public": ".gitignore",
            ".augment-guidelines-public": ".augment-guidelines"
        }
        
        missing_files = []
        for source_file in public_suffix_files.keys():
            if not Path(source_file).exists():
                missing_files.append(source_file)
        
        result["details"]["public_suffix_files_ready"] = len(missing_files) == 0
        result["details"]["missing_public_files"] = missing_files
        
        if missing_files:
            result["success"] = False
            result["issues"].append(f"Missing public files: {missing_files}")
        
        # Check critical directories
        critical_dirs = ["n8n_builder", "Documentation", "static", "n8n-docker"]
        missing_dirs = []
        for dir_name in critical_dirs:
            if not Path(dir_name).exists():
                missing_dirs.append(dir_name)
        
        result["details"]["critical_dirs_ready"] = len(missing_dirs) == 0
        result["details"]["missing_dirs"] = missing_dirs
        
        if missing_dirs:
            result["success"] = False
            result["issues"].append(f"Missing critical directories: {missing_dirs}")
        
        if result["success"]:
            self.log("✅ Public files readiness verification passed")
        else:
            self.log(f"❌ Public files readiness verification failed: {len(result['issues'])} issues", "ERROR")
        
        return result
    
    def check_private_component_detection(self) -> Dict[str, Any]:
        """Run private component detection on current repository."""
        check_name = "private_component_detection"
        self.log("=== RUNNING PRIVATE COMPONENT DETECTION ===")
        
        result = {
            "check_name": check_name,
            "success": False,
            "details": {},
            "issues": []
        }
        
        # Run detection script
        detection_cmd = "python detect_private_components.py --path . --output pre_execution_detection.json"
        success, stdout, stderr = self.run_command(detection_cmd, timeout=120)
        
        if success and Path("pre_execution_detection.json").exists():
            try:
                with open("pre_execution_detection.json", 'r') as f:
                    detection_results = json.load(f)
                
                total_references = detection_results["scan_metadata"]["total_references"]
                files_with_refs = detection_results["scan_metadata"]["files_with_references"]
                
                result["details"]["detection_executed"] = True
                result["details"]["total_references"] = total_references
                result["details"]["files_with_references"] = files_with_refs
                
                if total_references > 0:
                    result["issues"].append(f"Found {total_references} private references in {files_with_refs} files")
                    self.log(f"⚠️ Found {total_references} private references - review pre_execution_detection.json", "WARNING")
                else:
                    result["success"] = True
                    self.log("✅ No private component references detected")
                
            except json.JSONDecodeError:
                result["issues"].append("Detection results file has invalid JSON")
        else:
            result["issues"].append(f"Detection script failed: {stderr}")
        
        return result
    
    def check_system_dependencies(self) -> Dict[str, Any]:
        """Check system dependencies and tools."""
        check_name = "system_dependencies"
        self.log("=== CHECKING SYSTEM DEPENDENCIES ===")
        
        result = {
            "check_name": check_name,
            "success": True,
            "details": {},
            "issues": []
        }
        
        # Check Python
        success, stdout, stderr = self.run_command("python --version")
        result["details"]["python_available"] = success
        if success:
            result["details"]["python_version"] = stdout.strip()
        else:
            result["success"] = False
            result["issues"].append("Python not available")
        
        # Check PowerShell
        success, stdout, stderr = self.run_command("powershell -Command 'Get-Host | Select-Object Version'")
        result["details"]["powershell_available"] = success
        if not success:
            result["success"] = False
            result["issues"].append("PowerShell not available")
        
        # Check Git
        success, stdout, stderr = self.run_command("git --version")
        result["details"]["git_available"] = success
        if success:
            result["details"]["git_version"] = stdout.strip()
        else:
            result["success"] = False
            result["issues"].append("Git not available")
        
        if result["success"]:
            self.log("✅ System dependencies verification passed")
        else:
            self.log(f"❌ System dependencies verification failed: {len(result['issues'])} issues", "ERROR")
        
        return result
    
    def check_disk_space(self) -> Dict[str, Any]:
        """Check available disk space for separation process."""
        check_name = "disk_space"
        self.log("=== CHECKING DISK SPACE ===")
        
        result = {
            "check_name": check_name,
            "success": True,
            "details": {},
            "issues": []
        }
        
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            
            # Convert to GB
            free_gb = free / (1024**3)
            total_gb = total / (1024**3)
            
            result["details"]["free_space_gb"] = round(free_gb, 2)
            result["details"]["total_space_gb"] = round(total_gb, 2)
            
            # Require at least 2GB free space
            if free_gb < 2.0:
                result["success"] = False
                result["issues"].append(f"Insufficient disk space: {free_gb:.2f}GB free (minimum 2GB required)")
            else:
                self.log(f"✅ Sufficient disk space: {free_gb:.2f}GB free")
        
        except Exception as e:
            result["success"] = False
            result["issues"].append(f"Could not check disk space: {e}")
        
        return result
    
    def run_comprehensive_verification(self) -> Dict[str, Any]:
        """Run all pre-execution verification checks."""
        self.log("🔍 Starting Comprehensive Pre-Execution Verification")
        
        verification_checks = [
            self.check_phase1_components,
            self.check_phase2_components,
            self.check_public_files_ready,
            self.check_private_component_detection,
            self.check_system_dependencies,
            self.check_disk_space
        ]
        
        overall_success = True
        
        for check_method in verification_checks:
            try:
                check_result = check_method()
                check_name = check_result["check_name"]
                
                self.results["verification_results"][check_name] = check_result
                self.results["verification_metadata"]["checks_run"].append(check_name)
                
                if check_result["success"]:
                    self.results["verification_metadata"]["checks_passed"].append(check_name)
                else:
                    self.results["verification_metadata"]["checks_failed"].append(check_name)
                    self.results["verification_metadata"]["issues_found"].extend(check_result["issues"])
                    overall_success = False
            
            except Exception as e:
                self.log(f"❌ Verification check crashed: {check_method.__name__} - {e}", "ERROR")
                self.results["verification_metadata"]["checks_failed"].append(check_method.__name__)
                overall_success = False
        
        # Update final metadata
        self.results["verification_metadata"]["end_time"] = datetime.now().isoformat()
        self.results["verification_metadata"]["duration_seconds"] = (datetime.now() - self.verification_start_time).total_seconds()
        self.results["verification_metadata"]["overall_success"] = overall_success
        
        # Display summary
        self.display_verification_summary()
        
        return self.results
    
    def display_verification_summary(self):
        """Display verification summary."""
        print("\n" + "="*70)
        print("🔍 PRE-EXECUTION VERIFICATION SUMMARY")
        print("="*70)
        
        metadata = self.results["verification_metadata"]
        total_checks = len(metadata["checks_run"])
        passed_checks = len(metadata["checks_passed"])
        failed_checks = len(metadata["checks_failed"])
        
        print(f"⏱️  Duration: {metadata['duration_seconds']:.2f} seconds")
        print(f"🔍 Total Checks: {total_checks}")
        print(f"✅ Passed: {passed_checks}")
        print(f"❌ Failed: {failed_checks}")
        print(f"📊 Success Rate: {(passed_checks/total_checks*100):.1f}%" if total_checks > 0 else "📊 Success Rate: 0%")
        
        if metadata["overall_success"]:
            print("\n🎉 ALL VERIFICATION CHECKS PASSED!")
            print("✅ System is ready for Phase 3 separation execution")
            print("🚀 You can proceed with confidence")
        else:
            print(f"\n⚠️  {failed_checks} VERIFICATION CHECKS FAILED")
            print("❌ System is NOT ready for separation execution")
            print("🔧 Address the following issues before proceeding:")
            
            for i, issue in enumerate(metadata["issues_found"], 1):
                print(f"   {i}. {issue}")
            
            print(f"\n📋 Failed Checks:")
            for check in metadata["checks_failed"]:
                print(f"   - {check}")
        
        print("="*70)
    
    def export_results(self, output_file: str = "pre_execution_verification_results.json"):
        """Export verification results to JSON file."""
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.log(f"Verification results exported to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Pre-Execution Verification for Phase 3")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--fix-issues", action="store_true", help="Attempt to fix issues automatically")
    parser.add_argument("--output", default="pre_execution_verification_results.json", help="Output file")
    
    args = parser.parse_args()
    
    # Initialize verifier
    verifier = PreExecutionVerifier(args.verbose, args.fix_issues)
    
    # Run verification
    results = verifier.run_comprehensive_verification()
    
    # Export results
    verifier.export_results(args.output)
    
    # Exit with appropriate code
    if results["verification_metadata"]["overall_success"]:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
