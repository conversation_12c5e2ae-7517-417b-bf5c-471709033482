# ============================================================================
# ROOT FOLDER CLEANUP SCRIPT
# ============================================================================
# Moves private component files from root level to proper Self_Healer structure
# This prepares the repository for clean public/private separation
# ============================================================================

param(
    [switch]$DryRun = $false,
    [switch]$Force = $false
)

$LogFile = "cleanup-root-folder.log"

function Write-CleanupLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry -ForegroundColor $(
        switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "MOVE" { "Cyan" }
            default { "White" }
        }
    )
    Add-Content -Path $LogFile -Value $logEntry
}

function Ensure-Directory {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        if ($DryRun) {
            Write-CleanupLog "[DRY RUN] Would create directory: $Path" "INFO"
        } else {
            New-Item -Path $Path -ItemType Directory -Force | Out-Null
            Write-CleanupLog "Created directory: $Path" "SUCCESS"
        }
    }
}

function Move-FileToLocation {
    param(
        [string]$SourceFile,
        [string]$TargetDirectory,
        [string]$Category
    )
    
    if (-not (Test-Path $SourceFile)) {
        Write-CleanupLog "Source file not found: $SourceFile" "WARN"
        return
    }
    
    $fileName = Split-Path $SourceFile -Leaf
    $targetPath = Join-Path $TargetDirectory $fileName
    
    if ($DryRun) {
        Write-CleanupLog "[DRY RUN] Would move [$Category]: $SourceFile → $targetPath" "MOVE"
    } else {
        # Ensure target directory exists
        Ensure-Directory $TargetDirectory
        
        # Check if target file already exists
        if (Test-Path $targetPath) {
            if (-not $Force) {
                Write-CleanupLog "Target file already exists: $targetPath (use -Force to overwrite)" "WARN"
                return
            } else {
                Write-CleanupLog "Overwriting existing file: $targetPath" "WARN"
            }
        }
        
        # Move the file
        Move-Item -Path $SourceFile -Destination $targetPath -Force
        Write-CleanupLog "Moved [$Category]: $fileName → $TargetDirectory" "SUCCESS"
    }
}

# ============================================================================
# FILE MOVEMENT DEFINITIONS
# ============================================================================

# Define the file movements
$FileMoves = @{
    # Diagnostic and Debug Files → Self_Healer/tests/
    "Tests" = @{
        "TargetDir" = "Self_Healer\tests"
        "Files" = @(
            "check_healer_status.py",
            "debug_self_healer.py", 
            "debug_self_healer_flow.py",
            "debug_error_criteria.py",
            "force_error_detection.py"
        )
    }
    
    # Utility Scripts → Self_Healer/utilities/
    "Utilities" = @{
        "TargetDir" = "Self_Healer\utilities"
        "Files" = @(
            "fix_healer_sync.py",
            "cleanup_self_healer.ps1",
            "cleanup_duplicate_folder.ps1"
        )
    }
    
    # Configuration Files → Self_Healer/config/
    "Config" = @{
        "TargetDir" = "Self_Healer\config"
        "Files" = @(
            "config_private_template.yaml"
        )
    }
    
    # Database/Schema Files → Self_Healer/database/
    "Database" = @{
        "TargetDir" = "Self_Healer\database"
        "Files" = @(
            "check_db_state.py",
            "check_schema.py",
            "create_analytics_procedure.py",
            "create_simple_session_procedure.py"
        )
    }
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

Write-CleanupLog "Starting root folder cleanup..."
Write-CleanupLog "Dry Run: $DryRun"
Write-CleanupLog "Force Overwrite: $Force"

# Check if Self_Healer directory exists
if (-not (Test-Path "Self_Healer")) {
    Write-CleanupLog "Self_Healer directory not found - creating it..." "WARN"
    Ensure-Directory "Self_Healer"
}

$totalMoved = 0
$totalSkipped = 0

# Process each category of files
foreach ($category in $FileMoves.Keys) {
    $categoryInfo = $FileMoves[$category]
    $targetDir = $categoryInfo.TargetDir
    $files = $categoryInfo.Files
    
    Write-CleanupLog "Processing category: $category → $targetDir"
    
    foreach ($file in $files) {
        if (Test-Path $file) {
            Move-FileToLocation -SourceFile $file -TargetDirectory $targetDir -Category $category
            $totalMoved++
        } else {
            Write-CleanupLog "File not found (already moved?): $file" "INFO"
            $totalSkipped++
        }
    }
}

# ============================================================================
# CONSOLIDATE DUPLICATE DIRECTORIES
# ============================================================================

Write-CleanupLog "Checking for duplicate Self-Healer directories..."

if (Test-Path "Self-Healer") {
    Write-CleanupLog "Found Self-Healer directory (with hyphen) - need to consolidate"
    
    if ($DryRun) {
        Write-CleanupLog "[DRY RUN] Would consolidate Self-Healer/ → Self_Healer/" "MOVE"
    } else {
        # This is complex - let's just warn for now
        Write-CleanupLog "MANUAL ACTION REQUIRED: Consolidate Self-Healer/ and Self_Healer/ directories" "WARN"
        Write-CleanupLog "Recommend: Move contents of Self-Healer/ to Self_Healer/ and delete Self-Healer/" "WARN"
    }
}

# ============================================================================
# SUMMARY AND VERIFICATION
# ============================================================================

Write-CleanupLog "Cleanup completed!"
Write-CleanupLog "Files moved: $totalMoved"
Write-CleanupLog "Files skipped: $totalSkipped"

if (-not $DryRun) {
    Write-CleanupLog "Verifying cleanup results..."
    
    # Check if any private files remain at root level
    $remainingPrivateFiles = @()
    
    # Check for remaining private patterns at root
    $privatePatterns = @("*healer*", "*knowledge*", "*private*", "*advanced*")
    
    foreach ($pattern in $privatePatterns) {
        $matches = Get-ChildItem -Path "." -Filter $pattern -File -ErrorAction SilentlyContinue
        foreach ($match in $matches) {
            # Skip known public files
            if ($match.Name -notlike "*_public*" -and $match.Name -notlike "sync-public*" -and $match.Name -notlike "verify-public*") {
                $remainingPrivateFiles += $match.Name
            }
        }
    }
    
    if ($remainingPrivateFiles.Count -gt 0) {
        Write-CleanupLog "WARNING: Remaining private files at root level:" "WARN"
        foreach ($file in $remainingPrivateFiles) {
            Write-CleanupLog "  - $file" "WARN"
        }
    } else {
        Write-CleanupLog "✅ No obvious private files remaining at root level" "SUCCESS"
    }
}

Write-CleanupLog "Log saved to: $LogFile"

if ($DryRun) {
    Write-CleanupLog "This was a dry run. Use without -DryRun to perform actual cleanup." "INFO"
} else {
    Write-CleanupLog "Next steps:" "INFO"
    Write-CleanupLog "1. Review moved files in Self_Healer subdirectories" "INFO"
    Write-CleanupLog "2. Consolidate Self-Healer/ and Self_Healer/ if both exist" "INFO"
    Write-CleanupLog "3. Run verification: .\comprehensive-audit.ps1" "INFO"
    Write-CleanupLog "4. Test that N8N_Builder still works: python run.py" "INFO"
}
