# Simple Reverse Proxy for Stable URLs
# This creates a stable localhost:8080 URL that forwards to n8n
# Solves the webhook URL problem without external dependencies

services:
  # Nginx Reverse Proxy - Simple and reliable
  stable-proxy:
    image: nginx:alpine
    container_name: n8n-stable-proxy
    restart: unless-stopped
    ports:
      - "8080:80"  # Stable URL: http://localhost:8080
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - n8n-network

networks:
  n8n-network:
    external: true
    name: n8n-network
