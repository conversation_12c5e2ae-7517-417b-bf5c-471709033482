{"validation_date": "2025-07-16T13:51:37.484164", "total_files": 29, "total_issues": 16, "total_recommendations": 0, "summary": {"total_lines": 9812, "total_words": 45439, "average_readability_score": 8.3, "average_structure_score": 8.1, "files_with_issues": 12, "files_needing_tunnel_updates": 4, "documentation_health": "Good"}, "file_analyses": [{"file_path": "data\\documentation_analysis_report.md", "line_count": 4705, "word_count": 25151, "headers": [{"level": 1, "text": "N8N_Builder Documentation Analysis Report"}, {"level": 2, "text": "Executive Summary"}, {"level": 2, "text": "Documentation Distribution by Folder"}, {"level": 2, "text": "Potential Redundancy Analysis"}, {"level": 2, "text": "Detailed File Analysis"}, {"level": 3, "text": "ROOT"}, {"level": 4, "text": "FEATURES.md"}, {"level": 4, "text": "GETTING_STARTED.md"}, {"level": 4, "text": "LIGHTNING_START.md"}, {"level": 4, "text": "README.md"}, {"level": 4, "text": "README_community.md"}, {"level": 4, "text": "separation_detection.md"}, {"level": 3, "text": "Documentation"}, {"level": 4, "text": "ADVANCED_FEATURES.md"}, {"level": 4, "text": "ARCHITECTURE.md"}, {"level": 4, "text": "DATABASE_INTEGRATION.md"}, {"level": 4, "text": "DevelopersWorkflow.md"}, {"level": 4, "text": "FOLDER_ORGANIZATION.md"}, {"level": 4, "text": "GITHUB_ORGANIZATION_HANDOFF.md"}, {"level": 4, "text": "GITHUB_ORGANIZATION_SUMMARY.md"}, {"level": 4, "text": "GITHUB_ORGANIZATION_TASKS.md"}, {"level": 4, "text": "GITHUB_SETUP_INSTRUCTIONS.md"}, {"level": 4, "text": "MANUAL_REVIEW_CHECKLIST.md"}, {"level": 4, "text": "PHASE1_COMPLETION_SUMMARY.md"}, {"level": 4, "text": "PHASE2_COMPLETION_SUMMARY.md"}, {"level": 4, "text": "PHASE3_COMPLETION_SUMMARY.md"}, {"level": 4, "text": "PUBLIC_PRIVATE_SEPARATION_COMPLETE.md"}, {"level": 4, "text": "README.md"}, {"level": 4, "text": "SERVER_STARTUP_METHODS.md"}, {"level": 4, "text": "SYSTEMATIC_REMEDIATION_PLAN.md"}, {"level": 4, "text": "TROUBLESHOOTING.md"}, {"level": 3, "text": "Documentation\\api"}, {"level": 4, "text": "API_DOCUMENTATION.md"}, {"level": 4, "text": "API_QUICK_REFERENCE.md"}, {"level": 3, "text": "Documentation\\guides"}, {"level": 4, "text": "FIRST_WORKFLOW.md"}, {"level": 4, "text": "INTEGRATION_SETUP.md"}, {"level": 3, "text": "Documentation\\technical"}, {"level": 4, "text": "DOCUMENTATION.md"}, {"level": 4, "text": "PYTHON_ENVIRONMENT_SETUP.md"}, {"level": 4, "text": "ProcessFlow.md"}, {"level": 3, "text": "<PERSON><PERSON><PERSON>"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "Self_Healer"}, {"level": 4, "text": "ARCHITECTURE.md"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "Self_Healer\\Documentation"}, {"level": 4, "text": "ARCHITECTURE.md"}, {"level": 4, "text": "DesignPrincipals.md"}, {"level": 4, "text": "INDEX.md"}, {"level": 4, "text": "INTEGRATION_GUIDE.md"}, {"level": 4, "text": "KnowledgeBaseReadMe.md"}, {"level": 4, "text": "README.md"}, {"level": 4, "text": "SQLConventions.md"}, {"level": 3, "text": "Self_Healer\\Documentation\\DB_Admin"}, {"level": 4, "text": "KnowledgeBaseInfo.md"}, {"level": 3, "text": "data"}, {"level": 4, "text": "documentation_analysis_report.md"}, {"level": 4, "text": "safe_project_analysis_20250627_232355.md"}, {"level": 4, "text": "safe_project_analysis_20250628_014002.md"}, {"level": 3, "text": "n8n-docker"}, {"level": 4, "text": "DOCUMENTATION_CLEANUP_SUMMARY.md"}, {"level": 4, "text": "GETTING_STARTED.md"}, {"level": 4, "text": "LIGHTNING_START.md"}, {"level": 4, "text": "MIGRATION_GUIDE.md"}, {"level": 4, "text": "NGROK_CLEANUP_AUDIT.md"}, {"level": 4, "text": "OAUTH_STABLE_URL_GUIDE.md"}, {"level": 4, "text": "STABLE_URL_ASSESSMENT.md"}, {"level": 3, "text": "n8n-docker\\Documentation"}, {"level": 4, "text": "QUICK_START.md"}, {"level": 4, "text": "README.md"}, {"level": 4, "text": "README_OLD.md"}, {"level": 4, "text": "REORGANIZATION_COMPLETE.md"}, {"level": 4, "text": "USER_JOURNEY_VALIDATION.md"}, {"level": 3, "text": "n8n-docker\\Documentation\\guides"}, {"level": 4, "text": "AUTOMATION_SETUP.md"}, {"level": 4, "text": "CREDENTIALS_SETUP.md"}, {"level": 4, "text": "SECURITY_SETUP.md"}, {"level": 3, "text": "n8n-docker\\Documentation\\technical"}, {"level": 4, "text": "ADVANCED_SECURITY.md"}, {"level": 4, "text": "AUTOMATION_REFERENCE.md"}, {"level": 4, "text": "DOCKER_SETUP.md"}, {"level": 4, "text": "MANUAL_OPERATIONS.md"}, {"level": 4, "text": "TROUBLESHOOTING.md"}, {"level": 3, "text": "n8n-docker\\legacy-tunneling"}, {"level": 4, "text": "README.md"}, {"level": 4, "text": "ZROK_SETUP_GUIDE.md"}, {"level": 3, "text": "n8n-docker\\ssl"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "n8n_builder"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "n8n_builder\\validation"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "projects"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "projects\\elthosdb1"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "projects\\test-1"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "projects\\test-project"}, {"level": 4, "text": "README.md"}, {"level": 3, "text": "tests"}, {"level": 4, "text": "README.md"}], "links": [{"text": "🔧 Troubleshooting", "url": "TROUBLESHOOTING.md", "type": "markdown"}, {"text": "README.md", "url": "../README.md", "type": "markdown"}, {"text": "API_DOCUMENTATION.md", "url": "API_DOCUMENTATION.md", "type": "markdown"}, {"text": "API_QUICK_REFERENCE.md", "url": "API_QUICK_REFERENCE.md", "type": "markdown"}, {"text": "ProcessFlow.md", "url": "ProcessFlow.MD", "type": "markdown"}, {"text": "Complete Architecture Guide", "url": "Documentation/../Documentation/ARCHITECTURE.m...\n- **📖 [Main Documentation](Documentation/README.md", "type": "markdown"}, {"text": "Documentation Index", "url": "Documentation/INDEX.md", "type": "markdown"}, {"text": "KnowledgeBase Integration", "url": "Documentation/INTEGRATION_COMPLETE.md", "type": "markdown"}, {"text": "Complete Documentation", "url": "Documentation/README.md", "type": "markdown"}, {"text": "Architecture Guide", "url": "Documentation/../Documentation/ARCHITECTURE.md", "type": "markdown"}, {"text": "Documentation Index", "url": "Documentation/INDEX.md", "type": "markdown"}, {"text": "KnowledgeBase Integration", "url": "Documentation/INTEGRATION_COMPLETE.md", "type": "markdown"}, {"text": "Setup Guides", "url": "Documentation/GENERIC_SETUP_GUIDE.md", "type": "markdown"}, {"text": "README.md", "url": "README.md", "type": "markdown"}, {"text": "ARCHITECTURE.md", "url": "../Documentation/ARCHITECTURE.md", "type": "markdown"}, {"text": "INTEGRATION_GUIDE.md", "url": "INTEGRATION_GUIDE.md", "type": "markdown"}, {"text": "KnowledgeBaseReadMe.md", "url": "KnowledgeBaseReadMe.md", "type": "markdown"}, {"text": "SQLConventions.md", "url": "SQLConventions.md", "type": "markdown"}, {"text": "N8N_Builder Setup", "url": "../../README.md", "type": "markdown"}, {"text": "Master Documentation", "url": "../../Documentation/DOCUMENTATION_INDEX.md", "type": "markdown"}, {"text": "Main README", "url": "README.md", "type": "markdown"}, {"text": "Security Guide", "url": "SECURITY.md", "type": "markdown"}, {"text": "Quick Start", "url": "QUICK_START.md", "type": "markdown"}, {"text": "Main README", "url": "README.md", "type": "markdown"}, {"text": "Security Guide", "url": "SECURITY.md", "type": "markdown"}, {"text": "Automation Guide", "url": "AUTOMATION-README.md", "type": "markdown"}, {"text": "Manual Operations", "url": "../RunSystem.md", "type": "markdown"}, {"text": "Quick Start", "url": "QUICK_START.md", "type": "markdown"}, {"text": "Main README", "url": "README.md", "type": "markdown"}, {"text": "Credentials Setup", "url": "CREDENTIALS_SETUP.md", "type": "markdown"}, {"text": "Automation Guide", "url": "AUTOMATION-README.md", "type": "markdown"}, {"text": "SSL Guide", "url": "../ssl/README.md", "type": "markdown"}, {"text": "Quick Start", "url": "QUICK_START.md", "type": "markdown"}, {"text": "Main README", "url": "README.md", "type": "markdown"}, {"text": "Manual Operations", "url": "../RunSystem.md", "type": "markdown"}, {"text": "Security Guide", "url": "SECURITY.md", "type": "markdown"}, {"text": "Credentials Setup", "url": "CREDENTIALS_SETUP.md", "type": "markdown"}, {"text": "Docker Desktop releases", "url": "https://docs.docker.com/desktop/release-notes/", "type": "markdown"}, {"text": "Quick Start", "url": "Documentation/QUICK_START.md", "type": "markdown"}, {"text": "Automation Guide", "url": "Documentation/AUTOMATION-README.md", "type": "markdown"}, {"text": "Security Guide", "url": "Documentation/SECURITY.md", "type": "markdown"}, {"text": "Credentials Setup", "url": "Documentation/CREDENTIALS_SETUP.md", "type": "markdown"}], "code_blocks": {"fenced": 0, "inline": 314, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": true, "has_oauth_setup": true, "tunnel_method_count": 1, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 21.9, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": true, "readability_score": 5}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": ["File is very long (>300 lines) - consider splitting", "File is very wordy (>3000 words) - consider condensing", "References outdated ngrok instead of LocalTunnel"], "recommendations": []}, {"file_path": "data\\Documentation_Consolidation_Report.md", "line_count": 300, "word_count": 1205, "headers": [{"level": 1, "text": "N8N_Builder Documentation Consolidation Plan"}, {"level": 2, "text": "📊 Executive Summary"}, {"level": 3, "text": "Current State"}, {"level": 3, "text": "Issues Identified"}, {"level": 2, "text": "🚨 Critical Redundancy Issues (Immediate Action Required)"}, {"level": 3, "text": "Readme.Md"}, {"level": 3, "text": "Prerequisites"}, {"level": 3, "text": "🎯 Overview"}, {"level": 3, "text": "Stop Everything"}, {"level": 3, "text": "Common Issues"}, {"level": 2, "text": "📋 Consolidation Groups"}, {"level": 3, "text": "1. Setup and Getting Started"}, {"level": 3, "text": "2. Technical Documentation"}, {"level": 3, "text": "3. Troubleshooting Guide"}, {"level": 3, "text": "4. Project-Specific Documentation"}, {"level": 2, "text": "✅ Action Items by Priority"}, {"level": 3, "text": "High Priority (Do First)"}, {"level": 3, "text": "Medium Priority (Do Second)"}, {"level": 3, "text": "Low Priority (Do Last)"}, {"level": 2, "text": "🎯 Recommended Consolidation Approach"}, {"level": 3, "text": "Phase 1: Critical Redundancy (Week 1)"}, {"level": 3, "text": "Phase 2: File Consolidation (Week 2)"}, {"level": 3, "text": "Phase 3: Cleanup (Week 3)"}, {"level": 2, "text": "🏗️ Target Documentation Structure"}, {"level": 2, "text": "📈 Success Metrics"}], "links": [], "code_blocks": {"fenced": 1, "inline": 26, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 10.5, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "data\\file_deletion_report.md", "line_count": 103, "word_count": 241, "headers": [{"level": 1, "text": "File Deletion Report"}, {"level": 2, "text": "📊 Summary"}, {"level": 2, "text": "✅ Successfully Deleted Files"}, {"level": 2, "text": "🗂️ Empty Directories Removed"}, {"level": 2, "text": "🔄 Recovery Instructions"}, {"level": 1, "text": "To see what was deleted"}, {"level": 1, "text": "To recover a specific file"}, {"level": 1, "text": "To recover all deleted files"}, {"level": 2, "text": "🎯 Next Steps"}], "links": [], "code_blocks": {"fenced": 1, "inline": 66, "bash_blocks": 1, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": true, "has_oauth_setup": true, "tunnel_method_count": 1, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 3.5, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": false, "logical_flow": true, "structure_score": 8}, "issues": ["References outdated ngrok instead of LocalTunnel"], "recommendations": []}, {"file_path": "data\\link_validation_report.md", "line_count": 99, "word_count": 246, "headers": [{"level": 1, "text": "Documentation Link Validation Report"}, {"level": 2, "text": "📊 Summary"}, {"level": 2, "text": "📄 File Details"}, {"level": 3, "text": "README.md"}, {"level": 3, "text": "GETTING_STARTED.md"}, {"level": 3, "text": "FEATURES.md"}, {"level": 3, "text": "Documentation\\Architecture.md"}, {"level": 3, "text": "Documentation\\DesignPrinciples.md"}, {"level": 3, "text": "Documentation\\DevelopersWorkflow.md"}, {"level": 3, "text": "Documentation\\guides\\Integration.md"}, {"level": 3, "text": "Documentation\\guides\\Troubleshooting.md"}, {"level": 3, "text": "Documentation\\technical\\Specifications.md"}, {"level": 3, "text": "Documentation\\api\\API_Reference.md"}, {"level": 3, "text": "Scripts\\README.md"}, {"level": 2, "text": "📈 Final Results"}], "links": [{"text": "📖 Getting Started", "url": "GETTING_STARTED.md", "type": "markdown"}, {"text": "🔗 Integration Guide", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "🔧 Troubleshooting", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "Documentation/Architecture.md", "type": "markdown"}, {"text": "Troubleshooting Guide", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "Developer Workflow", "url": "Documentation/DevelopersWorkflow.md", "type": "markdown"}, {"text": "API Reference", "url": "Documentation/api/API_Reference.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Getting Started Guide", "url": "GETTING_STARTED.md", "type": "markdown"}, {"text": "Integration Setup", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Complete Developer Guide", "url": "Documentation/DevelopersWorkflow.md", "type": "markdown"}, {"text": "📖 Getting Started", "url": "GETTING_STARTED.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Integration Guide", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "Documentation/Architecture.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "API Reference", "url": "Documentation/api/API_Reference.md", "type": "markdown"}, {"text": "Troubleshooting Guide", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "README.md", "url": "../README.md", "type": "markdown"}, {"text": "Architecture.md", "url": "Architecture.md", "type": "markdown"}, {"text": "API_Reference.md", "url": "api/API_Reference.md", "type": "markdown"}, {"text": "Troubleshooting.md", "url": "guides/Troubleshooting.md", "type": "markdown"}, {"text": "Getting Started Guide", "url": "../../GETTING_STARTED.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "../technical/Specifications.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "../Architecture.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "../technical/Specifications.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "../Architecture.md", "type": "markdown"}, {"text": "Getting Started", "url": "../../GETTING_STARTED.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "../technical/Specifications.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "../Architecture.md", "type": "markdown"}], "code_blocks": {"fenced": 0, "inline": 0, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 3.7, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": true, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "data\\safe_project_analysis_20250627_232355.md", "line_count": 9, "word_count": 26, "headers": [{"level": 1, "text": "Safe Project Analysis Report"}, {"level": 2, "text": "Summary"}], "links": [], "code_blocks": {"fenced": 0, "inline": 1, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 26.0, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": false, "readability_score": 6}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": false, "logical_flow": true, "structure_score": 8}, "issues": [], "recommendations": []}, {"file_path": "data\\safe_project_analysis_20250628_014002.md", "line_count": 9, "word_count": 26, "headers": [{"level": 1, "text": "Safe Project Analysis Report"}, {"level": 2, "text": "Summary"}], "links": [], "code_blocks": {"fenced": 0, "inline": 1, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 26.0, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": false, "readability_score": 6}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": false, "logical_flow": true, "structure_score": 8}, "issues": [], "recommendations": []}, {"file_path": "data\\streamlined_cleanup_plan.md", "line_count": 149, "word_count": 436, "headers": [{"level": 1, "text": "Streamlined Documentation Cleanup Plan"}, {"level": 2, "text": "📊 Cleanup Summary"}, {"level": 2, "text": "✅ Files to Keep"}, {"level": 2, "text": "🔄 Files to Rename/Move"}, {"level": 2, "text": "🗑️ Files to Delete"}, {"level": 2, "text": "📝 Files to Create"}, {"level": 2, "text": "📁 Folders to Create"}, {"level": 2, "text": "🎯 Target Structure"}, {"level": 2, "text": "⚡ Execution Steps"}, {"level": 2, "text": "⚠️ Safety Notes"}], "links": [], "code_blocks": {"fenced": 1, "inline": 83, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": true, "has_oauth_setup": true, "tunnel_method_count": 1, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 4.5, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": false, "logical_flow": true, "structure_score": 8}, "issues": ["References outdated ngrok instead of LocalTunnel"], "recommendations": []}, {"file_path": "Documentation\\api\\API_Reference.md", "line_count": 905, "word_count": 2876, "headers": [{"level": 1, "text": "N8N Builder API Documentation"}, {"level": 2, "text": "📋 **Overview**"}, {"level": 3, "text": "**🔄 Dual API Architecture**"}, {"level": 2, "text": "🤖 **AG-UI Protocol Endpoints**"}, {"level": 3, "text": "**1. Run Agent (AG-UI)**"}, {"level": 3, "text": "**2. AG-UI Health Check**"}, {"level": 3, "text": "**3. AG-UI Server Status**"}, {"level": 2, "text": "🔧 **Core Workflow Endpoints (Standard REST API)**"}, {"level": 3, "text": "**1. Generate Workflow**"}, {"level": 2, "text": "🔄 **Workflow Iteration Endpoints**"}, {"level": 3, "text": "**2. Modify Workflow**"}, {"level": 3, "text": "**3. Iterate Workflow**"}, {"level": 3, "text": "**4. Get Workflow Iterations**"}, {"level": 3, "text": "**5. Get Workflow Feedback**"}, {"level": 2, "text": "🗂️ **Project Management Endpoints**"}, {"level": 3, "text": "**6. List Projects**"}, {"level": 3, "text": "**7. Get Project Statistics**"}, {"level": 3, "text": "**8. Create Project**"}, {"level": 3, "text": "**9. Get Project Details**"}, {"level": 3, "text": "**10. List Project Workflows**"}, {"level": 3, "text": "**11. Get Workflow File**"}, {"level": 3, "text": "**12. Save Workflow File**"}, {"level": 3, "text": "**13. Delete Project**"}, {"level": 2, "text": "📋 **Version Management Endpoints**"}, {"level": 3, "text": "**14. List Workflow Versions**"}, {"level": 3, "text": "**15. Get Version Information**"}, {"level": 3, "text": "**16. Get Version Content**"}, {"level": 3, "text": "**17. Restore Version**"}, {"level": 3, "text": "**18. Compare Versions**"}, {"level": 3, "text": "**19. Delete Version**"}, {"level": 3, "text": "**20. Cleanup Versions**"}, {"level": 2, "text": "🏥 **Health Check Endpoints**"}, {"level": 3, "text": "**21. Basic Health Check (Standard API)**"}, {"level": 3, "text": "**22. LLM Health Check**"}, {"level": 2, "text": "🤖 **AG-UI Data Models**"}, {"level": 3, "text": "**RunAgentInput (AG-UI Protocol)**"}, {"level": 3, "text": "**AG-UI Message**"}, {"level": 3, "text": "**AG-UI Context**"}, {"level": 3, "text": "**AG-UI Event Types**"}, {"level": 2, "text": "📊 **Enhanced Data Models (Standard API)**"}, {"level": 3, "text": "**WorkflowModificationRequest**"}, {"level": 3, "text": "**WorkflowIterationRequest**"}, {"level": 3, "text": "**Enhanced ValidationResult**"}, {"level": 3, "text": "**<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON>hanced <PERSON><PERSON><PERSON>)**"}, {"level": 3, "text": "**ProjectResponse**"}, {"level": 2, "text": "🚨 **Enhanced <PERSON><PERSON><PERSON>ling**"}, {"level": 3, "text": "**Validation Errors**"}, {"level": 3, "text": "**LLM Service Errors**"}, {"level": 2, "text": "⚡ **Real-Time Streaming**"}, {"level": 3, "text": "**Complete Event Flow Example:**"}, {"level": 2, "text": "🎯 **Best Practices**"}, {"level": 3, "text": "**For Workflow Operations:**"}, {"level": 3, "text": "**For Project Management:**"}, {"level": 3, "text": "**Error Recovery:**"}, {"level": 2, "text": "📈 **Performance Considerations**"}, {"level": 2, "text": "🔗 **Integration Examples**"}, {"level": 3, "text": "**AG-UI Protocol Integration:**"}, {"level": 3, "text": "**Complete Workflow Lifecycle (Standard API):**"}, {"level": 1, "text": "1. Create a project"}, {"level": 1, "text": "2. Generate initial workflow"}, {"level": 1, "text": "3. Save workflow to project"}, {"level": 1, "text": "4. Modify workflow based on testing"}, {"level": 1, "text": "5. Check iteration history"}, {"level": 3, "text": "**AG-UI Health Monitoring:**"}, {"level": 1, "text": "Check AG-UI server health"}, {"level": 1, "text": "Get detailed AG-UI server status"}, {"level": 2, "text": "📞 **Support**"}, {"level": 2, "text": "🎯 **Choosing Between API Interfaces**"}, {"level": 3, "text": "**Use Standard REST API when:**"}, {"level": 3, "text": "**Use AG-UI Protocol when:**"}, {"level": 3, "text": "**Key Differences:**"}], "links": [{"text": "Getting Started", "url": "../../GETTING_STARTED.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "../technical/Specifications.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "../Architecture.md", "type": "markdown"}], "code_blocks": {"fenced": 36, "inline": 149, "bash_blocks": 2, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 15.7, "has_bullets": true, "has_numbered_lists": true, "has_tables": true, "has_emojis": true, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": ["File is very long (>300 lines) - consider splitting"], "recommendations": []}, {"file_path": "Documentation\\ARCHITECTURE.md", "line_count": 150, "word_count": 697, "headers": [{"level": 1, "text": "N8N_Builder Architecture"}, {"level": 2, "text": "🏗️ System Overview"}, {"level": 2, "text": "🧩 Core Components"}, {"level": 3, "text": "1. API Layer (`n8n_builder/app.py`)"}, {"level": 3, "text": "2. AI Processing Engine"}, {"level": 3, "text": "3. Local LLM Integration"}, {"level": 3, "text": "4. Workflow Generator"}, {"level": 2, "text": "📁 Project Structure"}, {"level": 2, "text": "🔧 Configuration Management"}, {"level": 3, "text": "Environment Configuration"}, {"level": 3, "text": "AI Model Configuration"}, {"level": 2, "text": "🚀 Deployment Architecture"}, {"level": 3, "text": "Local Development"}, {"level": 3, "text": "Production Deployment"}, {"level": 3, "text": "External Access (OAuth Setup)"}, {"level": 2, "text": "🔒 Security Considerations"}, {"level": 3, "text": "Data Privacy"}, {"level": 3, "text": "Access Control"}, {"level": 2, "text": "🔄 Data Flow"}, {"level": 2, "text": "🧪 Testing Strategy"}, {"level": 3, "text": "Unit Tests"}, {"level": 3, "text": "Integration Tests"}, {"level": 2, "text": "📈 Scalability"}, {"level": 3, "text": "Horizontal Scaling"}, {"level": 3, "text": "Performance Optimization"}], "links": [], "code_blocks": {"fenced": 2, "inline": 5, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": true, "tunnel_method_count": 1, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 21.8, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\Blogger-API-Workflow-Setup.md", "line_count": 189, "word_count": 882, "headers": [{"level": 1, "text": "Blogger API Workflow Setup Guide"}, {"level": 2, "text": "Overview"}, {"level": 2, "text": "Benefits of Using Blogger API"}, {"level": 2, "text": "Prerequisites"}, {"level": 2, "text": "Step 1: Set Up Blogger API Credentials"}, {"level": 3, "text": "1.1 Run the Setup Script"}, {"level": 3, "text": "1.2 Manual Setup (if needed)"}, {"level": 3, "text": "1.3 Configure N8N Credentials"}, {"level": 2, "text": "Step 2: Find Your Blog ID"}, {"level": 3, "text": "2.1 Run the Helper Script"}, {"level": 3, "text": "2.2 Using Blogger API (Recommended)"}, {"level": 3, "text": "2.3 From Blogger Dashboard"}, {"level": 2, "text": "Step 3: Configure the Workflow"}, {"level": 3, "text": "3.1 Import the New Workflow"}, {"level": 3, "text": "3.2 Update Configuration"}, {"level": 3, "text": "3.3 Workflow Structure"}, {"level": 2, "text": "Step 4: Key Workflow Changes"}, {"level": 3, "text": "4.1 Replaced Nodes"}, {"level": 3, "text": "4.2 New API Parameters"}, {"level": 3, "text": "4.3 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, {"level": 2, "text": "Step 5: Testing the Workflow"}, {"level": 3, "text": "5.1 Test Individual Nodes"}, {"level": 3, "text": "5.2 Full Workflow Test"}, {"level": 3, "text": "5.3 Expected API Response Structure"}, {"level": 2, "text": "Step 6: Troubleshooting"}, {"level": 3, "text": "6.1 Common Issues"}, {"level": 3, "text": "6.2 Debug Tips"}, {"level": 3, "text": "6.3 Fallback Options"}, {"level": 2, "text": "Step 7: Advanced Configuration"}, {"level": 3, "text": "7.1 API Parameters"}, {"level": 3, "text": "7.2 Post Filtering"}, {"level": 3, "text": "7.3 Metadata Usage"}, {"level": 2, "text": "Conclusion"}], "links": [{"text": "Google Cloud Console", "url": "https://console.cloud.google.com/", "type": "markdown"}, {"text": "Blogger.com", "url": "https://www.blogger.com/", "type": "markdown"}], "code_blocks": {"fenced": 6, "inline": 33, "bash_blocks": 0, "powershell_blocks": 2}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": false, "has_ngrok": true, "has_oauth_setup": true, "tunnel_method_count": 2, "preferred_method": "localtunnel"}, "readability_score": {"avg_words_per_sentence": 9.7, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": false, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\Blogger-Workflow-Comparison.md", "line_count": 241, "word_count": 949, "headers": [{"level": 1, "text": "Blogger Workflow Comparison: ScrapeNinja vs Blogger API"}, {"level": 2, "text": "Overview"}, {"level": 2, "text": "Architecture Comparison"}, {"level": 3, "text": "Old Approach (ScrapeNinja)"}, {"level": 3, "text": "New Approach (Blogger API)"}, {"level": 2, "text": "Detailed Node Comparison"}, {"level": 3, "text": "1. Initial Configuration"}, {"level": 4, "text": "Old: Set Main Blog URL"}, {"level": 4, "text": "New: Set Blog ID"}, {"level": 3, "text": "2. Data Retrieval"}, {"level": 4, "text": "Old: Fetch Blog Page + Scrape Blog Page"}, {"level": 4, "text": "New: Get Blog Posts via API"}, {"level": 3, "text": "3. URL Extraction"}, {"level": 4, "text": "Old: Extract Post URLs (Complex Regex)"}, {"level": 4, "text": "New: Extract Post URLs from API (Simple)"}, {"level": 2, "text": "Performance Comparison"}, {"level": 3, "text": "Old Approach Performance"}, {"level": 3, "text": "New Approach Performance"}, {"level": 2, "text": "Error <PERSON>"}, {"level": 3, "text": "Old Approach Errors"}, {"level": 3, "text": "New Approach Errors"}, {"level": 2, "text": "Data Quality Comparison"}, {"level": 3, "text": "Old Approach Data"}, {"level": 3, "text": "New Approach Data"}, {"level": 2, "text": "Maintenance Comparison"}, {"level": 3, "text": "Old Approach Maintenance"}, {"level": 3, "text": "New Approach Maintenance"}, {"level": 2, "text": "Security Comparison"}, {"level": 3, "text": "Old Approach Security"}, {"level": 3, "text": "New Approach Security"}, {"level": 2, "text": "Migration Benefits"}, {"level": 3, "text": "Immediate Benefits"}, {"level": 3, "text": "Long-term Benefits"}, {"level": 2, "text": "Migration Checklist"}, {"level": 3, "text": "Pre-Migration"}, {"level": 3, "text": "Migration"}, {"level": 3, "text": "Post-Migration"}, {"level": 2, "text": "Conclusion"}, {"level": 3, "text": "Recommendation"}], "links": [], "code_blocks": {"fenced": 9, "inline": 17, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": true, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 19.8, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": false, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\DesignPrinciples.md", "line_count": 159, "word_count": 618, "headers": [{"level": 1, "text": "N8N_Builder Design Principles"}, {"level": 2, "text": "🎯 Core Philosophy"}, {"level": 2, "text": "🏗️ Architectural Principles"}, {"level": 3, "text": "1. **Local-First Privacy**"}, {"level": 3, "text": "2. **Modular Architecture**"}, {"level": 3, "text": "3. **<PERSON><PERSON><PERSON>-Friendly**"}, {"level": 3, "text": "4. **User Experience First**"}, {"level": 3, "text": "5. **Script-Driven Operations**"}, {"level": 2, "text": "🔧 Technical Principles"}, {"level": 3, "text": "1. **Reliability**"}, {"level": 3, "text": "2. **Performance**"}, {"level": 3, "text": "3. **Maintainability**"}, {"level": 3, "text": "4. **Extensibility**"}, {"level": 2, "text": "🌟 Community vs Enterprise"}, {"level": 3, "text": "Community Edition Principles"}, {"level": 3, "text": "Enterprise Edition Principles"}, {"level": 2, "text": "🎨 Design Patterns"}, {"level": 3, "text": "1. **Configuration Over Code**"}, {"level": 3, "text": "2. **Fail-<PERSON> De<PERSON>ults**"}, {"level": 3, "text": "3. **Progressive Enhancement**"}, {"level": 2, "text": "🔄 Development Workflow"}, {"level": 3, "text": "1. **Documentation-Driven**"}, {"level": 3, "text": "2. **Test-Driven Quality**"}, {"level": 3, "text": "3. **Community-Focused**"}, {"level": 2, "text": "📈 Success Metrics"}, {"level": 3, "text": "Technical Metrics"}, {"level": 3, "text": "User Experience Metrics"}, {"level": 2, "text": "🔮 Future Principles"}, {"level": 3, "text": "1. **AI Evolution**"}, {"level": 3, "text": "2. **Integration Expansion**"}, {"level": 3, "text": "3. **Community Growth**"}], "links": [], "code_blocks": {"fenced": 0, "inline": 0, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 28.1, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": true, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\DevelopersWorkflow.md", "line_count": 210, "word_count": 954, "headers": [{"level": 1, "text": "🔧 Developer Workflow Guide"}, {"level": 2, "text": "🎯 **Overview**"}, {"level": 2, "text": "🏗️ **Repository Architecture**"}, {"level": 2, "text": "🚀 **Daily Development Workflow**"}, {"level": 3, "text": "**Standard Workflow (Recommended)**"}, {"level": 3, "text": "**Alternative: Direct Script Execution**"}, {"level": 1, "text": "From N8N_Builder root directory"}, {"level": 2, "text": "📋 **Workflow Options**"}, {"level": 3, "text": "**Option 1: Commit + Sync (Most Common)**"}, {"level": 3, "text": "**Option 2: Commit Only (Local Development)**"}, {"level": 3, "text": "**Option 3: Sync Only (Manual)**"}, {"level": 2, "text": "🛡️ **Safety Features**"}, {"level": 3, "text": "**Automatic Private Component Protection**"}, {"level": 3, "text": "**What Gets Synced vs. What Stays Private**"}, {"level": 4, "text": "**✅ Synced to Community Edition:**"}, {"level": 4, "text": "**❌ Stays Private (Never Synced):**"}, {"level": 2, "text": "🔧 **VS Code Integration**"}, {"level": 3, "text": "**Available Tasks**"}, {"level": 3, "text": "**Task Configuration**"}, {"level": 2, "text": "📝 **Commit Message Guidelines**"}, {"level": 3, "text": "**Main Repository Commits**"}, {"level": 3, "text": "**Community Repository Commits**"}, {"level": 2, "text": "🔍 **Troubleshooting**"}, {"level": 3, "text": "**Common Issues**"}, {"level": 4, "text": "**\"No changes to commit\"**"}, {"level": 4, "text": "**\"sync-public.ps1 not found\"**"}, {"level": 4, "text": "**\"Failed to push to GitHub\"**"}, {"level": 4, "text": "**\"Community sync failed\"**"}, {"level": 3, "text": "**Manual Recovery**"}, {"level": 1, "text": "1. Commit to main repository"}, {"level": 1, "text": "2. Sync to community"}, {"level": 1, "text": "3. <PERSON><PERSON> to GitHub"}, {"level": 2, "text": "📚 **Related Documentation**"}, {"level": 2, "text": "🤝 **Team Development**"}, {"level": 3, "text": "**For New Developers**"}, {"level": 3, "text": "**Best Practices**"}, {"level": 2, "text": "🔄 **Workflow Summary**"}], "links": [{"text": "README.md", "url": "../README.md", "type": "markdown"}, {"text": "Architecture.md", "url": "Architecture.md", "type": "markdown"}, {"text": "API_Reference.md", "url": "api/API_Reference.md", "type": "markdown"}, {"text": "Troubleshooting.md", "url": "guides/Troubleshooting.md", "type": "markdown"}], "code_blocks": {"fenced": 4, "inline": 12, "bash_blocks": 0, "powershell_blocks": 2}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 16.4, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\guides\\Integration.md", "line_count": 214, "word_count": 886, "headers": [{"level": 1, "text": "🔗 Integration Setup Guide"}, {"level": 2, "text": "What You'll Learn"}, {"level": 2, "text": "Prerequisites"}, {"level": 2, "text": "Common Integrations"}, {"level": 3, "text": "📧 Email Integration (Gmail)"}, {"level": 4, "text": "Generate Email Workflow"}, {"level": 4, "text": "Configure Gmail in n8n"}, {"level": 4, "text": "Test the Integration"}, {"level": 3, "text": "💬 Slack Integration"}, {"level": 4, "text": "Generate Slack Workflow"}, {"level": 4, "text": "Configure <PERSON><PERSON>ck in n8n"}, {"level": 4, "text": "Test File Upload Trigger"}, {"level": 3, "text": "🌐 Webhook Integrations"}, {"level": 4, "text": "Setup LocalTunnel for External Access"}, {"level": 1, "text": "Start LocalTunnel to n8n (no installation required)"}, {"level": 4, "text": "Get Public Webhook URL"}, {"level": 4, "text": "Configure External Services"}, {"level": 2, "text": "Advanced Integration Patterns"}, {"level": 3, "text": "Multi-Service Workflow"}, {"level": 3, "text": "Conditional Logic"}, {"level": 3, "text": "Data Transformation"}, {"level": 2, "text": "Security Best Practices"}, {"level": 3, "text": "Credential Management"}, {"level": 3, "text": "Webhook Security"}, {"level": 3, "text": "Network Security"}, {"level": 2, "text": "Troubleshooting Integrations"}, {"level": 3, "text": "Common Issues"}, {"level": 3, "text": "Debug Tools"}, {"level": 1, "text": "Test your webhook"}, {"level": 1, "text": "View n8n container logs"}, {"level": 1, "text": "Check specific execution"}, {"level": 1, "text": "Go to n8n → Executions tab → <PERSON><PERSON> failed execution"}, {"level": 2, "text": "Next Steps"}, {"level": 3, "text": "More Complex Integrations"}, {"level": 3, "text": "Service-Specific Guides"}], "links": [{"text": "Getting Started Guide", "url": "../../GETTING_STARTED.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "../technical/Specifications.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "../Architecture.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "../technical/Specifications.md", "type": "markdown"}], "code_blocks": {"fenced": 10, "inline": 23, "bash_blocks": 4, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": true, "has_ngrok": true, "has_oauth_setup": true, "tunnel_method_count": 3, "preferred_method": "localhost.run"}, "readability_score": {"avg_words_per_sentence": 13.4, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\guides\\Troubleshooting.md", "line_count": 273, "word_count": 746, "headers": [{"level": 1, "text": "🔧 Troubleshooting Guide"}, {"level": 2, "text": "🚨 Emergency Quick Fixes"}, {"level": 3, "text": "System Won't Start"}, {"level": 1, "text": "Check if ports are in use"}, {"level": 1, "text": "Kill processes using ports"}, {"level": 1, "text": "Windows"}, {"level": 1, "text": "Linux/Mac"}, {"level": 1, "text": "Restart everything"}, {"level": 3, "text": "Complete Reset"}, {"level": 1, "text": "Stop everything"}, {"level": 1, "text": "<PERSON> Docker"}, {"level": 1, "text": "Restart fresh"}, {"level": 2, "text": "🤖 N8N_Builder Issues"}, {"level": 3, "text": "\"N8N_Builder won't start\""}, {"level": 1, "text": "Should return model list"}, {"level": 3, "text": "\"Workflow generation fails\""}, {"level": 3, "text": "\"API returns errors\""}, {"level": 2, "text": "🐳 n8n-docker Issues"}, {"level": 3, "text": "\"n8n won't start\""}, {"level": 1, "text": "Should show Docker running"}, {"level": 1, "text": "Should be empty or show n8n"}, {"level": 3, "text": "\"Can't access n8n web interface\""}, {"level": 1, "text": "Should show n8n-dev as \"Up\""}, {"level": 3, "text": "\"Database connection errors\""}, {"level": 2, "text": "🔗 Integration Issues"}, {"level": 3, "text": "\"Workflows won't import\""}, {"level": 3, "text": "\"Nodes show errors\""}, {"level": 3, "text": "\"Webhooks not working\""}, {"level": 1, "text": "Verify LocalTunnel SSH process is running"}, {"level": 1, "text": "Check if n8n is accessible locally"}, {"level": 2, "text": "🔍 Diagnostic Commands"}, {"level": 3, "text": "System Health Check"}, {"level": 1, "text": "N8N_Builder"}, {"level": 1, "text": "n8n"}, {"level": 1, "text": "<PERSON>er"}, {"level": 1, "text": "nGrok (if running)"}, {"level": 3, "text": "Log Locations"}, {"level": 1, "text": "N8N_Builder logs"}, {"level": 1, "text": "n8n logs"}, {"level": 1, "text": "PostgreSQL logs"}, {"level": 1, "text": "Docker compose logs"}, {"level": 3, "text": "Port Usage Check"}, {"level": 1, "text": "Windows"}, {"level": 1, "text": "Linux/Mac"}, {"level": 2, "text": "🆘 Getting Help"}, {"level": 3, "text": "Before Asking for Help"}, {"level": 3, "text": "Where to Get Help"}, {"level": 3, "text": "Information to Include"}], "links": [{"text": "Architecture Overview", "url": "../Architecture.md", "type": "markdown"}], "code_blocks": {"fenced": 19, "inline": 49, "bash_blocks": 19, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": true, "has_ngrok": true, "has_oauth_setup": false, "tunnel_method_count": 3, "preferred_method": "localhost.run"}, "readability_score": {"avg_words_per_sentence": 21.3, "has_bullets": true, "has_numbered_lists": true, "has_tables": true, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\ReadMe_TunnelSetup.md", "line_count": 91, "word_count": 644, "headers": [{"level": 1, "text": "Tunnel Setup for OAuth2 Integration"}, {"level": 2, "text": "Overview"}, {"level": 2, "text": "Prerequisites (Verify First)"}, {"level": 2, "text": "Step 1: Start SSH Tunnel (localhost.run)"}, {"level": 2, "text": "Step 2: Update Docker Configuration"}, {"level": 2, "text": "Step 3: <PERSON><PERSON> n8n Container"}, {"level": 2, "text": "Step 4: Update OAuth Provider (Twitter Example)"}, {"level": 2, "text": "Step 5: Complete OAuth in n8n"}, {"level": 2, "text": "Step 6: Test and Cleanup"}, {"level": 2, "text": "Important Notes"}, {"level": 3, "text": "URL Behavior"}, {"level": 3, "text": "Common Issues"}, {"level": 3, "text": "OAuth Provider URLs"}, {"level": 3, "text": "Security Considerations"}, {"level": 2, "text": "Alternative Tunneling Options"}, {"level": 2, "text": "Troubleshooting"}], "links": [], "code_blocks": {"fenced": 1, "inline": 25, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": true, "has_ngrok": true, "has_oauth_setup": true, "tunnel_method_count": 4, "preferred_method": "localhost.run"}, "readability_score": {"avg_words_per_sentence": 35.8, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": false, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "Documentation\\technical\\Specifications.md", "line_count": 255, "word_count": 789, "headers": [{"level": 1, "text": "N8N_Builder Technical Specifications"}, {"level": 2, "text": "🔧 System Requirements"}, {"level": 3, "text": "Minimum Requirements"}, {"level": 3, "text": "Recommended Requirements"}, {"level": 2, "text": "🏗️ Technical Architecture"}, {"level": 3, "text": "Core Components"}, {"level": 4, "text": "1. **N8N_Builder API Server**"}, {"level": 4, "text": "2. **AI Processing Engine**"}, {"level": 4, "text": "3. **Workflow Generator**"}, {"level": 4, "text": "4. **Configuration Management**"}, {"level": 4, "text": "5. **External Access (LocalTunnel)**"}, {"level": 3, "text": "Data Flow Architecture"}, {"level": 2, "text": "📡 API Specifications"}, {"level": 3, "text": "REST API Endpoints"}, {"level": 4, "text": "**POST /generate-workflow**"}, {"level": 4, "text": "**GET /health**"}, {"level": 4, "text": "**GET /templates**"}, {"level": 3, "text": "WebSocket API (Future)"}, {"level": 2, "text": "🔒 Security Specifications"}, {"level": 3, "text": "Authentication"}, {"level": 3, "text": "Data Privacy"}, {"level": 3, "text": "Network Security"}, {"level": 2, "text": "📊 Performance Specifications"}, {"level": 3, "text": "Response Times"}, {"level": 3, "text": "Throughput"}, {"level": 3, "text": "Scalability"}, {"level": 2, "text": "🔧 Configuration Specifications"}, {"level": 3, "text": "Environment Variables"}, {"level": 1, "text": "Core Settings"}, {"level": 1, "text": "AI Settings"}, {"level": 1, "text": "Security Settings"}, {"level": 3, "text": "Configuration Files"}, {"level": 2, "text": "🧪 Testing Specifications"}, {"level": 3, "text": "Test Coverage"}, {"level": 3, "text": "Test Environment"}, {"level": 3, "text": "Quality Gates"}, {"level": 2, "text": "📈 Monitoring Specifications"}, {"level": 3, "text": "Metrics Collection"}, {"level": 3, "text": "Logging"}, {"level": 3, "text": "Health Monitoring"}], "links": [], "code_blocks": {"fenced": 6, "inline": 21, "bash_blocks": 1, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": true, "has_ngrok": false, "has_oauth_setup": true, "tunnel_method_count": 2, "preferred_method": "localtunnel"}, "readability_score": {"avg_words_per_sentence": 23.2, "has_bullets": true, "has_numbered_lists": false, "has_tables": true, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": [], "recommendations": []}, {"file_path": "FEATURES.md", "line_count": 51, "word_count": 241, "headers": [{"level": 1, "text": "N8N_Builder Features"}, {"level": 2, "text": "🚀 Core Capabilities"}, {"level": 3, "text": "AI-Powered Workflow Generation"}, {"level": 3, "text": "Advanced Automation"}, {"level": 3, "text": "Developer-Friendly"}, {"level": 2, "text": "🛠️ Technical Features"}, {"level": 3, "text": "Local AI Integration"}, {"level": 3, "text": "Workflow Management"}, {"level": 3, "text": "Integration Capabilities"}, {"level": 2, "text": "🎯 Use Cases"}, {"level": 2, "text": "🔧 Customization Options"}], "links": [], "code_blocks": {"fenced": 0, "inline": 0, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 241.0, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": true, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "GETTING_STARTED.md", "line_count": 250, "word_count": 1012, "headers": [{"level": 1, "text": "📖 Getting Started with N8N_Builder"}, {"level": 2, "text": "What is N8N_Builder?"}, {"level": 2, "text": "Quick Setup Overview"}, {"level": 2, "text": "Step 1: Setup N8N_Builder (Workflow Generator)"}, {"level": 3, "text": "Python Environment Requirements"}, {"level": 4, "text": "Required Setup"}, {"level": 1, "text": "Create and activate virtual environment"}, {"level": 1, "text": "Windows (PowerShell)"}, {"level": 1, "text": "Windows (Command Prompt)"}, {"level": 1, "text": "Linux/Mac"}, {"level": 1, "text": "Install dependencies in virtual environment"}, {"level": 4, "text": "Why Virtual Environment is Required"}, {"level": 4, "text": "Verification"}, {"level": 1, "text": "Check you're in the virtual environment (should show venv path)"}, {"level": 1, "text": "Test critical imports"}, {"level": 3, "text": "Configure Your LLM"}, {"level": 3, "text": "Start the Generator"}, {"level": 1, "text": "Windows PowerShell"}, {"level": 1, "text": "Windows Command Prompt"}, {"level": 1, "text": "These scripts automatically:"}, {"level": 1, "text": "- Verify virtual environment is configured"}, {"level": 1, "text": "- Check dependencies are installed"}, {"level": 1, "text": "- Start with proper Python environment"}, {"level": 1, "text": "Activate virtual environment first"}, {"level": 1, "text": "venv\\Scripts\\activate.bat   # Windows Command Prompt"}, {"level": 1, "text": "source venv/bin/activate    # Linux/Mac"}, {"level": 1, "text": "Then start normally"}, {"level": 1, "text": "Opens TWO interfaces:"}, {"level": 1, "text": "N8N Builder: http://localhost:8002"}, {"level": 1, "text": "System Dashboard: http://localhost:8081"}, {"level": 1, "text": "Use venv Python directly (no activation needed)"}, {"level": 1, "text": "./venv/bin/python run.py        # Linux/Mac"}, {"level": 1, "text": "Ensure virtual environment is activated first"}, {"level": 1, "text": "Opens at http://localhost:8000"}, {"level": 2, "text": "Step 2: Setup n8n-docker (Workflow Executor)"}, {"level": 3, "text": "Quick Start"}, {"level": 1, "text": "Windows"}, {"level": 1, "text": "Linux/Mac"}, {"level": 3, "text": "Access n8n"}, {"level": 2, "text": "Step 3: Create Your First Workflow"}, {"level": 3, "text": "Generate with AI"}, {"level": 3, "text": "Deploy to n8n"}, {"level": 2, "text": "Understanding the System"}, {"level": 3, "text": "N8N_Builder Features"}, {"level": 3, "text": "n8n-docker Features"}, {"level": 2, "text": "Common Customizations"}, {"level": 3, "text": "Change Ports"}, {"level": 3, "text": "Enable Webhooks"}, {"level": 3, "text": "Production Security"}, {"level": 2, "text": "Next Steps"}, {"level": 3, "text": "I Want To..."}, {"level": 3, "text": "Troubleshooting"}, {"level": 4, "text": "Environment Issues"}, {"level": 4, "text": "General Issues"}, {"level": 4, "text": "Environment Recovery Commands"}, {"level": 1, "text": "If virtual environment is corrupted, recreate it:"}, {"level": 1, "text": "Verify everything works:"}, {"level": 2, "text": "Success Indicators"}], "links": [{"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Integration Guide", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "Documentation/Architecture.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "API Reference", "url": "Documentation/api/API_Reference.md", "type": "markdown"}, {"text": "Troubleshooting Guide", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "User Guides", "url": "Documentation/guides/", "type": "markdown"}], "code_blocks": {"fenced": 12, "inline": 29, "bash_blocks": 11, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": true, "has_oauth_setup": false, "tunnel_method_count": 1, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 14.7, "has_bullets": true, "has_numbered_lists": true, "has_tables": true, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": ["References outdated ngrok instead of LocalTunnel", "Key file missing quick start section"], "recommendations": []}, {"file_path": "n8n-docker\\OAUTH_SETUP_GUIDE.md", "line_count": 135, "word_count": 621, "headers": [{"level": 1, "text": "OAuth2 Setup Guide for n8n"}, {"level": 2, "text": "Overview"}, {"level": 2, "text": "Prerequisites"}, {"level": 2, "text": "Step 1: Start LocalTunnel"}, {"level": 2, "text": "Step 2: Configure OAuth2 Services"}, {"level": 3, "text": "Twitter OAuth2"}, {"level": 3, "text": "Google OAuth2"}, {"level": 3, "text": "GitHub OAuth2"}, {"level": 2, "text": "Step 3: Configure n8n Credentials"}, {"level": 2, "text": "Step 4: Test Your Integration"}, {"level": 2, "text": "Important Notes"}, {"level": 3, "text": "Keep LocalTunnel Running"}, {"level": 3, "text": "Browser Password Prompt"}, {"level": 3, "text": "Workflow Development"}, {"level": 2, "text": "Troubleshooting"}, {"level": 3, "text": "\"Subdomain not available\""}, {"level": 3, "text": "\"Connection refused\""}, {"level": 3, "text": "\"OAuth2 callback failed\""}, {"level": 3, "text": "\"LocalTunnel not found\""}, {"level": 2, "text": "File Structure"}, {"level": 2, "text": "Security Notes"}, {"level": 2, "text": "Summary"}], "links": [], "code_blocks": {"fenced": 3, "inline": 19, "bash_blocks": 0, "powershell_blocks": 1}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": true, "tunnel_method_count": 1, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 9.3, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": [], "recommendations": []}, {"file_path": "n8n-docker\\README-LocalTunnel.md", "line_count": 62, "word_count": 237, "headers": [{"level": 1, "text": "LocalTunnel for n8n OAuth Integrations"}, {"level": 2, "text": "Quick Start"}, {"level": 3, "text": "1. Start LocalTunnel"}, {"level": 3, "text": "2. Use OAuth2 Callback URL"}, {"level": 2, "text": "What This Does"}, {"level": 2, "text": "OAuth2 Service Setup"}, {"level": 3, "text": "Twitter"}, {"level": 3, "text": "Google"}, {"level": 3, "text": "GitHub"}, {"level": 2, "text": "n8n Access"}, {"level": 2, "text": "Important Notes"}, {"level": 2, "text": "Troubleshooting"}, {"level": 2, "text": "Files Created"}], "links": [], "code_blocks": {"fenced": 2, "inline": 9, "bash_blocks": 0, "powershell_blocks": 1}, "has_toc": false, "has_quick_start": true, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": true, "tunnel_method_count": 1, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 6.6, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": false, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": ["Missing Step-by-step process in tunnel documentation", "Missing Prerequisites in tunnel documentation"], "recommendations": []}, {"file_path": "n8n_builder\\README.md", "line_count": 108, "word_count": 457, "headers": [{"level": 1, "text": "N8N_Builder Core Module"}, {"level": 2, "text": "🏗️ Module Structure"}, {"level": 3, "text": "Core Application Files"}, {"level": 3, "text": "AI and Processing"}, {"level": 3, "text": "Data Management"}, {"level": 3, "text": "Validation and Quality"}, {"level": 3, "text": "System Management"}, {"level": 3, "text": "Advanced Features"}, {"level": 2, "text": "🚀 Key Features"}, {"level": 3, "text": "AI Integration"}, {"level": 3, "text": "Workflow Generation"}, {"level": 3, "text": "Performance & Reliability"}, {"level": 3, "text": "Integration Capabilities"}, {"level": 2, "text": "🔧 Configuration"}, {"level": 2, "text": "📊 Usage Examples"}, {"level": 3, "text": "Basic Workflow Generation"}, {"level": 3, "text": "API Server"}, {"level": 3, "text": "CLI Usage"}, {"level": 2, "text": "🧪 Testing"}, {"level": 2, "text": "🔗 Dependencies"}], "links": [], "code_blocks": {"fenced": 3, "inline": 30, "bash_blocks": 1, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 13.1, "has_bullets": true, "has_numbered_lists": false, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": ["Key file missing quick start section"], "recommendations": []}, {"file_path": "n8n_builder\\validation\\README.md", "line_count": 151, "word_count": 509, "headers": [{"level": 1, "text": "N8N Builder Validation System"}, {"level": 2, "text": "Features"}, {"level": 2, "text": "Components"}, {"level": 3, "text": "Validators"}, {"level": 3, "text": "Error Codes"}, {"level": 2, "text": "Usage"}, {"level": 3, "text": "Basic Usage"}, {"level": 1, "text": "Create a validation service"}, {"level": 1, "text": "Validate a workflow"}, {"level": 1, "text": "Check validation results"}, {"level": 3, "text": "Custom Configuration"}, {"level": 1, "text": "Create a custom configuration"}, {"level": 1, "text": "Create a validation service with custom configuration"}, {"level": 3, "text": "Adding Custom Rules"}, {"level": 1, "text": "Create a custom rule"}, {"level": 1, "text": "Add the rule to the configuration"}, {"level": 1, "text": "Create a validation service with the custom rule"}, {"level": 2, "text": "Best Practices"}, {"level": 2, "text": "Contributing"}, {"level": 2, "text": "Testing"}, {"level": 2, "text": "License"}], "links": [], "code_blocks": {"fenced": 4, "inline": 10, "bash_blocks": 1, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 14.1, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": false, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": ["Key file missing quick start section"], "recommendations": []}, {"file_path": "projects\\basicai\\README.md", "line_count": 43, "word_count": 132, "headers": [{"level": 1, "text": "Basicai"}, {"level": 2, "text": "Project Information"}, {"level": 2, "text": "Workflows"}, {"level": 2, "text": "Getting Started"}, {"level": 2, "text": "Project Structure"}, {"level": 2, "text": "File Naming Conventions"}, {"level": 2, "text": "Iteration History"}], "links": [], "code_blocks": {"fenced": 1, "inline": 5, "bash_blocks": 0, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 9.4, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": false, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": false, "logical_flow": true, "structure_score": 8}, "issues": ["Key file missing quick start section"], "recommendations": []}, {"file_path": "projects\\elthosdb1\\README.md", "line_count": 199, "word_count": 854, "headers": [{"level": 1, "text": "ElthosRPG Blog to Twitter Automation"}, {"level": 2, "text": "Overview"}, {"level": 2, "text": "Workflows"}, {"level": 3, "text": "1. ElthosRPG_Blog_Twitter.json (Legacy)"}, {"level": 3, "text": "2. ElthosRPG_Blog_Twitter_BloggerAPI.json (Current)"}, {"level": 2, "text": "Current Workflow Features"}, {"level": 3, "text": "🔄 Automated Process"}, {"level": 3, "text": "🛠️ Technical Components"}, {"level": 3, "text": "📊 Workflow Nodes"}, {"level": 2, "text": "Setup Instructions"}, {"level": 3, "text": "Prerequisites"}, {"level": 3, "text": "Quick Setup"}, {"level": 3, "text": "Detailed Setup"}, {"level": 2, "text": "Configuration"}, {"level": 3, "text": "Blog Configuration"}, {"level": 3, "text": "AI Configuration"}, {"level": 3, "text": "Twitter Configuration"}, {"level": 2, "text": "API Endpoints Used"}, {"level": 3, "text": "Blogger API"}, {"level": 3, "text": "Twitter API"}, {"level": 2, "text": "Monitoring and Debugging"}, {"level": 3, "text": "Execution Logs"}, {"level": 3, "text": "Common Issues"}, {"level": 3, "text": "Erro<PERSON>"}, {"level": 2, "text": "Performance Metrics"}, {"level": 3, "text": "Reliability Improvements"}, {"level": 3, "text": "Speed Improvements"}, {"level": 3, "text": "Maintenance Reduction"}, {"level": 2, "text": "Future Enhancements"}, {"level": 3, "text": "Planned Features"}, {"level": 3, "text": "Metadata Utilization"}, {"level": 2, "text": "Support and Documentation"}, {"level": 3, "text": "Resources"}, {"level": 3, "text": "<PERSON><PERSON><PERSON>"}, {"level": 3, "text": "Troubleshooting"}, {"level": 2, "text": "License and Usage"}, {"level": 2, "text": "Contributing"}], "links": [{"text": "Blogger API Workflow Setup Guide", "url": "../../Documentation/Blogger-API-Workflow-Setup.md", "type": "markdown"}, {"text": "Setup Guide", "url": "../../Documentation/Blogger-API-Workflow-Setup.md", "type": "markdown"}, {"text": "Comparison Guide", "url": "../../Documentation/Blogger-Workflow-Comparison.md", "type": "markdown"}, {"text": "Blogger API Documentation", "url": "https://developers.google.com/blogger/docs/3.0/reference/", "type": "markdown"}, {"text": "N8N Documentation", "url": "https://docs.n8n.io/", "type": "markdown"}], "code_blocks": {"fenced": 3, "inline": 11, "bash_blocks": 0, "powershell_blocks": 2}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": true, "has_localhost_run": false, "has_ngrok": true, "has_oauth_setup": true, "tunnel_method_count": 2, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 10.9, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": false, "readability_score": 8}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": true, "structure_score": 9}, "issues": ["Key file missing quick start section"], "recommendations": []}, {"file_path": "README.md", "line_count": 224, "word_count": 1180, "headers": [{"level": 1, "text": "N8N_Builder: AI-Powered Workflow Automation"}, {"level": 2, "text": "🏷️ Editions"}, {"level": 2, "text": "🚀 Quick Start (Choose Your Speed)"}, {"level": 2, "text": "🏗️ How It Works"}, {"level": 2, "text": "✨ What You Can Build"}, {"level": 2, "text": "🎯 Key Features"}, {"level": 3, "text": "🌟 **Community Edition Features**"}, {"level": 3, "text": "🚀 **Enterprise Edition Enhancements**"}, {"level": 2, "text": "🚀 Getting Started"}, {"level": 3, "text": "**🌟 Community Edition (This Repository)**"}, {"level": 1, "text": "Start N8N Builder Community Edition"}, {"level": 1, "text": "Run core system tests"}, {"level": 3, "text": "**🚀 Enterprise Edition**"}, {"level": 2, "text": "🔧 Running Different Editions"}, {"level": 3, "text": "**🌟 Community Edition (Default)**"}, {"level": 1, "text": "Standard startup (Community Edition)"}, {"level": 1, "text": "Available at: http://localhost:8002"}, {"level": 1, "text": "Features: Full AI workflow generation with standard error handling"}, {"level": 3, "text": "**🚀 Enterprise Edition**"}, {"level": 1, "text": "Full system with advanced features (requires Enterprise components)"}, {"level": 1, "text": "Available at:"}, {"level": 1, "text": "- Main App: http://localhost:8002"}, {"level": 1, "text": "- Advanced Dashboard: http://localhost:8081 (Enterprise only)"}, {"level": 3, "text": "**🔍 How to Tell Which Edition You're Running**"}, {"level": 2, "text": "📚 Documentation"}, {"level": 3, "text": "🎯 **Start Here**"}, {"level": 3, "text": "🔧 **For Developers**"}, {"level": 3, "text": "🐳 **n8n-docker Setup**"}, {"level": 3, "text": "🤖 **Advanced Features**"}, {"level": 2, "text": "🚀 Recent Updates"}, {"level": 3, "text": "**🌟 Community Edition (Latest)**"}, {"level": 3, "text": "**🚀 Enterprise Edition Features**"}, {"level": 2, "text": "👨‍💻 **Developer Workflow**"}, {"level": 2, "text": "🤝 Contributing"}, {"level": 2, "text": "📄 License"}, {"level": 2, "text": "📊 Project Overview"}, {"level": 3, "text": "**🎯 Project Statistics**"}, {"level": 3, "text": "**📈 Architecture Highlights**"}, {"level": 3, "text": "**🏗️ Development Philosophy**"}, {"level": 3, "text": "**🌟 Quality Assurance**"}], "links": [{"text": "📖 Getting Started", "url": "GETTING_STARTED.md", "type": "markdown"}, {"text": "🔗 Integration Guide", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "🔧 Troubleshooting", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "Documentation/Architecture.md", "type": "markdown"}, {"text": "Troubleshooting Guide", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "Developer Workflow", "url": "Documentation/DevelopersWorkflow.md", "type": "markdown"}, {"text": "API Reference", "url": "Documentation/api/API_Reference.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Getting Started Guide", "url": "GETTING_STARTED.md", "type": "markdown"}, {"text": "Integration Setup", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Complete Developer Guide", "url": "Documentation/DevelopersWorkflow.md", "type": "markdown"}, {"text": "📖 Getting Started", "url": "GETTING_STARTED.md", "type": "markdown"}], "code_blocks": {"fenced": 5, "inline": 10, "bash_blocks": 4, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": true, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 28.8, "has_bullets": true, "has_numbered_lists": true, "has_tables": true, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": [], "recommendations": []}, {"file_path": "README_Community.md", "line_count": 201, "word_count": 1554, "headers": [{"level": 1, "text": "N8N_Builder: AI-Powered Workflow Automation"}, {"level": 2, "text": "🏷️ Editions"}, {"level": 2, "text": "🚀 Quick Start (Choose Your Speed)"}, {"level": 2, "text": "🏗️ How It Works"}, {"level": 2, "text": "✨ What You Can Build"}, {"level": 2, "text": "🎯 Key Features"}, {"level": 3, "text": "🌟 **Community Edition Features**"}, {"level": 3, "text": "🚀 **Private Modules**"}, {"level": 2, "text": "🚀 Getting Started"}, {"level": 3, "text": "**🌟 Community Edition (This Repository)**"}, {"level": 1, "text": "Start N8N Builder"}, {"level": 1, "text": "Run core system tests"}, {"level": 3, "text": "**🔍 How to Tell Which Edition You're Running**"}, {"level": 2, "text": "📚 Documentation"}, {"level": 3, "text": "🎯 **Start Here**"}, {"level": 3, "text": "🔧 **For Developers**"}, {"level": 3, "text": "🐳 **n8n-docker Setup**"}, {"level": 3, "text": "🤖 **Advanced Topics**"}, {"level": 2, "text": "🚀 Recent Updates"}, {"level": 3, "text": "**🌟 Community Edition (Latest)**"}, {"level": 3, "text": "**🚀 Advanced Private Module Features**"}, {"level": 2, "text": "👨‍💻 **Developer Workflow**"}, {"level": 2, "text": "🤝 Contributing"}, {"level": 2, "text": "📄 License"}, {"level": 2, "text": "📊 Project Overview"}, {"level": 3, "text": "**🎯 Project Statistics**"}, {"level": 3, "text": "**📈 Architecture Highlights**"}, {"level": 3, "text": "**🏗️ Development Philosophy**"}, {"level": 3, "text": "**🌟 Quality Assurance**"}, {"level": 2, "text": "Notes:"}], "links": [{"text": "📖 Getting Started", "url": "GETTING_STARTED.md", "type": "markdown"}, {"text": "🔗 Integration Guide", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "🔧 Troubleshooting", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "Architecture Overview", "url": "Documentation/Architecture.md", "type": "markdown"}, {"text": "Troubleshooting Guide", "url": "Documentation/guides/Troubleshooting.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "Developer Workflow", "url": "Documentation/DevelopersWorkflow.md", "type": "markdown"}, {"text": "API Reference", "url": "Documentation/api/API_Reference.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Getting Started Guide", "url": "GETTING_STARTED.md", "type": "markdown"}, {"text": "Integration Setup", "url": "Documentation/guides/Integration.md", "type": "markdown"}, {"text": "Design Principles", "url": "Documentation/DesignPrinciples.md", "type": "markdown"}, {"text": "Technical Specifications", "url": "Documentation/technical/Specifications.md", "type": "markdown"}, {"text": "Complete Developer Guide", "url": "Documentation/DevelopersWorkflow.md", "type": "markdown"}, {"text": "📖 Getting Started", "url": "GETTING_STARTED.md", "type": "markdown"}], "code_blocks": {"fenced": 3, "inline": 6, "bash_blocks": 2, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": true, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 25.5, "has_bullets": true, "has_numbered_lists": true, "has_tables": true, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": [], "recommendations": []}, {"file_path": "Scripts\\README.md", "line_count": 66, "word_count": 260, "headers": [{"level": 1, "text": "N8N_Builder Scripts"}, {"level": 2, "text": "📁 Script Categories"}, {"level": 3, "text": "🔧 Project Management"}, {"level": 3, "text": "📊 Analysis Tools"}, {"level": 3, "text": "🔄 Log Management"}, {"level": 3, "text": "🧪 Testing Utilities"}, {"level": 2, "text": "🚀 Usage"}, {"level": 3, "text": "Running Python Scripts"}, {"level": 1, "text": "From the project root directory"}, {"level": 3, "text": "Running PowerShell Scripts"}, {"level": 1, "text": "From the project root directory"}, {"level": 2, "text": "⚙️ Configuration"}, {"level": 2, "text": "🛡️ Safety Features"}, {"level": 2, "text": "📋 Best Practices"}, {"level": 2, "text": "🔗 Integration"}], "links": [], "code_blocks": {"fenced": 2, "inline": 5, "bash_blocks": 1, "powershell_blocks": 1}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 12.4, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": ["Key file missing quick start section"], "recommendations": []}, {"file_path": "tests\\README.md", "line_count": 261, "word_count": 1010, "headers": [{"level": 1, "text": "🧪 N8N Builder Test Suite"}, {"level": 2, "text": "📋 Overview"}, {"level": 2, "text": "🎯 Test Categories"}, {"level": 3, "text": "**1. System Health Tests** (`test_system_health.py`)"}, {"level": 3, "text": "**2. Stored Procedures Tests** (`test_stored_procedures.py`)"}, {"level": 3, "text": "**3. MCP Research Tool Tests**"}, {"level": 3, "text": "**4. Core System Tests**"}, {"level": 2, "text": "🚀 Quick Start"}, {"level": 3, "text": "**Run All Tests**"}, {"level": 1, "text": "Run comprehensive system test suite"}, {"level": 1, "text": "Or run individual test suites"}, {"level": 3, "text": "**Using Pytest**"}, {"level": 1, "text": "Run all tests with pytest"}, {"level": 1, "text": "Run specific test files"}, {"level": 1, "text": "Run with coverage"}, {"level": 3, "text": "**Individual Test Files**"}, {"level": 1, "text": "System health check"}, {"level": 1, "text": "Database and stored procedures"}, {"level": 1, "text": "MCP Research Tool tests"}, {"level": 1, "text": "Complete integration tests"}, {"level": 2, "text": "📊 Test Results"}, {"level": 3, "text": "**Status Indicators**"}, {"level": 3, "text": "**Result Files**"}, {"level": 3, "text": "**Expected Test Results**"}, {"level": 2, "text": "🔧 Configuration"}, {"level": 3, "text": "**Test Configuration** (`test_config.json`)"}, {"level": 3, "text": "**Database Configuration**"}, {"level": 3, "text": "**Environment Setup**"}, {"level": 3, "text": "**Dependencies**"}, {"level": 2, "text": "🐛 Troubleshooting"}, {"level": 3, "text": "**Common Issues**"}, {"level": 4, "text": "**Import Errors**"}, {"level": 4, "text": "**Network Timeouts**"}, {"level": 4, "text": "**<PERSON><PERSON>**"}, {"level": 2, "text": "📈 Performance Benchmarks"}, {"level": 3, "text": "**Typical Test Performance**"}, {"level": 3, "text": "**Network-Dependent Tests**"}, {"level": 2, "text": "🔍 Test Details"}, {"level": 3, "text": "**test_mcp_research.py**"}, {"level": 3, "text": "**test_complete_integration.py**"}, {"level": 3, "text": "**test_research_quality.py**"}, {"level": 2, "text": "📝 Adding New Tests"}, {"level": 3, "text": "**Test File Template**"}, {"level": 1, "text": "Add parent directory to path for imports when running from tests folder"}, {"level": 1, "text": "Your test imports and code here"}, {"level": 3, "text": "**Best Practices**"}, {"level": 2, "text": "🎯 Test Goals"}, {"level": 2, "text": "📚 Related Documentation"}], "links": [{"text": "MCP Research Setup Guide", "url": "../Documentation/MCP_RESEARCH_SETUP_GUIDE.md", "type": "markdown"}, {"text": "API Documentation", "url": "../Documentation/API_DOCUMENTATION.md", "type": "markdown"}, {"text": "Technical Architecture", "url": "../Documentation/DOCUMENTATION.MD", "type": "markdown"}], "code_blocks": {"fenced": 10, "inline": 37, "bash_blocks": 4, "powershell_blocks": 0}, "has_toc": false, "has_quick_start": false, "tunnel_references": {"has_localtunnel": false, "has_localhost_run": false, "has_ngrok": false, "has_oauth_setup": false, "tunnel_method_count": 0, "preferred_method": null}, "readability_score": {"avg_words_per_sentence": 13.5, "has_bullets": true, "has_numbered_lists": true, "has_tables": false, "has_emojis": true, "readability_score": 9}, "structure_score": {"has_title": true, "has_sections": true, "has_subsections": true, "logical_flow": false, "structure_score": 7}, "issues": ["Key file missing quick start section"], "recommendations": []}], "priority_actions": [{"priority": "HIGH", "file": "data\\documentation_analysis_report.md", "action": "Fix 3 critical issues", "issues": ["File is very long (>300 lines) - consider splitting", "File is very wordy (>3000 words) - consider condensing", "References outdated ngrok instead of LocalTunnel"]}, {"priority": "MEDIUM", "file": "data\\documentation_analysis_report.md", "action": "Update tunnel documentation to prefer LocalTunnel", "details": "Replace ngrok references with LocalTunnel instructions"}, {"priority": "MEDIUM", "file": "data\\file_deletion_report.md", "action": "Update tunnel documentation to prefer LocalTunnel", "details": "Replace ngrok references with LocalTunnel instructions"}, {"priority": "MEDIUM", "file": "data\\streamlined_cleanup_plan.md", "action": "Update tunnel documentation to prefer LocalTunnel", "details": "Replace ngrok references with LocalTunnel instructions"}, {"priority": "MEDIUM", "file": "GETTING_STARTED.md", "action": "Update tunnel documentation to prefer LocalTunnel", "details": "Replace ngrok references with LocalTunnel instructions"}]}