{"total_files": 60, "files_processed": 60, "files_modified": 59, "files_failed": 1, "details": [{"file": "Scripts\\analyze_documentation.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_documentation.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\analyze_project_files.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_project_files.py", "Added Python root detection code", "Fixed import pattern: from\\s+\\.\\s+import", "File updated successfully"]}, {"file": "Scripts\\analyze_script_paths.py", "status": "failed", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_script_paths.py", "Root detection already present", "No changes needed"]}, {"file": "Scripts\\analyze_venv_files.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_venv_files.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\analyze_workspace_folders.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_workspace_folders.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\auto-commit-clean.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\auto-commit-clean.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\clean_commit_messages.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\clean_commit_messages.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\cleanup-root-folder.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\cleanup-root-folder.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\cleanup_markdown_refs.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\cleanup_markdown_refs.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\commit-and-sync-community.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\commit-and-sync-community.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\commit-and-sync-simple.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\commit-and-sync-simple.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\compare_readme_files.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\compare_readme_files.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\comprehensive-audit.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\comprehensive-audit.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\comprehensive_repo_scan.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\comprehensive_repo_scan.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\consolidate-self-healer.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\consolidate-self-healer.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\create_documentation_consolidation_plan.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\create_documentation_consolidation_plan.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\debug_analyze_files.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\debug_analyze_files.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\delete_obsolete_files.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\delete_obsolete_files.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\deploy_public.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\deploy_public.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\detect-private-components.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\detect-private-components.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\dev_publish.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\dev_publish.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\direct_commit_cleanup.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\direct_commit_cleanup.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\execute_separation.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\execute_separation.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\final_documentation_validation.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\final_documentation_validation.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\fix_analyze_files.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\fix_analyze_files.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\fix_script_references.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\fix_script_references.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\generate_consolidation_report.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\generate_consolidation_report.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\get-blogger-blog-id.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\get-blogger-blog-id.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\github_repository_setup.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\github_repository_setup.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\improve_documentation.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\improve_documentation.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\migrate_naming_convention.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\migrate_naming_convention.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\pre_commit_cleanup.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\pre_commit_cleanup.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\pre_execution_verification.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\pre_execution_verification.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\prepare_public_release.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\prepare_public_release.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\project_cleanup_manager.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\project_cleanup_manager.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\restore_n8n_setup.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\restore_n8n_setup.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\rewrite_commit_messages.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\rewrite_commit_messages.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\run_cleanup.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\run_cleanup.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\run_with_venv.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\run_with_venv.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\sanitize_documentation.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\sanitize_documentation.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\scan_md_for_private_refs.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\scan_md_for_private_refs.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\setup_log_rotation.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\setup_log_rotation.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\shutdown.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\shutdown.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\simple_commit_cleanup.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\simple_commit_cleanup.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\streamlined_documentation_cleanup.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\streamlined_documentation_cleanup.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\sync-community-only.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\sync-community-only.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\sync-public.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\sync-public.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\test-blogger-api.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test-blogger-api.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\test-detection.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test-detection.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\test_detection_simple.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_detection_simple.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\test_enhanced_sync.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_enhanced_sync.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\test_knowledgebase_procedures.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_knowledgebase_procedures.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\test_safe_cleanup.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_safe_cleanup.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\test_verification_systems.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_verification_systems.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\update_folder_references.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\update_folder_references.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\validate-localtunnel-integration.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\validate-localtunnel-integration.ps1", "Added PowerShell root detection code", "File updated successfully"]}, {"file": "Scripts\\validate_documentation_links.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\validate_documentation_links.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\validate_documentation_quality.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\validate_documentation_quality.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\verification_pipeline.py", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\verification_pipeline.py", "Added Python root detection code", "File updated successfully"]}, {"file": "Scripts\\verify-public-clean.ps1", "status": "success", "changes": ["Created backup: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\verify-public-clean.ps1", "Added PowerShell root detection code", "File updated successfully"]}]}