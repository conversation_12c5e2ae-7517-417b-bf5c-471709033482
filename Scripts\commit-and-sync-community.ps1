# N8N Builder - Commit and Sync Community Edition
# This script commits changes to the main repository and optionally syncs to the community edition

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
param(
    [string]$CommitMessage = "",
    [string]$CommunityMessage = "",
    [switch]$SkipCommunitySync = $false
)

# Set up colors for better output
$ErrorActionPreference = "Continue"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "🎯 $Title" "Cyan"
    Write-ColorOutput ("=" * ($Title.Length + 3)) "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Blue"
}

try {
    Write-Header "N8N Builder - Commit and Sync Workflow"
    
    # Check if we're in the right directory
    if (-not (Test-Path "n8n_builder") -or -not (Test-Path "Scripts")) {
        Write-Error "Please run this script from the N8N_Builder root directory"
        exit 1
    }

    # Check git status
    $gitStatus = git status --porcelain
    if (-not $gitStatus) {
        Write-Warning "No changes to commit in the main repository"
        $continueAnyway = Read-Host "Continue with community sync only? (y/N)"
        if ($continueAnyway -ne "y" -and $continueAnyway -ne "Y") {
            Write-Info "Operation cancelled"
            exit 0
        }
        $SkipMainCommit = $true
    }

    # Step 1: Commit to main repository
    if (-not $SkipMainCommit) {
        Write-Header "Step 1: Commit to Main Repository"
        
        if (-not $CommitMessage) {
            Write-Info "Enter commit message for main N8N_Builder repository:"
            $CommitMessage = Read-Host "Commit message"
        }

        if (-not $CommitMessage -or $CommitMessage.Trim() -eq "") {
            Write-Error "Commit message is required"
            exit 1
        }

        Write-Info "Staging all changes..."
        git add .
        
        Write-Info "Committing with message: '$CommitMessage'"
        git commit -m "$CommitMessage"
        
        Write-Success "Successfully committed to main repository"
    }

    # Step 2: Community Edition Sync
    if (-not $SkipCommunitySync) {
        Write-Header "Step 2: Community Edition Sync"
        
        if (-not $CommunityMessage) {
            Write-Info "Enter message for Community Edition sync:"
            Write-ColorOutput "  (Press Enter to skip community sync)" "Gray"
            $CommunityMessage = Read-Host "Community message"
        }

        if (-not $CommunityMessage -or $CommunityMessage.Trim() -eq "") {
            Write-Warning "Skipping community sync (no message provided)"
            Write-Info "You can sync manually later with: .\Scripts\sync-community-only.ps1"
            exit 0
        }

        # Run the community sync
        Write-Info "Syncing to Community Edition..."
        
        # Check if sync script exists
        if (-not (Test-Path "sync-public.ps1")) {
            Write-Error "sync-public.ps1 not found. Please ensure the separation system is set up."
            exit 1
        }

        # Run the sync
        .\sync-public.ps1 -PublicRepoPath "..\N8N_Builder_Community" -Force
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Community sync failed"
            exit 1
        }

        Write-Success "Community files synced successfully"

        # Step 3: Commit and push to GitHub
        Write-Header "Step 3: Push to GitHub"
        
        $originalLocation = Get-Location
        try {
            Set-Location "..\N8N_Builder_Community"

            # Check if there are changes to commit
            $communityStatus = git status --porcelain
            if ($communityStatus) {
                Write-Info "Committing community changes..."
                git add .
                git commit -m "$CommunityMessage"

                Write-Info "Pushing to GitHub..."
                git push origin master

                Write-Success "Successfully pushed to GitHub!"
                Write-ColorOutput "📝 Community commit: $CommunityMessage" "Cyan"
            } else {
                Write-Warning "No changes to commit in community repository"
            }
        }
        finally {
            Set-Location $originalLocation
        }
    }

    Write-Header "Workflow Complete"
    Write-Success "All operations completed successfully!"
    
    if (-not $SkipMainCommit) {
        Write-Info "Main repository: Committed '$CommitMessage'"
    }
    
    if (-not $SkipCommunitySync -and $CommunityMessage) {
        Write-Info "Community repository: Synced and pushed '$CommunityMessage'"
        Write-Info "GitHub: https://github.com/vbwyrde/N8N_Builder"
    }

} catch {
    Write-Error "Script failed: $($_.Exception.Message)"
    Write-Info "You may need to run operations manually:"
    Write-Info "  1. git add . && git commit -m 'Your message'"
    Write-Info "  2. .\Scripts\sync-community-only.ps1"
    exit 1
}
