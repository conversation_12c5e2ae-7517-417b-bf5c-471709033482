# VS Code Upgrade Script for N8N_Builder MCP Integration
# This script safely upgrades VS Code to the latest version with MCP support

param(
    [switch]$Force,
    [switch]$BackupSettings
)

Write-Host "🔧 VS Code Upgrade Script for MCP Support" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Check current VS Code version
Write-Host "`n📋 Checking current VS Code version..." -ForegroundColor Yellow
try {
    $currentVersion = & code --version 2>$null | Select-Object -First 1
    Write-Host "Current version: $currentVersion" -ForegroundColor Green
    
    # Parse version number
    $versionParts = $currentVersion.Split('.')
    $majorVersion = [int]$versionParts[0]
    $minorVersion = [int]$versionParts[1]
    
    if ($majorVersion -eq 1 -and $minorVersion -ge 102) {
        Write-Host "✅ VS Code version supports MCP!" -ForegroundColor Green
        if (-not $Force) {
            $continue = Read-Host "Continue with upgrade anyway? (y/N)"
            if ($continue -ne 'y' -and $continue -ne 'Y') {
                Write-Host "Upgrade cancelled." -ForegroundColor Yellow
                exit 0
            }
        }
    } else {
        Write-Host "⚠️  VS Code version is too old for MCP support (requires 1.102+)" -ForegroundColor Red
        Write-Host "Upgrade is required." -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Could not detect VS Code version" -ForegroundColor Red
    Write-Host "VS Code may not be installed or not in PATH" -ForegroundColor Yellow
}

# Backup settings if requested
if ($BackupSettings) {
    Write-Host "`n💾 Backing up VS Code settings..." -ForegroundColor Yellow
    $settingsPath = "$env:APPDATA\Code\User"
    $backupPath = ".\backup\vscode_settings_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    
    if (Test-Path $settingsPath) {
        New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
        Copy-Item -Path "$settingsPath\*" -Destination $backupPath -Recurse -Force
        Write-Host "✅ Settings backed up to: $backupPath" -ForegroundColor Green
    } else {
        Write-Host "⚠️  No existing settings found to backup" -ForegroundColor Yellow
    }
}

# Download latest VS Code
Write-Host "`n⬇️  Downloading latest VS Code..." -ForegroundColor Yellow
$downloadUrl = "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64-user"
$installerPath = "$env:TEMP\VSCodeUserSetup-x64.exe"

try {
    Write-Host "Downloading from: $downloadUrl" -ForegroundColor Gray
    Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "✅ Download completed: $installerPath" -ForegroundColor Green
} catch {
    Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Install VS Code
Write-Host "`n🔧 Installing VS Code..." -ForegroundColor Yellow
Write-Host "This will close any open VS Code instances." -ForegroundColor Yellow

$installArgs = @(
    "/VERYSILENT"
    "/NORESTART"
    "/MERGETASKS=!runcode"  # Don't run VS Code after install
)

try {
    # Close VS Code instances
    Get-Process -Name "Code" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 2
    
    # Run installer
    $process = Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait -PassThru
    
    if ($process.ExitCode -eq 0) {
        Write-Host "✅ VS Code installation completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Installation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Installation error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify new version
Write-Host "`n🔍 Verifying new installation..." -ForegroundColor Yellow
Start-Sleep -Seconds 3  # Wait for installation to complete

try {
    $newVersion = & code --version 2>$null | Select-Object -First 1
    Write-Host "New version: $newVersion" -ForegroundColor Green
    
    # Check MCP support
    $versionParts = $newVersion.Split('.')
    $majorVersion = [int]$versionParts[0]
    $minorVersion = [int]$versionParts[1]
    
    if ($majorVersion -eq 1 -and $minorVersion -ge 102) {
        Write-Host "✅ MCP support is now available!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Version may still not support MCP" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not verify new version immediately" -ForegroundColor Yellow
    Write-Host "Please restart your terminal and run 'code --version'" -ForegroundColor Gray
}

# Cleanup
Write-Host "`n🧹 Cleaning up..." -ForegroundColor Yellow
Remove-Item -Path $installerPath -Force -ErrorAction SilentlyContinue

Write-Host "`n🎉 VS Code upgrade process completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart your terminal" -ForegroundColor White
Write-Host "2. Run 'code --version' to verify" -ForegroundColor White
Write-Host "3. Continue with MCP configuration" -ForegroundColor White

# Check if we should continue with MCP setup
Write-Host "`n❓ Would you like to continue with MCP configuration now? (y/N)" -ForegroundColor Yellow
$continueSetup = Read-Host
if ($continueSetup -eq 'y' -or $continueSetup -eq 'Y') {
    Write-Host "Continuing with MCP setup..." -ForegroundColor Green
    # This could call the next script or return a flag
    exit 2  # Special exit code to indicate continue with setup
}
