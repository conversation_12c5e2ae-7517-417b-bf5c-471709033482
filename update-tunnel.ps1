﻿# Quick tunnel URL update script
# Usage: .\update-tunnel.ps1 "https://your-new-url.lhr.life"

param($NewTunnelUrl)

if (-not $NewTunnelUrl) {
    Write-Host "Usage: .\update-tunnel.ps1 'https://your-tunnel-url.lhr.life'" -ForegroundColor Red
    exit 1
}

Write-Host "Updating configuration with: $NewTunnelUrl" -ForegroundColor Green

# Update .env file
$lines = Get-Content .env
$lines = $lines -replace 'WEBHOOK_URL=https://.*\.lhr\.life/', "WEBHOOK_URL=$NewTunnelUrl/"
$lines | Out-File .env -Encoding utf8

# Update docker-compose.yml  
$lines = Get-Content docker-compose.yml
$lines = $lines -replace 'N8N_EDITOR_BASE_URL=https://.*\.lhr\.life', "N8N_EDITOR_BASE_URL=$NewTunnelUrl"
$lines = $lines -replace 'WEBHOOK_URL=https://.*\.lhr\.life/', "WEBHOOK_URL=$NewTunnelUrl/"
$lines | Out-File docker-compose.yml -Encoding utf8

Write-Host "Files updated! Now restart n8n with: docker-compose restart n8n" -ForegroundColor Yellow
Write-Host "Twitter callback URL should be: $NewTunnelUrl/rest/oauth2-credential/callback" -ForegroundColor Cyan
