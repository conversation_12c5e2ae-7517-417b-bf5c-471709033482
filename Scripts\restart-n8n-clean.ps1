# Restart N8N with Clean Logs
# This script restarts n8n and shows clean logs without telemetry noise

Write-Host "🔄 Restarting N8N with Updated Configuration..." -ForegroundColor Green
Write-Host "=" * 50

# Navigate to n8n-docker directory
$scriptDir = Split-Path -Parent $PSScriptRoot
$n8nDockerPath = Join-Path $scriptDir "n8n-docker"

if (-not (Test-Path $n8nDockerPath)) {
    Write-Host "❌ ERROR: n8n-docker directory not found at: $n8nDockerPath" -ForegroundColor Red
    exit 1
}

Set-Location $n8nDockerPath

# Stop current containers
Write-Host "🛑 Stopping current n8n containers..." -ForegroundColor Yellow
docker-compose down

# Start containers with updated configuration
Write-Host "🚀 Starting n8n with updated configuration..." -ForegroundColor Yellow
docker-compose up -d

# Wait for startup
Write-Host "⏳ Waiting for n8n to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Show status
Write-Host "`n📊 Container Status:" -ForegroundColor Cyan
docker-compose ps

# Show recent logs (filtered for important messages)
Write-Host "`n📋 Recent Logs (filtered):" -ForegroundColor Cyan
Write-Host "=" * 30
docker logs n8n --tail 20 2>&1 | Where-Object { 
    $_ -notmatch "Rudder.*error" -and 
    $_ -notmatch "ECONNREFUSED" -and
    $_ -notmatch "typescript.*devDependency"
}

Write-Host "`n✅ N8N Restart Complete!" -ForegroundColor Green
Write-Host "🌐 Access n8n at: http://localhost:5678" -ForegroundColor White
Write-Host "🔗 Stable URL: http://localhost:8080" -ForegroundColor White
Write-Host "`n💡 To view live logs: docker logs n8n -f" -ForegroundColor Cyan
