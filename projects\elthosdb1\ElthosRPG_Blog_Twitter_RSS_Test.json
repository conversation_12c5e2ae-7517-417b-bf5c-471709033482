{"name": "ElthosRPG_Blog_Twitter_RSS_Test", "nodes": [{"parameters": {"url": "={{ $json.selectedPostURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [0, 60], "id": "c9fe53ca-3a67-4ee9-9db5-d776584a8dcb"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, -380], "id": "e7a7f4bd-3ae8-49c7-aafd-b15738091113", "name": "When clicking 'Execute workflow'", "disabled": true}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "mainBlogURL", "value": "https://elthosrpg.blogspot.com/", "type": "string"}, {"id": "456721e2-5dce-41fa-9118-1891bca27395", "name": "rssURL", "value": "https://elthosrpg.blogspot.com/feeds/posts/default?max-results=999", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, -200], "id": "8d837417-3896-4526-a97a-b0ef365678e0", "name": "Set Main Blog URL"}, {"parameters": {"url": "={{ $json.rssURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch RSS Feed", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [360, -200], "id": "40c12ed9-47e2-4d68-9e08-654690c06192"}, {"parameters": {"jsCode": "// Get RSS XML from Fetch RSS Feed\nconst rssXml = $node['Fetch RSS Feed'].json.data;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log('Extracting URLs from RSS XML...');\nconsole.log('RSS XML length:', rssXml.length);\n\n// Parse RSS XML to extract blog post URLs\n// Look for <link> tags that contain blog post URLs\nconst linkMatches = rssXml.match(/<link[^>]*>([^<]+)<\\/link>/g) || [];\nconsole.log('Found', linkMatches.length, 'link tags');\n\n// Also look for alternate link format in entries\nconst entryMatches = rssXml.match(/<entry[\\s\\S]*?<\\/entry>/g) || [];\nconsole.log('Found', entryMatches.length, 'entry blocks');\n\nlet postUrls = [];\n\n// Extract URLs from link tags\nlinkMatches.forEach(match => {\n  const url = match.replace(/<link[^>]*>/, '').replace(/<\\/link>/, '').trim();\n  if (url.includes('elthosrpg.blogspot.com') && url.includes('/20') && url.endsWith('.html') && !postUrls.includes(url)) {\n    postUrls.push(url);\n    console.log('Added URL from link:', url);\n  }\n});\n\n// Extract URLs from entry blocks (alternative method)\nentryMatches.forEach(entry => {\n  // Look for alternate link with rel=\"alternate\"\n  const altLinkMatch = entry.match(/<link[^>]+rel=[\"']alternate[\"'][^>]+href=[\"']([^\"']+)[\"']/i);\n  if (altLinkMatch) {\n    const url = altLinkMatch[1];\n    if (url.includes('elthosrpg.blogspot.com') && url.includes('/20') && url.endsWith('.html') && !postUrls.includes(url)) {\n      postUrls.push(url);\n      console.log('Added URL from entry:', url);\n    }\n  }\n});\n\nconsole.log('Total unique URLs extracted:', postUrls.length);\n\n// If no URLs found, show debug info\nif (postUrls.length === 0) {\n  console.log('No URLs found! RSS XML preview:', rssXml.substring(0, 1000));\n  return [{ \n    postUrls: [],\n    totalPosts: 0,\n    mainBlogURL: mainBlogURL,\n    error: 'No URLs found in RSS feed',\n    rssPreview: rssXml.substring(0, 1000)\n  }];\n}\n\nreturn [{ \n  postUrls: postUrls,\n  totalPosts: postUrls.length,\n  mainBlogURL: mainBlogURL,\n  extractionMethod: 'rss_feed'\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [540, -200], "id": "b42d8fd6-5673-4fdc-8fdd-2b675b0bc3ba", "name": "Extract Post URLs"}, {"parameters": {"jsCode": "// Randomly select a blog post URL\nconst postUrls = $input.first().json.postUrls;\nconst totalPosts = $input.first().json.totalPosts;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log(`Selecting random post from ${totalPosts} available posts...`);\n\n// Generate random index\nconst randomIndex = Math.floor(Math.random() * postUrls.length);\nconst selectedPostURL = postUrls[randomIndex];\n\nconsole.log(`Selected post: ${selectedPostURL}`);\n\n// Return the selected URL along with metadata\nreturn [{ json: { \n  selectedPostURL: selectedPostURL,\n  randomIndex: randomIndex,\n  totalPosts: totalPosts,\n  mainBlogURL: mainBlogURL\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, -200], "id": "63f9d0e1-2669-4d1a-aa66-918704eb45b3", "name": "Random Selection"}], "pinData": {}, "connections": {"Set Main Blog URL": {"main": [[{"node": "Fetch RSS Feed", "type": "main", "index": 0}]]}, "Fetch RSS Feed": {"main": [[{"node": "Extract Post URLs", "type": "main", "index": 0}]]}, "Extract Post URLs": {"main": [[{"node": "Random Selection", "type": "main", "index": 0}]]}, "Random Selection": {"main": [[{"node": "Fetch Post", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "test-rss-version", "meta": {"templateCredsSetupCompleted": true, "instanceId": "test-instance"}, "id": "RSS_TEST_WORKFLOW", "tags": []}