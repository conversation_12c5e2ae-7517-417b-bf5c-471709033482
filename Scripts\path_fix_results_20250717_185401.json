{"total_files": 60, "files_processed": 60, "files_modified": 0, "files_failed": 0, "details": [{"file": "Scripts\\analyze_documentation.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\analyze_project_files.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\analyze_script_paths.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\analyze_venv_files.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\analyze_workspace_folders.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\auto-commit-clean.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\clean_commit_messages.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\cleanup-root-folder.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\cleanup_markdown_refs.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\commit-and-sync-community.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\commit-and-sync-simple.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\compare_readme_files.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\comprehensive-audit.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\comprehensive_repo_scan.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\consolidate-self-healer.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\create_documentation_consolidation_plan.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\debug_analyze_files.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\delete_obsolete_files.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\deploy_public.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\detect-private-components.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\dev_publish.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\direct_commit_cleanup.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\execute_separation.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\final_documentation_validation.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\fix_analyze_files.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\fix_script_references.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\generate_consolidation_report.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\get-blogger-blog-id.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\github_repository_setup.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\improve_documentation.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\migrate_naming_convention.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\pre_commit_cleanup.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\pre_execution_verification.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\prepare_public_release.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\project_cleanup_manager.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\restore_n8n_setup.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\rewrite_commit_messages.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\run_cleanup.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\run_with_venv.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\sanitize_documentation.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\scan_md_for_private_refs.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\setup_log_rotation.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\shutdown.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\simple_commit_cleanup.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\streamlined_documentation_cleanup.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\sync-community-only.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\sync-public.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\test-blogger-api.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\test-detection.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\test_detection_simple.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\test_enhanced_sync.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\test_knowledgebase_procedures.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\test_safe_cleanup.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\test_verification_systems.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\update_folder_references.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\validate-localtunnel-integration.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\validate_documentation_links.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\validate_documentation_quality.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\verification_pipeline.py", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}, {"file": "Scripts\\verify-public-clean.ps1", "status": "would_process", "changes": ["DRY RUN - would add root detection if needed"]}]}