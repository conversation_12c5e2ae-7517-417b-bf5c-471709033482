# Install MCP Dependencies for N8N_Builder
# This script installs the required MCP packages for VS Code integration

param(
    [switch]$Force,
    [switch]$Upgrade
)

Write-Host "Installing MCP Dependencies for N8N_Builder" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Check if virtual environment is active
$venvActive = $env:VIRTUAL_ENV -ne $null
if (-not $venvActive) {
    Write-Host "WARNING: No virtual environment detected!" -ForegroundColor Yellow
    Write-Host "It's recommended to use a virtual environment." -ForegroundColor Yellow
    
    $continue = Read-Host "Continue anyway? (y/N)"
    if ($continue -ne 'y' -and $continue -ne 'Y') {
        Write-Host "Installation cancelled." -ForegroundColor Yellow
        exit 0
    }
} else {
    Write-Host "Virtual environment active: $env:VIRTUAL_ENV" -ForegroundColor Green
}

# Check if pip is available
try {
    $pipVersion = & pip --version 2>$null
    Write-Host "Using pip: $pipVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: pip not found!" -ForegroundColor Red
    Write-Host "Please ensure Python and pip are installed and in PATH." -ForegroundColor Yellow
    exit 1
}

# Install MCP dependencies
Write-Host "`nInstalling MCP dependencies..." -ForegroundColor Yellow

$installArgs = @("install", "-r", "requirements_mcp.txt")

if ($Upgrade) {
    $installArgs += "--upgrade"
    Write-Host "Upgrading existing packages..." -ForegroundColor Gray
}

if ($Force) {
    $installArgs += "--force-reinstall"
    Write-Host "Force reinstalling packages..." -ForegroundColor Gray
}

try {
    Write-Host "Running: pip $($installArgs -join ' ')" -ForegroundColor Gray
    $process = Start-Process -FilePath "pip" -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "MCP dependencies installed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Installation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Installation error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify installation
Write-Host "`nVerifying MCP installation..." -ForegroundColor Yellow

$testPackages = @("mcp", "jsonschema", "aiohttp")
$allInstalled = $true

foreach ($package in $testPackages) {
    try {
        $result = & python -c "import $package; print(f'$package: OK')" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "$result" -ForegroundColor Green
        } else {
            Write-Host "$package: FAILED" -ForegroundColor Red
            $allInstalled = $false
        }
    } catch {
        Write-Host "$package: FAILED" -ForegroundColor Red
        $allInstalled = $false
    }
}

if ($allInstalled) {
    Write-Host "`nAll MCP dependencies verified!" -ForegroundColor Green
    
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "1. Upgrade VS Code to version 1.102+ if not already done" -ForegroundColor White
    Write-Host "2. Test MCP servers with: python -m n8n_builder.mcp_research_server" -ForegroundColor White
    Write-Host "3. Configure VS Code MCP settings in .vscode/mcp.json" -ForegroundColor White
    Write-Host "4. Test MCP integration in VS Code" -ForegroundColor White
} else {
    Write-Host "`nSome packages failed to install!" -ForegroundColor Red
    Write-Host "Please check the error messages above and try again." -ForegroundColor Yellow
    exit 1
}

Write-Host "`nMCP installation completed!" -ForegroundColor Green
