{"nodes": [{"parameters": {"event": "post.published", "url": "https://elthosrpg.blogspot.com"}, "name": "B<PERSON> Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"method": "GET", "url": "={{$node['Blog Trigger'].json['postUrl']}}"}, "name": "Fetch Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/summarize", "headers": {"Content-Type": "application/json"}, "body": {"text": "={{$node['Fetch Post'].json['body']}}"}}, "name": "Local AI Summarize", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"resource": "tweet", "operation": "create", "text": "New Elthos post: {{$node['Local AI Summarize'].json['summary']}} #TTRPG #IndieRPG {{$node['Blog Trigger'].json['postUrl']}}"}, "name": "Tweet", "type": "n8n-nodes-base.twitter", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"channel": "elthos-announcements", "text": "Check out our latest post: {{$node['Local AI Summarize'].json['summary']}} {{$node['Blog Trigger'].json['postUrl']}}"}, "name": "Discord", "type": "n8n-nodes-base.discord", "typeVersion": 1, "position": [900, 400]}], "connections": {"Blog Trigger": {"main": [[{"node": "Fetch Post", "type": "main", "index": 0}]]}, "Fetch Post": {"main": [[{"node": "Local AI Summarize", "type": "main", "index": 0}]]}, "Local AI Summarize": {"main": [[{"node": "Tweet", "type": "main", "index": 0}], [{"node": "Discord", "type": "main", "index": 0}]]}}}