#!/usr/bin/env python3
"""
Developer Publishing Workflow - Single Command GitHub Update
============================================================
Streamlined workflow for developers to update public GitHub repository.

Usage: python dev_publish.py [--message "commit message"] [--dry-run]
"""

import os
import json
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class DeveloperPublisher:
    """Streamlined developer publishing workflow."""
    
    def __init__(self, commit_message: str = None, dry_run: bool = False):
        self.commit_message = commit_message or f"Update N8N_Builder Community Edition - {datetime.now().strftime('%Y-%m-%d')}"
        self.dry_run = dry_run
        self.public_repo_path = Path("../N8N_Builder_Community")
        
        # Load configuration
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """Load developer publishing configuration."""
        config_file = Path("Scripts/dev_config.json")
        
        default_config = {
            "public_repo_path": "../N8N_Builder_Community",
            "auto_push": True,
            "verification_enabled": True,
            "backup_enabled": True,
            "git_remote": "origin",
            "git_branch": "main"
        }
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        symbols = {"INFO": "ℹ️", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌"}
        symbol = symbols.get(level, "ℹ️")
        print(f"[{timestamp}] {symbol} {message}")
    
    def run_command(self, command: str, cwd: str = None) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command, shell=True, cwd=cwd, capture_output=True, text=True, timeout=300
            )
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def step_verification(self) -> bool:
        """Step 1: Quick verification check."""
        self.log("Running quick verification check...")
        
        # Check if private components exist (they should in private repo)
        private_indicators = ["Self_Healer", "KnowledgeBase"]
        has_private = any(Path(indicator).exists() for indicator in private_indicators)
        
        if not has_private:
            self.log("Warning: No private components detected - are you in the right repository?", "WARNING")
        
        # Check critical public files exist
        critical_files = ["README_public.md", "requirements_public.txt", "run_public.py"]
        missing_files = [f for f in critical_files if not Path(f).exists()]
        
        if missing_files:
            self.log(f"Missing critical files: {missing_files}", "ERROR")
            return False
        
        self.log("Verification check passed", "SUCCESS")
        return True
    
    def step_sync(self) -> bool:
        """Step 2: Execute enhanced sync."""
        self.log("Executing enhanced sync to public repository...")
        
        sync_cmd = f"powershell -ExecutionPolicy Bypass -File Scripts\\sync-public.ps1 -PublicRepoPath \"{self.public_repo_path}\" -RunVerification"
        
        if self.dry_run:
            sync_cmd += " -DryRun"
        
        success, stdout, stderr = self.run_command(sync_cmd)
        
        if success:
            self.log("Enhanced sync completed successfully", "SUCCESS")
            return True
        else:
            self.log(f"Enhanced sync failed: {stderr}", "ERROR")
            return False
    
    def step_git_operations(self) -> bool:
        """Step 3: Git operations (add, commit, push)."""
        if self.dry_run:
            self.log("Git operations skipped in dry run mode")
            return True
        
        if not self.public_repo_path.exists():
            self.log(f"Public repository path does not exist: {self.public_repo_path}", "ERROR")
            return False
        
        self.log("Performing Git operations...")
        
        # Check if there are changes
        status_cmd = "git status --porcelain"
        success, stdout, stderr = self.run_command(status_cmd, cwd=str(self.public_repo_path))
        
        if not success:
            self.log(f"Git status check failed: {stderr}", "ERROR")
            return False
        
        if not stdout.strip():
            self.log("No changes detected - nothing to commit", "INFO")
            return True
        
        # Add all changes
        add_cmd = "git add ."
        success, stdout, stderr = self.run_command(add_cmd, cwd=str(self.public_repo_path))
        
        if not success:
            self.log(f"Git add failed: {stderr}", "ERROR")
            return False
        
        # Commit changes
        commit_cmd = f'git commit -m "{self.commit_message}"'
        success, stdout, stderr = self.run_command(commit_cmd, cwd=str(self.public_repo_path))
        
        if not success:
            self.log(f"Git commit failed: {stderr}", "ERROR")
            return False
        
        # Push to GitHub (if auto_push enabled)
        if self.config.get("auto_push", True):
            push_cmd = f"git push {self.config['git_remote']} {self.config['git_branch']}"
            success, stdout, stderr = self.run_command(push_cmd, cwd=str(self.public_repo_path))
            
            if success:
                self.log("Changes pushed to GitHub successfully", "SUCCESS")
            else:
                self.log(f"Git push failed: {stderr}", "ERROR")
                self.log("Changes committed locally but not pushed to GitHub", "WARNING")
                return False
        else:
            self.log("Auto-push disabled - changes committed locally only", "INFO")
        
        return True
    
    def step_final_verification(self) -> bool:
        """Step 4: Final verification of public repository."""
        if self.dry_run:
            self.log("Final verification skipped in dry run mode")
            return True
        
        self.log("Running final verification on public repository...")
        
        # Quick check for private references in public repo
        detection_cmd = f"python detect_private_components.py --path \"{self.public_repo_path}\" --output final_verification.json"
        success, stdout, stderr = self.run_command(detection_cmd)
        
        if success and Path("final_verification.json").exists():
            try:
                with open("final_verification.json", 'r') as f:
                    results = json.load(f)
                
                references_found = results["scan_metadata"]["total_references"]
                
                if references_found == 0:
                    self.log("Final verification passed - no private references detected", "SUCCESS")
                    # Clean up verification file
                    Path("final_verification.json").unlink()
                    return True
                else:
                    self.log(f"CRITICAL: {references_found} private references found in public repo!", "ERROR")
                    self.log("Review final_verification.json immediately", "ERROR")
                    return False
            
            except json.JSONDecodeError:
                self.log("Final verification results invalid", "WARNING")
                return True  # Don't fail on verification file issues
        else:
            self.log("Final verification could not run - proceeding with caution", "WARNING")
            return True  # Don't fail if verification can't run
    
    def publish(self) -> bool:
        """Execute the complete developer publishing workflow."""
        self.log("🚀 Starting Developer Publishing Workflow")
        self.log(f"Target: {self.public_repo_path}")
        self.log(f"Commit Message: {self.commit_message}")
        self.log(f"Dry Run: {self.dry_run}")
        
        steps = [
            ("Verification", self.step_verification),
            ("Enhanced Sync", self.step_sync),
            ("Git Operations", self.step_git_operations),
            ("Final Verification", self.step_final_verification)
        ]
        
        for step_name, step_method in steps:
            self.log(f"=== {step_name.upper()} ===")
            
            try:
                if not step_method():
                    self.log(f"Step '{step_name}' failed - aborting workflow", "ERROR")
                    return False
            except Exception as e:
                self.log(f"Step '{step_name}' crashed: {e}", "ERROR")
                return False
        
        # Success summary
        self.log("🎉 Developer Publishing Workflow Completed Successfully!", "SUCCESS")
        
        if not self.dry_run:
            if self.config.get("auto_push", True):
                self.log("✅ Changes have been pushed to GitHub", "SUCCESS")
                self.log("🌐 Public repository is updated and live", "SUCCESS")
            else:
                self.log("📝 Changes committed locally - push manually when ready", "INFO")
        else:
            self.log("ℹ️ This was a dry run - use without --dry-run to execute", "INFO")
        
        return True

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Developer Publishing Workflow")
    parser.add_argument("--message", "-m", help="Commit message")
    parser.add_argument("--dry-run", action="store_true", help="Dry run mode")
    
    args = parser.parse_args()
    
    # Initialize publisher
    publisher = DeveloperPublisher(args.message, args.dry_run)
    
    # Execute publishing workflow
    success = publisher.publish()
    
    # Exit with appropriate code
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
