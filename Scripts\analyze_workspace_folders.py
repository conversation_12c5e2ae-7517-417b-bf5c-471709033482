#!/usr/bin/env python3
"""
Workspace Folder Analysis Script
===============================
Analyzes N8N* folders in the parent workspace to identify obsolete directories.
Helps determine which folders are still needed vs. can be cleaned up.

Usage: python analyze_workspace_folders.py
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class WorkspaceFolderAnalyzer:
    """Analyzes workspace folders to identify obsolete directories."""
    
    def __init__(self):
        self.workspace_root = Path("..").resolve()
        self.current_project = Path(".").resolve()
        self.analysis_results = {
            "scan_time": datetime.now().isoformat(),
            "workspace_path": str(self.workspace_root),
            "current_project": str(self.current_project),
            "n8n_folders": {},
            "analysis": {},
            "recommendations": []
        }
    
    def get_folder_info(self, folder_path: Path) -> Dict[str, Any]:
        """Get detailed information about a folder."""
        if not folder_path.exists() or not folder_path.is_dir():
            return {"exists": False}
        
        try:
            # Get basic stats
            stat = folder_path.stat()
            
            # Count files and subdirectories
            total_files = 0
            total_dirs = 0
            total_size = 0
            
            for item in folder_path.rglob('*'):
                if item.is_file():
                    total_files += 1
                    try:
                        total_size += item.stat().st_size
                    except (OSError, PermissionError):
                        pass
                elif item.is_dir():
                    total_dirs += 1
            
            # Check for key indicator files
            key_files = {
                "README.md": (folder_path / "README.md").exists(),
                "requirements.txt": (folder_path / "requirements.txt").exists(),
                "run.py": (folder_path / "run.py").exists(),
                "setup.py": (folder_path / "setup.py").exists(),
                ".git": (folder_path / ".git").exists(),
                "n8n-docker": (folder_path / "n8n-docker").exists(),
                "Self_Healer": (folder_path / "Self_Healer").exists(),
                "Scripts": (folder_path / "Scripts").exists()
            }
            
            # Check for recent activity (files modified in last 30 days)
            recent_files = 0
            cutoff_time = datetime.now().timestamp() - (30 * 24 * 60 * 60)  # 30 days ago
            
            for item in folder_path.rglob('*'):
                if item.is_file():
                    try:
                        if item.stat().st_mtime > cutoff_time:
                            recent_files += 1
                    except (OSError, PermissionError):
                        pass
            
            return {
                "exists": True,
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "total_files": total_files,
                "total_directories": total_dirs,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "key_files": key_files,
                "recent_files": recent_files,
                "has_git": key_files[".git"],
                "appears_active": recent_files > 0
            }
            
        except Exception as e:
            return {
                "exists": True,
                "error": str(e),
                "analysis_failed": True
            }
    
    def find_n8n_folders(self) -> Dict[str, Dict]:
        """Find all N8N* folders in the workspace."""
        n8n_folders = {}
        
        try:
            for item in self.workspace_root.iterdir():
                if item.is_dir() and item.name.upper().startswith('N8N'):
                    folder_info = self.get_folder_info(item)
                    folder_info["relative_path"] = str(item.relative_to(self.workspace_root))
                    folder_info["is_current_project"] = item.resolve() == self.current_project
                    n8n_folders[item.name] = folder_info
        
        except Exception as e:
            print(f"Error scanning workspace: {e}")
        
        return n8n_folders
    
    def analyze_folder_purposes(self, folders: Dict[str, Dict]) -> Dict[str, Any]:
        """Analyze what purpose each folder serves."""
        analysis = {
            "current_project": None,
            "git_repositories": [],
            "backup_folders": [],
            "derivative_folders": [],
            "obsolete_candidates": [],
            "active_projects": []
        }
        
        for name, info in folders.items():
            if not info.get("exists"):
                continue
            
            # Identify current project
            if info.get("is_current_project"):
                analysis["current_project"] = name
            
            # Identify Git repositories
            if info.get("has_git"):
                analysis["git_repositories"].append(name)
            
            # Identify potentially active projects
            if info.get("appears_active") and info.get("total_files", 0) > 10:
                analysis["active_projects"].append(name)
            
            # Identify potential backup/derivative folders
            if ("backup" in name.lower() or 
                "public" in name.lower() or 
                "copy" in name.lower() or
                "old" in name.lower()):
                analysis["backup_folders"].append(name)
            
            # Identify derivative folders (created by scripts)
            if ("_Public" in name or 
                "_Community" in name or
                "_Output" in name):
                analysis["derivative_folders"].append(name)
            
            # Identify obsolete candidates
            if (not info.get("appears_active") and 
                info.get("total_files", 0) < 50 and
                not info.get("has_git")):
                analysis["obsolete_candidates"].append(name)
        
        return analysis
    
    def generate_recommendations(self, folders: Dict, analysis: Dict) -> List[str]:
        """Generate recommendations for folder cleanup."""
        recommendations = []
        
        # Current project
        if analysis["current_project"]:
            recommendations.append(f"KEEP: {analysis['current_project']} (current active project)")
        
        # Git repositories
        for repo in analysis["git_repositories"]:
            if repo != analysis["current_project"]:
                recommendations.append(f"REVIEW: {repo} (has Git repository - check if still needed)")
        
        # Derivative folders
        for folder in analysis["derivative_folders"]:
            folder_info = folders[folder]
            if folder_info.get("appears_active"):
                recommendations.append(f"KEEP: {folder} (derivative folder with recent activity)")
            else:
                recommendations.append(f"CONSIDER REMOVING: {folder} (derivative folder, no recent activity)")
        
        # Backup folders
        for folder in analysis["backup_folders"]:
            recommendations.append(f"REVIEW FOR REMOVAL: {folder} (appears to be backup/copy)")
        
        # Obsolete candidates
        for folder in analysis["obsolete_candidates"]:
            recommendations.append(f"LIKELY OBSOLETE: {folder} (no recent activity, few files, no Git)")
        
        return recommendations
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run complete workspace analysis."""
        print("🔍 Analyzing workspace N8N* folders...")
        
        # Find all N8N folders
        n8n_folders = self.find_n8n_folders()
        self.analysis_results["n8n_folders"] = n8n_folders
        
        # Analyze purposes
        analysis = self.analyze_folder_purposes(n8n_folders)
        self.analysis_results["analysis"] = analysis
        
        # Generate recommendations
        recommendations = self.generate_recommendations(n8n_folders, analysis)
        self.analysis_results["recommendations"] = recommendations
        
        return self.analysis_results
    
    def display_results(self):
        """Display analysis results."""
        print("\n" + "="*70)
        print("📁 WORKSPACE N8N* FOLDER ANALYSIS")
        print("="*70)
        
        folders = self.analysis_results["n8n_folders"]
        analysis = self.analysis_results["analysis"]
        
        print(f"\n📍 Workspace: {self.analysis_results['workspace_path']}")
        print(f"📍 Current Project: {self.analysis_results['current_project']}")
        
        print(f"\n📊 Found {len(folders)} N8N* folders:")
        
        for name, info in folders.items():
            if not info.get("exists"):
                continue
            
            status = "🟢 CURRENT" if info.get("is_current_project") else "📁"
            git_status = "📦 Git" if info.get("has_git") else "📄"
            activity = f"🔥 {info.get('recent_files', 0)} recent" if info.get("appears_active") else "💤 inactive"
            
            print(f"   {status} {name}")
            print(f"      {git_status} | {activity} | {info.get('total_files', 0)} files | {info.get('total_size_mb', 0)} MB")
        
        print(f"\n🎯 Analysis Summary:")
        print(f"   • Current Project: {analysis.get('current_project', 'Not identified')}")
        print(f"   • Git Repositories: {len(analysis.get('git_repositories', []))}")
        print(f"   • Active Projects: {len(analysis.get('active_projects', []))}")
        print(f"   • Derivative Folders: {len(analysis.get('derivative_folders', []))}")
        print(f"   • Backup Folders: {len(analysis.get('backup_folders', []))}")
        print(f"   • Obsolete Candidates: {len(analysis.get('obsolete_candidates', []))}")
        
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(self.analysis_results["recommendations"], 1):
            print(f"   {i}. {rec}")
        
        print("="*70)
    
    def save_results(self, output_file: str = "workspace_analysis.json"):
        """Save analysis results to file."""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2)
        print(f"📄 Analysis saved to: {output_file}")

def main():
    """Main execution function."""
    analyzer = WorkspaceFolderAnalyzer()
    results = analyzer.run_analysis()
    analyzer.display_results()
    analyzer.save_results()

if __name__ == "__main__":
    main()
