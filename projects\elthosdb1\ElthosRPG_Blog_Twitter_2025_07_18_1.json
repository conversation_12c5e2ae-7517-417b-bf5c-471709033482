{"name": "ElthosRPG_Blog_Twitter", "nodes": [{"parameters": {"url": "={{ $json.selectedPostURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [520, 420], "id": "4c585937-5a51-4c9d-bd6b-dd13fd3e9c50"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-180, 360], "id": "64c60cca-814b-4913-8898-43eb016bb8e7", "name": "When clicking 'Execute workflow'"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "mainBlogURL", "value": "https://elthosrpg.blogspot.com/", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-40, 360], "id": "6ef3c140-98d1-4733-9dc6-6a122b3414a1", "name": "Set Main Blog URL"}, {"parameters": {"url": "={{ $json.mainBlogURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Blog Page", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [120, 320], "id": "ac3af3d6-97f8-404b-8d15-edf723021e68"}, {"parameters": {"operation": "extract-content", "html": "={{ $json.data }}"}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [280, 280], "id": "31a071b7-93d6-4c4f-9be5-5a19860530d8", "name": "Scrape Blog Page"}, {"parameters": {"jsCode": "// Get raw HTML from Fetch Blog Page\\nconst rawHtml = $node['Fetch Blog Page'].json.data;\\nconst mainBlogURL = $input.first().json.mainBlogURL;\\n\\nconsole.log('Extracting URLs from raw HTML...');\\nconsole.log('Raw HTML length:', rawHtml.length);\\n\\n// Simple regex to find href='https://elthosrpg.blogspot.com/YYYY/MM/post-name.html'\\nconst matches = rawHtml.match(/href='https:\\/\\/elthosrpg\\.blogspot\\.com\\/\\d{4}\\/\\d{2}\\/[^']+\\.html'/g) || [];\\n\\nconsole.log('Found', matches.length, 'href matches');\\n\\nlet postUrls = [];\\nmatches.forEach(match => {\\n  // Extract URL from href='URL'\\n  const url = match.replace(/href='/, '').replace(/'$/, '');\\n  if (url.includes('/20') && !postUrls.includes(url)) {\\n    postUrls.push(url);\\n    console.log('Added URL:', url);\\n  }\\n});\\n\\nconsole.log('Total unique URLs:', postUrls.length);\\n\\n// If no URLs found, show debug info\\nif (postUrls.length === 0) {\\n  console.log('No URLs found! HTML preview:', rawHtml.substring(0, 500));\\n  return [{ json: { \\n    postUrls: [],\\n    totalPosts: 0,\\n    mainBlogURL: mainBlogURL,\\n    error: 'No URLs found',\\n    htmlPreview: rawHtml.substring(0, 500)\\n  }}];\\n}\\n\\nreturn [{ json: { \\n  postUrls: postUrls,\\n  totalPosts: postUrls.length,\\n  mainBlogURL: mainBlogURL\\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 200], "id": "edc7910d-76fa-459a-8a85-61c6c3040dc1", "name": "Extract Post URLs"}, {"parameters": {"jsCode": "// Randomly select a blog post URL\nconst postUrls = $input.first().json.postUrls;\nconst totalPosts = $input.first().json.totalPosts;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log(`Selecting random post from ${totalPosts} available posts...`);\n\n// Generate random index\nconst randomIndex = Math.floor(Math.random() * postUrls.length);\nconst selectedPostURL = postUrls[randomIndex];\n\nconsole.log(`Selected post: ${selectedPostURL}`);\n\n// Return the selected URL along with metadata\nreturn [{ json: { \n  selectedPostURL: selectedPostURL,\n  randomIndex: randomIndex,\n  totalPosts: totalPosts,\n  mainBlogURL: mainBlogURL\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 200], "id": "331681f4-e8a7-43be-a98f-8bbd8da467cf", "name": "Random Selection"}, {"parameters": {"promptType": "define", "text": "={{ $json.firstParagraph }}", "messages": {"messageValues": [{"message": "You are a twitter expert who can take the best content from the blog entry and make the perfect tweet from it that is no more than 60 characters long. Use Text format. Include hash tags #IndieRPG #Elthos #TTRPG. Be concise and engaging."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1260, 360], "id": "2f4b490f-42df-48d0-876f-230283269f39", "name": "Basic LLM Chain", "alwaysOutputData": false}, {"parameters": {"model": {"__rl": true, "value": "deepseek-r1-distill-llama-8b", "mode": "list", "cachedResultName": "deepseek-r1-distill-llama-8b"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1340, 580], "id": "2f278633-a60c-4f78-88ee-dbb06c8531b4", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "cqdpm9ID0q2zjSkV", "name": "LM Studio"}}}, {"parameters": {"operation": "extract-content", "html": "={{ $('Fetch Post').item.json.data }}", "outputMarkdown": true}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [880, 320], "id": "75a73171-7f1e-4be2-af26-717fd8420fce", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [1760, 360], "id": "66cb9ca5-c337-4121-847a-b2c2a9c0a0a7", "name": "Create Tweet", "credentials": {"twitterOAuth2Api": {"id": "QMuVcnYLarrzm0jp", "name": "X account"}}}, {"parameters": {"jsCode": "// Replace \"content\" with the actual field name from ScrapeNinja output\nconst content = $input.first().json.content;\n\n// Split into paragraphs by double line breaks\nconst paragraphs = content.split(/\\n\\s*\\n/);\n\n// Get the first paragraph\nlet firstParagraph = paragraphs[2] || \"\";\n\n// Limit to 3000 words\nconst words = firstParagraph.split(/\\s+/).slice(0, 3000);\nconst limitedParagraph = words.join(\" \");\n\n// PRESERVE the blogURL field from input\nconst blogURL = $input.first().json.blogURL;\n\nreturn [{ json: { \n    firstParagraph: limitedParagraph,\n    blogURL: blogURL \n}}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 400], "id": "6bcfc3d3-dfd0-478d-b5c5-1ff419677640", "name": "Code - Get Paragraph 1"}, {"parameters": {"jsCode": "// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// Get the blog URL directly from the Set node\nconst blogUrl = $node[\"Edit Field - blogURL\"].json.blogURL;\n\nconsole.log('LLM text:', text);\nconsole.log('Blog URL from Set node:', blogUrl);\n\n// Remove <think>...</think> and everything in between\nconst cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Clean whitespace\ntext = cleaned.replace(/^\\s+|\\s+$/g, '');\n\n// Twitter character limit\nconst TARGET_LIMIT = 280;\n\n// Try the full text + URL first\nconst testTweet = text + ' ' + blogUrl;\nconsole.log('Test tweet length:', testTweet.length);\n\n// If it fits within limit, use it as-is\nif (testTweet.length <= TARGET_LIMIT) {\n    console.log('Tweet fits within limit, using full text');\n    return [{ json: { text: testTweet } }];\n}\n\n// If too long, try to preserve hashtags by truncating main content\nconst hashtagRegex = /#\\w+/g;\nconst hashtags = text.match(hashtagRegex) || [];\nconst hashtagsText = hashtags.join(' ');\n\n// Remove hashtags and em-dashes from main text to see content length\nconst contentWithoutHashtags = text.replace(hashtagRegex, '').replace(/—/g, '').replace(/\\s+/g, ' ').trim();\n\n// More precise calculation: content + \" \" + hashtags + \" \" + URL = TARGET_LIMIT\n// So: content = TARGET_LIMIT - hashtags.length - URL.length - 2 spaces - 3 for \"...\"\nconst spacesNeeded = 2; // one before hashtags, one before URL\nconst ellipsisLength = 3;\nconst availableForContent = TARGET_LIMIT - hashtagsText.length - blogUrl.length - spacesNeeded - ellipsisLength;\n\nconsole.log('Available space for content:', availableForContent);\nconsole.log('Content without hashtags:', contentWithoutHashtags);\nconsole.log('Hashtags found:', hashtags);\nconsole.log('Hashtags text length:', hashtagsText.length);\n\n// If we have enough space, use truncated content + hashtags\nif (availableForContent > 15 && hashtags.length > 0) {\n    const truncatedContent = contentWithoutHashtags.substring(0, availableForContent) + '...';\n    const finalTweet = truncatedContent + ' ' + hashtagsText + ' ' + blogUrl;\n    console.log('Using truncated content with hashtags');\n    console.log('Final tweet length:', finalTweet.length);\n    return [{ json: { text: finalTweet } }];\n}\n\n// Fallback: just truncate everything\nconst maxTextLength = TARGET_LIMIT - blogUrl.length - 1 - 3;\nconst truncatedText = text.substring(0, maxTextLength) + '...';\nconst fallbackTweet = truncatedText + ' ' + blogUrl;\n\nconsole.log('Using fallback truncation');\nreturn [{ json: { text: fallbackTweet } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1580, 420], "id": "c340b437-316f-4814-9909-01e62fbafefc", "name": "Code - Clean for Tweet"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "blogURL", "value": "={{ $json.selectedPostURL }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [700, 420], "id": "d603d6a6-97d4-41bb-b52b-0ac0383997a8", "name": "Edit Field - blogURL"}], "pinData": {}, "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "Set Main Blog URL", "type": "main", "index": 0}]]}, "Set Main Blog URL": {"main": [[{"node": "Fetch Blog Page", "type": "main", "index": 0}]]}, "Fetch Blog Page": {"main": [[{"node": "Scrape Blog Page", "type": "main", "index": 0}]]}, "Scrape Blog Page": {"main": [[{"node": "Extract Post URLs", "type": "main", "index": 0}]]}, "Extract Post URLs": {"main": [[{"node": "Random Selection", "type": "main", "index": 0}]]}, "Random Selection": {"main": [[{"node": "Fetch Post", "type": "main", "index": 0}]]}, "Fetch Post": {"main": [[{"node": "Edit Field - blogURL", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Code - Clean for Tweet", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "ScrapeNinja": {"main": [[{"node": "Code - Get Paragraph 1", "type": "main", "index": 0}]]}, "Code - Get Paragraph 1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Code - Clean for Tweet": {"main": [[{"node": "Create Tweet", "type": "main", "index": 0}]]}, "Edit Field - blogURL": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "7b04c13d-70e8-402d-9abc-35c3c017b5e2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "M4PfByDgjUvSFPqZ", "tags": []}