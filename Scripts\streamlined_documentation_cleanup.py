#!/usr/bin/env python3
"""
Streamlined Documentation Cleanup Script

Instead of trying to merge 75 files with massive redundancy, this script:
1. Identifies which files to KEEP based on target structure
2. Creates a list of files to DELETE
3. Provides a clean, efficient path to the target documentation structure

Author: N8N_Builder Development Team
Date: 2025-07-08
Purpose: Efficient documentation cleanup via deletion rather than merging
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Set

class StreamlinedDocumentationCleanup:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        
        # Define the target structure - files we want to KEEP
        self.target_files = {
            # Root level files to keep
            'README.md',
            'GETTING_STARTED.md',
            'FEATURES.md',

            # Documentation folder structure
            'Documentation/DevelopersWorkflow.md',
        }

        # Files that exist but need to be renamed/moved to target structure
        self.files_to_rename = {
            'Documentation/ARCHITECTURE.md': 'Documentation/Architecture.md',
            'Documentation/TROUBLESHOOTING.md': 'Documentation/guides/Troubleshooting.md',
            'Documentation/guides/INTEGRATION_SETUP.md': 'Documentation/guides/Integration.md',
            'Documentation/api/API_DOCUMENTATION.md': 'Documentation/api/API_Reference.md'
        }

        # Files we still need to create (don't exist in any form)
        self.files_to_create_new = {
            'Documentation/DesignPrinciples.md',
            'Documentation/technical/Specifications.md'
        }
        
        # Files that might exist and should be kept if they have good content
        self.conditional_keep_files = {
            'Documentation/ProcessFlow.md',  # Auto-generated, might be useful
            'Documentation/SQLConventions.md',  # Specific technical content
            'Scripts/README.md'  # Script documentation
        }
        
        # Folders to completely ignore (don't touch)
        self.ignore_folders = {
            'venv', '.git', '.vscode', '__pycache__', 'node_modules',
            'n8n_builder',  # Application code
            'tests',        # Test files
            'config',       # Configuration files
            'data'          # Data files (including our analysis)
        }
        
        self.analysis_results = {
            'files_to_keep': [],
            'files_to_delete': [],
            'files_to_create': [],
            'folders_to_create': [],
            'summary': {}
        }
    
    def scan_current_files(self) -> List[str]:
        """Scan for all current markdown files"""
        current_md_files = []
        
        for root, dirs, files in os.walk(self.root_path):
            # Skip ignored folders
            dirs[:] = [d for d in dirs if d not in self.ignore_folders]
            
            root_path = Path(root)
            for file in files:
                if file.endswith('.md'):
                    file_path = root_path / file
                    relative_path = file_path.relative_to(self.root_path)
                    current_md_files.append(str(relative_path).replace('\\', '/'))
        
        return current_md_files
    
    def analyze_files(self):
        """Analyze which files to keep, delete, rename, or create"""
        current_files = self.scan_current_files()
        current_files_set = set(current_files)

        # Files to keep as-is (exist and are in target structure)
        files_to_keep = []
        for target_file in self.target_files:
            if target_file in current_files_set:
                files_to_keep.append(target_file)

        # Files to rename/move (exist but need to be moved to target structure)
        files_to_rename = []
        for source_file, target_file in self.files_to_rename.items():
            if source_file in current_files_set:
                files_to_rename.append({'source': source_file, 'target': target_file})
                files_to_keep.append(source_file)  # Keep source until renamed

        # Check conditional files
        for conditional_file in self.conditional_keep_files:
            if conditional_file in current_files_set:
                files_to_keep.append(conditional_file)

        # Files to delete (everything else)
        files_to_delete = []
        for current_file in current_files:
            if current_file not in files_to_keep:
                files_to_delete.append(current_file)

        # Files to create (don't exist in any form)
        files_to_create = []
        for target_file in self.files_to_create_new:
            files_to_create.append(target_file)

        # Folders to create
        folders_to_create = set()
        all_target_files = list(self.target_files) + [item['target'] for item in files_to_rename] + list(self.files_to_create_new)
        for target_file in all_target_files:
            folder = str(Path(target_file).parent)
            if folder != '.' and folder not in ['Documentation']:
                folders_to_create.add(folder)

        self.analysis_results = {
            'files_to_keep': sorted(files_to_keep),
            'files_to_rename': files_to_rename,
            'files_to_delete': sorted(files_to_delete),
            'files_to_create': sorted(files_to_create),
            'folders_to_create': sorted(list(folders_to_create)),
            'summary': {
                'current_files': len(current_files),
                'files_to_keep': len(files_to_keep),
                'files_to_rename': len(files_to_rename),
                'files_to_delete': len(files_to_delete),
                'files_to_create': len(files_to_create),
                'reduction_percentage': round((len(files_to_delete) / len(current_files)) * 100, 1) if current_files else 0
            }
        }
    
    def generate_cleanup_plan(self, output_file: str):
        """Generate a cleanup plan document"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Streamlined Documentation Cleanup Plan\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            summary = self.analysis_results['summary']
            f.write("## 📊 Cleanup Summary\n\n")
            f.write(f"- **Current Files:** {summary['current_files']} markdown files\n")
            f.write(f"- **Files to Keep:** {summary['files_to_keep']} files\n")
            f.write(f"- **Files to Rename/Move:** {summary['files_to_rename']} files\n")
            f.write(f"- **Files to Delete:** {summary['files_to_delete']} files\n")
            f.write(f"- **Files to Create:** {summary['files_to_create']} files\n")
            f.write(f"- **Reduction:** {summary['reduction_percentage']}% of files will be deleted\n\n")
            
            f.write("## ✅ Files to Keep\n\n")
            f.write("These files align with our target structure:\n\n")
            for file in self.analysis_results['files_to_keep']:
                f.write(f"- `{file}`\n")
            f.write("\n")

            f.write("## 🔄 Files to Rename/Move\n\n")
            f.write("These files have good content but need to be moved to target structure:\n\n")
            for rename_info in self.analysis_results['files_to_rename']:
                f.write(f"- `{rename_info['source']}` → `{rename_info['target']}`\n")
            f.write("\n")


            f.write("## 🗑️ Files to Delete\n\n")
            f.write("These files will be deleted (redundant/obsolete):\n\n")
            for file in self.analysis_results['files_to_delete']:
                f.write(f"- `{file}`\n")
            f.write("\n")

            f.write("## 📝 Files to Create\n\n")
            f.write("These target files don't exist yet and need to be created:\n\n")
            for file in self.analysis_results['files_to_create']:
                f.write(f"- `{file}`\n")
            f.write("\n")

            f.write("## 📁 Folders to Create\n\n")
            if self.analysis_results['folders_to_create']:
                for folder in self.analysis_results['folders_to_create']:
                    f.write(f"- `{folder}/`\n")
            else:
                f.write("All required folders already exist.\n")
            f.write("\n")
            
            f.write("## 🎯 Target Structure\n\n")
            f.write("```\n")
            f.write("ROOT/\n")
            f.write("├── README.md                    # Project overview & quick start\n")
            f.write("├── GETTING_STARTED.md          # Comprehensive setup guide\n")
            f.write("├── FEATURES.md                 # Feature overview\n")
            f.write("└── Documentation/\n")
            f.write("    ├── Architecture.md         # Technical architecture\n")
            f.write("    ├── DesignPrinciples.md    # Design philosophy\n")
            f.write("    ├── DevelopersWorkflow.md   # Developer guide\n")
            f.write("    ├── guides/\n")
            f.write("    │   ├── Troubleshooting.md  # Consolidated troubleshooting\n")
            f.write("    │   └── Integration.md      # Integration guides\n")
            f.write("    ├── technical/\n")
            f.write("    │   └── Specifications.md   # Detailed technical specs\n")
            f.write("    └── api/\n")
            f.write("        └── API_Reference.md    # API documentation\n")
            f.write("```\n\n")
            
            f.write("## ⚡ Execution Steps\n\n")
            f.write("1. **Create missing folders** (if any)\n")
            f.write("2. **Create missing target files** with basic structure\n")
            f.write("3. **Delete obsolete files** (backup first if needed)\n")
            f.write("4. **Review and clean remaining files** for obsolete references\n")
            f.write("5. **Update cross-references** in kept files\n\n")
            
            f.write("## ⚠️ Safety Notes\n\n")
            f.write("- All files are in git, so deletion is reversible\n")
            f.write("- Focus on the target structure rather than trying to preserve everything\n")
            f.write("- This approach is much faster than trying to merge 75 files\n")
            f.write("- Clean slate approach eliminates redundancy by design\n")
    
    def save_analysis(self, output_file: str):
        """Save the analysis results as JSON"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)

def main():
    """Main execution function"""
    print("N8N_Builder Streamlined Documentation Cleanup")
    print("=" * 50)
    
    root_path = os.getcwd()
    print(f"Analyzing documentation in: {root_path}")
    
    cleanup = StreamlinedDocumentationCleanup(root_path)
    print("Analyzing current files...")
    cleanup.analyze_files()
    
    # Generate outputs
    plan_file = "data/streamlined_cleanup_plan.md"
    analysis_file = "data/streamlined_cleanup_analysis.json"
    
    print(f"Generating cleanup plan: {plan_file}")
    cleanup.generate_cleanup_plan(plan_file)
    
    print(f"Saving analysis data: {analysis_file}")
    cleanup.save_analysis(analysis_file)
    
    # Print summary
    summary = cleanup.analysis_results['summary']
    print(f"\nStreamlined Cleanup Analysis:")
    print(f"- Current files: {summary['current_files']}")
    print(f"- Files to keep: {summary['files_to_keep']}")
    print(f"- Files to delete: {summary['files_to_delete']}")
    print(f"- Files to create: {summary['files_to_create']}")
    print(f"- Reduction: {summary['reduction_percentage']}%")
    print(f"\nPlan saved to: {plan_file}")

if __name__ == "__main__":
    main()
