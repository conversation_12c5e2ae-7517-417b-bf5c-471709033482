# Simple commit cleanup script
# Run this manually in PowerShell

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
Write-Host "🔧 Starting commit message cleanup..." -ForegroundColor Yellow

# Create backup
$backupName = "backup-cleanup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Write-Host "📋 Creating backup: $backupName" -ForegroundColor Cyan
git branch $backupName

# Set environment variable
$env:FILTER_BRANCH_SQUELCH_WARNING = 1

Write-Host "🔄 Running cleanup (this will take a few minutes)..." -ForegroundColor Yellow

# Single comprehensive filter-branch command
$result = git filter-branch -f --msg-filter @'
case "$1" in
"Remove additional private Self-Healer and KnowledgeBase development files") echo "Remove additional private development files" ;;
"Enhanced .gitignore to comprehensively exclude all private Self-Healer and KnowledgeBase components") echo "Enhanced .gitignore to exclude private components" ;;
"Remove private Self-Healer and KnowledgeBase components from public repository") echo "Remove private components from public repository" ;;
"update gitignore for self-healer and knowledgebase") echo "update gitignore for private components" ;;
"Updates for Self-Healer Separation Finalization") echo "Updates for system separation finalization" ;;
"Updates to KnowledgeBase Table structures and SP") echo "Updates to database table structures and SP" ;;
"Updates to Self-Healer") echo "Updates to system components" ;;
"Self-Healder - KnowledgeBase Fix") echo "System component fixes" ;;
"Self-Healer & KnowledgeBase Updates 4") echo "System component updates 4" ;;
"Self-Healer & KnowledgeBase Updates 3") echo "System component updates 3" ;;
"Self-Healer & KnowledgeBse Integration Updates 2") echo "System integration updates 2" ;;
"Self-Healer & KnowledgeBase Integration Updates") echo "System integration updates" ;;
"KnowledgeBase Updates") echo "Database component updates" ;;
"Update to Full version of Self-Healer") echo "Update to full system version" ;;
"Fixes to Self-Healer and Documentation") echo "Fixes to system and documentation" ;;
"Updates to Self-Healer / KnowledgeBase") echo "Updates to system components" ;;
"Self-Healer Finalized and Integrated with KnowledgeBase - First Pass") echo "System integration finalized - first pass" ;;
"Self Healer Updates") echo "System component updates" ;;
"Self-Healer") echo "System component implementation" ;;
*) echo "$1" ;;
esac
'@ -- --all

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Cleanup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next steps:" -ForegroundColor Cyan
    Write-Host "1. Verify: git log --oneline | head -10" -ForegroundColor White
    Write-Host "2. Push: git push --force-with-lease" -ForegroundColor White
    Write-Host "3. If problems: git reset --hard $backupName" -ForegroundColor White
} else {
    Write-Host "❌ Cleanup failed!" -ForegroundColor Red
    Write-Host "💡 Restore with: git reset --hard $backupName" -ForegroundColor Yellow
}
