#!/usr/bin/env python3
"""
Documentation Quality Validation Script for N8N_Builder

This script provides comprehensive validation of all *.md files to ensure:
1. Clean, concise, and accurate content
2. Quick developer onboarding capability
3. Clear LocalTunnel setup instructions
4. Consistent formatting and structure
5. Working links and references

Author: N8N_Builder Team
Date: 2025-01-16
Purpose: Automated documentation quality assurance
"""
import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any
from datetime import datetime

class DocumentationValidator:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.issues = []
        self.recommendations = []
        self.stats = {}
        
    def find_markdown_files(self) -> List[Path]:
        """Find all markdown files in the project"""
        md_files = []
        for root, dirs, files in os.walk(self.project_root):
            # Skip certain directories
            skip_dirs = {'venv', '__pycache__', '.git', 'node_modules', 'archive'}
            dirs[:] = [d for d in dirs if d not in skip_dirs]
            
            for file in files:
                if file.endswith('.md'):
                    md_files.append(Path(root) / file)
        return sorted(md_files)
    
    def analyze_file_structure(self, file_path: Path) -> Dict[str, Any]:
        """Analyze the structure and content of a markdown file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {"error": f"Could not read file: {e}"}
        
        lines = content.split('\n')
        
        analysis = {
            "file_path": str(file_path.relative_to(self.project_root)),
            "line_count": len(lines),
            "word_count": len(content.split()),
            "headers": self.extract_headers(content),
            "links": self.extract_links(content),
            "code_blocks": self.count_code_blocks(content),
            "has_toc": "## Table of Contents" in content or "# Table of Contents" in content,
            "has_quick_start": any("quick start" in line.lower() for line in lines[:20]),
            "tunnel_references": self.check_tunnel_references(content),
            "readability_score": self.assess_readability(content),
            "structure_score": self.assess_structure(content, lines),
            "issues": [],
            "recommendations": []
        }
        
        # Check for specific issues
        self.check_file_issues(analysis, content, lines)
        
        return analysis
    
    def extract_headers(self, content: str) -> List[Dict[str, Any]]:
        """Extract all headers from markdown content"""
        headers = []
        for match in re.finditer(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE):
            level = len(match.group(1))
            text = match.group(2).strip()
            headers.append({"level": level, "text": text})
        return headers
    
    def extract_links(self, content: str) -> List[Dict[str, str]]:
        """Extract all links from markdown content"""
        links = []
        # Markdown links [text](url)
        for match in re.finditer(r'\[([^\]]+)\]\(([^)]+)\)', content):
            links.append({"text": match.group(1), "url": match.group(2), "type": "markdown"})
        # HTML links <a href="url">text</a>
        for match in re.finditer(r'<a\s+href=["\']([^"\']+)["\'][^>]*>([^<]+)</a>', content):
            links.append({"text": match.group(2), "url": match.group(1), "type": "html"})
        return links
    
    def count_code_blocks(self, content: str) -> Dict[str, int]:
        """Count different types of code blocks"""
        return {
            "fenced": len(re.findall(r'```', content)) // 2,
            "inline": len(re.findall(r'`[^`]+`', content)),
            "bash_blocks": len(re.findall(r'```bash', content)),
            "powershell_blocks": len(re.findall(r'```powershell', content))
        }
    
    def check_tunnel_references(self, content: str) -> Dict[str, Any]:
        """Check for tunnel-related content and accuracy"""
        tunnel_info = {
            "has_localtunnel": "localtunnel" in content.lower() or "loca.lt" in content.lower(),
            "has_localhost_run": "localhost.run" in content.lower(),
            "has_ngrok": "ngrok" in content.lower(),
            "has_oauth_setup": "oauth" in content.lower(),
            "tunnel_method_count": 0,
            "preferred_method": None
        }
        
        # Count different tunnel methods mentioned
        methods = ["localtunnel", "localhost.run", "ngrok", "cloudflared"]
        for method in methods:
            if method in content.lower():
                tunnel_info["tunnel_method_count"] += 1
        
        # Determine preferred method based on content
        if "localtunnel" in content.lower() and "recommended" in content.lower():
            tunnel_info["preferred_method"] = "localtunnel"
        elif "localhost.run" in content.lower() and "ssh" in content.lower():
            tunnel_info["preferred_method"] = "localhost.run"
        
        return tunnel_info
    
    def assess_readability(self, content: str) -> Dict[str, Any]:
        """Assess readability of the content"""
        words = content.split()
        sentences = re.split(r'[.!?]+', content)
        
        avg_words_per_sentence = len(words) / max(len(sentences), 1)
        
        # Check for readability indicators
        has_bullets = '- ' in content or '* ' in content
        has_numbered_lists = bool(re.search(r'^\d+\.', content, re.MULTILINE))
        has_tables = '|' in content and '---' in content
        has_emojis = bool(re.search(r'[🎯🚀📖🔧⚡✅🌟💡🤖🔍]', content))
        
        return {
            "avg_words_per_sentence": round(avg_words_per_sentence, 1),
            "has_bullets": has_bullets,
            "has_numbered_lists": has_numbered_lists,
            "has_tables": has_tables,
            "has_emojis": has_emojis,
            "readability_score": self.calculate_readability_score(content)
        }
    
    def calculate_readability_score(self, content: str) -> int:
        """Calculate a simple readability score (1-10)"""
        score = 5  # Base score
        
        # Positive factors
        if len(content.split()) < 2000:  # Not too long
            score += 1
        if '## ' in content:  # Has sections
            score += 1
        if '```' in content:  # Has code examples
            score += 1
        if bool(re.search(r'[🎯🚀📖🔧⚡✅🌟💡🤖🔍]', content)):  # Has emojis
            score += 1
        
        # Negative factors
        if len(content.split()) > 5000:  # Too long
            score -= 2
        if content.count('\n\n') < 5:  # Not enough paragraphs
            score -= 1
        
        return max(1, min(10, score))
    
    def assess_structure(self, content: str, lines: List[str]) -> Dict[str, Any]:
        """Assess the structural quality of the document"""
        structure = {
            "has_title": lines[0].startswith('# ') if lines else False,
            "has_sections": '## ' in content,
            "has_subsections": '### ' in content,
            "logical_flow": True,  # Will be assessed
            "structure_score": 5
        }
        
        # Check for logical flow
        headers = self.extract_headers(content)
        if headers:
            # Check if headers follow logical progression
            levels = [h["level"] for h in headers]
            structure["logical_flow"] = self.check_header_progression(levels)
        
        # Calculate structure score
        score = 5
        if structure["has_title"]:
            score += 1
        if structure["has_sections"]:
            score += 1
        if structure["has_subsections"]:
            score += 1
        if structure["logical_flow"]:
            score += 1
        else:
            score -= 1
        
        structure["structure_score"] = max(1, min(10, score))
        return structure
    
    def check_header_progression(self, levels: List[int]) -> bool:
        """Check if header levels follow logical progression"""
        if not levels:
            return True
        
        # Should start with level 1
        if levels[0] != 1:
            return False
        
        # Check for reasonable progression
        for i in range(1, len(levels)):
            # Jump more than 1 level is questionable
            if levels[i] - levels[i-1] > 1:
                return False
        
        return True
    
    def check_file_issues(self, analysis: Dict[str, Any], content: str, lines: List[str]):
        """Check for specific issues in the file"""
        file_path = analysis["file_path"]
        
        # Check for common issues
        if analysis["line_count"] > 300:
            analysis["issues"].append("File is very long (>300 lines) - consider splitting")
        
        if analysis["word_count"] > 3000:
            analysis["issues"].append("File is very wordy (>3000 words) - consider condensing")
        
        if not analysis["headers"]:
            analysis["issues"].append("No headers found - poor structure")
        
        readability_score = analysis.get("readability_score", {})
        if isinstance(readability_score, dict):
            readability_score = readability_score.get("readability_score", 5)
        if readability_score < 4:
            analysis["issues"].append("Low readability score - needs improvement")

        structure_score = analysis.get("structure_score", {})
        if isinstance(structure_score, dict):
            structure_score = structure_score.get("structure_score", 5)
        if structure_score < 4:
            analysis["issues"].append("Poor structure - needs reorganization")
        
        # Check for outdated tunnel references
        if "ngrok" in content.lower() and "localtunnel" not in content.lower():
            analysis["issues"].append("References outdated ngrok instead of LocalTunnel")
        
        # Check for missing quick start in key files
        key_files = ["README.md", "GETTING_STARTED.md"]
        if any(kf in file_path for kf in key_files) and not analysis["has_quick_start"]:
            analysis["issues"].append("Key file missing quick start section")
        
        # File-specific checks
        if "tunnel" in file_path.lower():
            self.check_tunnel_documentation(analysis, content)
        
        if "getting_started" in file_path.lower():
            self.check_getting_started_quality(analysis, content)
    
    def check_tunnel_documentation(self, analysis: Dict[str, Any], content: str):
        """Check tunnel documentation for completeness"""
        required_elements = [
            ("OAuth callback URL", "oauth.*callback"),
            ("Step-by-step process", r"step \d+|^\d+\."),
            ("Troubleshooting", "troubleshoot"),
            ("Prerequisites", "prerequisite|requirement")
        ]
        
        for element_name, pattern in required_elements:
            if not re.search(pattern, content, re.IGNORECASE):
                analysis["issues"].append(f"Missing {element_name} in tunnel documentation")
        
        # Check for LocalTunnel preference
        if "ngrok" in content.lower() and "localtunnel" not in content.lower():
            analysis["recommendations"].append("Update to prefer LocalTunnel over ngrok")
    
    def check_getting_started_quality(self, analysis: Dict[str, Any], content: str):
        """Check getting started documentation quality"""
        essential_elements = [
            ("Installation steps", r"install|setup|pip install"),
            ("Quick start", r"quick start|getting started"),
            ("First workflow", r"first workflow|example"),
            ("Troubleshooting", r"troubleshoot|common issues")
        ]
        
        for element_name, pattern in essential_elements:
            if not re.search(pattern, content, re.IGNORECASE):
                analysis["issues"].append(f"Getting Started missing: {element_name}")
    
    def validate_all_files(self) -> Dict[str, Any]:
        """Validate all markdown files and generate comprehensive report"""
        md_files = self.find_markdown_files()
        
        print(f"🔍 Found {len(md_files)} markdown files to validate...")
        
        file_analyses = []
        total_issues = 0
        total_recommendations = 0
        
        for file_path in md_files:
            print(f"  📄 Analyzing {file_path.relative_to(self.project_root)}")
            analysis = self.analyze_file_structure(file_path)
            file_analyses.append(analysis)
            
            total_issues += len(analysis.get("issues", []))
            total_recommendations += len(analysis.get("recommendations", []))
        
        # Generate summary statistics
        summary = self.generate_summary(file_analyses)
        
        report = {
            "validation_date": datetime.now().isoformat(),
            "total_files": len(md_files),
            "total_issues": total_issues,
            "total_recommendations": total_recommendations,
            "summary": summary,
            "file_analyses": file_analyses,
            "priority_actions": self.generate_priority_actions(file_analyses)
        }
        
        return report
    
    def generate_summary(self, analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics from all analyses"""
        if not analyses:
            return {}
        
        total_lines = sum(a.get("line_count", 0) for a in analyses)
        total_words = sum(a.get("word_count", 0) for a in analyses)
        # Handle readability scores (could be dict or int)
        readability_scores = []
        structure_scores = []

        for a in analyses:
            r_score = a.get("readability_score", {})
            if isinstance(r_score, dict):
                r_score = r_score.get("readability_score", 5)
            readability_scores.append(r_score)

            s_score = a.get("structure_score", {})
            if isinstance(s_score, dict):
                s_score = s_score.get("structure_score", 5)
            structure_scores.append(s_score)

        avg_readability = sum(readability_scores) / len(readability_scores) if readability_scores else 5
        avg_structure = sum(structure_scores) / len(structure_scores) if structure_scores else 5
        
        files_with_issues = len([a for a in analyses if a.get("issues")])
        files_needing_tunnel_updates = len([a for a in analyses 
                                          if any("ngrok" in issue.lower() for issue in a.get("issues", []))])
        
        return {
            "total_lines": total_lines,
            "total_words": total_words,
            "average_readability_score": round(avg_readability, 1),
            "average_structure_score": round(avg_structure, 1),
            "files_with_issues": files_with_issues,
            "files_needing_tunnel_updates": files_needing_tunnel_updates,
            "documentation_health": "Good" if avg_readability > 6 and avg_structure > 6 else "Needs Improvement"
        }
    
    def generate_priority_actions(self, analyses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate prioritized list of actions needed"""
        actions = []
        
        # High priority: Files with many issues
        for analysis in analyses:
            issues = analysis.get("issues", [])
            if len(issues) >= 3:
                actions.append({
                    "priority": "HIGH",
                    "file": analysis["file_path"],
                    "action": f"Fix {len(issues)} critical issues",
                    "issues": issues[:3]  # Top 3 issues
                })
        
        # Medium priority: Tunnel documentation updates
        for analysis in analyses:
            if any("ngrok" in issue.lower() for issue in analysis.get("issues", [])):
                actions.append({
                    "priority": "MEDIUM",
                    "file": analysis["file_path"],
                    "action": "Update tunnel documentation to prefer LocalTunnel",
                    "details": "Replace ngrok references with LocalTunnel instructions"
                })
        
        # Low priority: Readability improvements
        for analysis in analyses:
            readability_score = analysis.get("readability_score", {})
            if isinstance(readability_score, dict):
                readability_score = readability_score.get("readability_score", 10)
            if readability_score < 5:
                actions.append({
                    "priority": "LOW",
                    "file": analysis["file_path"],
                    "action": "Improve readability",
                    "details": "Add structure, examples, or reduce complexity"
                })
        
        return sorted(actions, key=lambda x: {"HIGH": 1, "MEDIUM": 2, "LOW": 3}[x["priority"]])

def main():
    """Main execution function"""
    print("🚀 N8N_Builder Documentation Quality Validator")
    print("=" * 50)
    
    validator = DocumentationValidator()
    report = validator.validate_all_files()
    
    # Save detailed report
    report_path = Path("data/documentation_quality_report.json")
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # Generate human-readable summary
    summary_path = Path("data/documentation_quality_summary.md")
    generate_summary_report(report, summary_path)
    
    print(f"\n✅ Validation complete!")
    print(f"📊 Detailed report: {report_path}")
    print(f"📋 Summary report: {summary_path}")
    print(f"\n📈 Results:")
    print(f"  - Files analyzed: {report['total_files']}")
    print(f"  - Issues found: {report['total_issues']}")
    print(f"  - Recommendations: {report['total_recommendations']}")
    print(f"  - Documentation health: {report['summary']['documentation_health']}")
    
    if report['priority_actions']:
        print(f"\n🎯 Top Priority Actions:")
        for action in report['priority_actions'][:3]:
            print(f"  {action['priority']}: {action['file']} - {action['action']}")

def generate_summary_report(report: Dict[str, Any], output_path: Path):
    """Generate human-readable summary report"""
    content = f"""# Documentation Quality Report

**Generated**: {report['validation_date']}

## 📊 Summary Statistics

- **Total Files**: {report['total_files']}
- **Total Issues**: {report['total_issues']}
- **Total Recommendations**: {report['total_recommendations']}
- **Documentation Health**: {report['summary']['documentation_health']}
- **Average Readability**: {report['summary']['average_readability_score']}/10
- **Average Structure**: {report['summary']['average_structure_score']}/10

## 🎯 Priority Actions

"""
    
    for action in report['priority_actions']:
        content += f"### {action['priority']} Priority: {action['file']}\n"
        content += f"**Action**: {action['action']}\n\n"
        if 'issues' in action:
            content += "**Issues**:\n"
            for issue in action['issues']:
                content += f"- {issue}\n"
        if 'details' in action:
            content += f"**Details**: {action['details']}\n"
        content += "\n"
    
    content += "## 📋 File-by-File Analysis\n\n"
    
    for analysis in report['file_analyses']:
        if analysis.get('issues') or analysis.get('recommendations'):
            content += f"### {analysis['file_path']}\n"
            content += f"- **Lines**: {analysis['line_count']}\n"
            content += f"- **Words**: {analysis['word_count']}\n"
            content += f"- **Readability**: {analysis['readability_score']}/10\n"
            content += f"- **Structure**: {analysis['structure_score']}/10\n"
            
            if analysis.get('issues'):
                content += "\n**Issues**:\n"
                for issue in analysis['issues']:
                    content += f"- {issue}\n"
            
            if analysis.get('recommendations'):
                content += "\n**Recommendations**:\n"
                for rec in analysis['recommendations']:
                    content += f"- {rec}\n"
            
            content += "\n"
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)

if __name__ == "__main__":
    main()
