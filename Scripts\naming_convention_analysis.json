{"scan_time": "2025-07-05T16:04:41.534520", "public_files": {"config.yaml": {"exists": true, "size": 2278, "modified": "2025-07-01T19:58:14.487708", "hash": "f3b04a086eaef89f38d38748748f5721", "path": "config_public.yaml"}, "README.md": {"exists": true, "size": 6727, "modified": "2025-07-01T19:55:51.386619", "hash": "815b00cc0ad4bc58918827502cebc44f", "path": "README_public.md"}, "requirements.txt": {"exists": true, "size": 645, "modified": "2025-07-01T19:55:11.892572", "hash": "e02c03874ff851eefeb0cc754401c90c", "path": "requirements_public.txt"}, "run.py": {"exists": true, "size": 7834, "modified": "2025-07-01T19:54:56.852611", "hash": "bab2a3d4e16cf24b8c59d01cfeaeec92", "path": "run_public.py"}, "setup.py": {"exists": true, "size": 1916, "modified": "2025-07-01T19:56:21.788566", "hash": "495737eaa307957565dcfdf747e87088", "path": "setup_public.py"}}, "community_files": {"README.md": {"exists": true, "size": 7216, "modified": "2025-07-04T23:48:58.844254", "hash": "7fcfcf5ed17ac5832eb18020d59b7df3", "path": "README_community.md"}}, "migration_plan": {"README.md": {"public_file": {"exists": true, "size": 6727, "modified": "2025-07-01T19:55:51.386619", "hash": "815b00cc0ad4bc58918827502cebc44f", "path": "README_public.md"}, "community_file": {"exists": true, "size": 7216, "modified": "2025-07-04T23:48:58.844254", "hash": "7fcfcf5ed17ac5832eb18020d59b7df3", "path": "README_community.md"}, "comparison": "different_files", "recommended_action": "manual_review_required"}, "requirements.txt": {"public_file": {"exists": true, "size": 645, "modified": "2025-07-01T19:55:11.892572", "hash": "e02c03874ff851eefeb0cc754401c90c", "path": "requirements_public.txt"}, "community_file": {"exists": false}, "comparison": "public_only", "recommended_action": "rename_public_to_community"}, "run.py": {"public_file": {"exists": true, "size": 7834, "modified": "2025-07-01T19:54:56.852611", "hash": "bab2a3d4e16cf24b8c59d01cfeaeec92", "path": "run_public.py"}, "community_file": {"exists": false}, "comparison": "public_only", "recommended_action": "rename_public_to_community"}, "setup.py": {"public_file": {"exists": true, "size": 1916, "modified": "2025-07-01T19:56:21.788566", "hash": "495737eaa307957565dcfdf747e87088", "path": "setup_public.py"}, "community_file": {"exists": false}, "comparison": "public_only", "recommended_action": "rename_public_to_community"}, "config.yaml": {"public_file": {"exists": true, "size": 2278, "modified": "2025-07-01T19:58:14.487708", "hash": "f3b04a086eaef89f38d38748748f5721", "path": "config_public.yaml"}, "community_file": {"exists": false}, "comparison": "public_only", "recommended_action": "rename_public_to_community"}}, "recommendations": ["REVIEW: README.md - files differ, manual comparison needed", "RENAME: requirements_public.txt → requirements_community.txt", "RENAME: run_public.py → run_community.py", "RENAME: setup_public.py → setup_community.py", "RENAME: config_public.yaml → config_community.yaml"]}