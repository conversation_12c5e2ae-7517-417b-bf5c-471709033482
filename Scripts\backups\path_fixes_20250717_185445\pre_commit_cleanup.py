"""
Pre-Commit Cleanup Script for N8N Builder
Prepares the project for GitHub commit by cleaning up unnecessary files
"""

import os
import shutil
from pathlib import Path
import json
import datetime
from typing import List, Set


class PreCommitCleanup:
    """Handles pre-commit cleanup operations for the N8N Builder project."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.cleanup_log = []
        self.files_removed = 0
        self.space_saved = 0
        
        # Files and directories to exclude from Git (UPDATED - removed *.pyd)
        self.git_ignore_patterns = {
            # Python cache (safe to ignore, will be regenerated)
            '__pycache__/',
            '*.pyc',
            '*.pyo',
            # NOTE: Removed *.pyd - these are essential compiled extensions!

            # Python build artifacts
            '.Python',
            'build/',
            'develop-eggs/',
            'dist/',
            'downloads/',
            'eggs/',
            '.eggs/',
            'lib/',
            'lib64/',
            'parts/',
            'sdist/',
            'var/',
            'wheels/',
            '*.egg-info/',
            '.installed.cfg',
            '*.egg',

            # Virtual environments (entire directories)
            'venv/',
            'env/',
            'ENV/',
            '.venv/',

            # IDE files
            '.vscode/',
            '.idea/',
            '*.swp',
            '*.swo',
            '*~',

            # OS files
            '.DS_Store',
            'Thumbs.db',
            'desktop.ini',

            # Log files
            '*.log',
            'logs/',

            # Temporary files
            '*.tmp',
            '*.temp',
            '.cache/',

            # Node.js (if any)
            'node_modules/',
            'npm-debug.log*',
            'yarn-debug.log*',
            'yarn-error.log*',

            # Coverage reports
            'htmlcov/',
            '.coverage',
            '.coverage.*',
            'coverage.xml',
            '*.cover',
            '.hypothesis/',
            '.pytest_cache/',

            # Backup files
            '*.backup.*',
            '*.bak',

            # Analysis reports (keep only latest in repo)
            'project_analysis_report.json',
            'safe_project_analysis_*.json',
            'safe_project_analysis_*.md',
            'pre_commit_cleanup_summary.json',

            # Archive directories from cleanup
            'Archive/',
            'Backup/'
        }
        
        # Files to definitely remove before commit
        self.files_to_remove = {
            # Temporary analysis files
            'feedback_log.json',
            'ProcessFlow.MD',  # Auto-generated, will be recreated

            # Backup files from our cleanup
            'websockets.exe.backup.*',

            # Large temporary files
            'project_analysis_report.json',  # Keep only if recent

            # Test files
            'test_markdown_creation.md',
            'debug_project_analysis_report.md'
        }

        # Essential file patterns to NEVER remove
        self.essential_patterns = {
            # Python compiled extensions (ESSENTIAL for Windows)
            '*.pyd',  # Python extension modules
            '*.dll',  # Dynamic link libraries
            '*.so',   # Shared objects (Unix equivalent)

            # Essential executables
            'python.exe',
            'pip.exe',
            'Scripts/*.exe',  # Virtual environment executables

            # Package metadata and certificates
            '*.pem',  # SSL certificates
            '*.dist-info/*',  # Package metadata
            '*.egg-info/*',   # Package metadata

            # Essential Python files
            '__init__.py',  # Package initialization
            'site.py',      # Site configuration
        }
        
        # Directories to clean up
        self.dirs_to_clean = {
            'logs',
            'cache',
            'temp',
            '__pycache__',
            '.pytest_cache',
            'htmlcov'
        }
    
    def analyze_current_state(self):
        """Analyze current project state."""
        print("🔍 Analyzing current project state...")
        
        total_files = 0
        total_size = 0
        file_types = {}
        large_files = []
        
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                try:
                    size = file_path.stat().st_size
                    total_files += 1
                    total_size += size
                    
                    # Track file types
                    ext = file_path.suffix.lower()
                    file_types[ext] = file_types.get(ext, 0) + 1
                    
                    # Track large files (>1MB)
                    if size > 1024 * 1024:
                        large_files.append((file_path, size))
                        
                except Exception:
                    continue
        
        print(f"📊 Current State:")
        print(f"   Total files: {total_files:,}")
        print(f"   Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        
        if large_files:
            print(f"📦 Large files (>1MB):")
            for file_path, size in sorted(large_files, key=lambda x: x[1], reverse=True)[:10]:
                rel_path = file_path.relative_to(self.project_root)
                print(f"   {rel_path} ({size/1024/1024:.2f} MB)")
        
        return total_files, total_size, file_types, large_files
    
    def clean_cache_directories(self):
        """Remove cache and temporary directories (but preserve venv)."""
        print("\n🧹 Cleaning cache directories (preserving virtual environment)...")

        for dir_name in self.dirs_to_clean:
            for dir_path in self.project_root.rglob(dir_name):
                if dir_path.is_dir():
                    # Skip if it's inside virtual environment
                    if 'venv' in dir_path.parts or 'env' in dir_path.parts:
                        print(f"   🛡️ Skipped (venv): {dir_path.relative_to(self.project_root)}")
                        continue

                    try:
                        # Calculate size before deletion
                        size = sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file())

                        shutil.rmtree(dir_path)
                        print(f"   ✅ Removed: {dir_path.relative_to(self.project_root)} ({size/1024:.1f} KB)")

                        self.files_removed += 1
                        self.space_saved += size
                        self.cleanup_log.append(f"Removed directory: {dir_path}")

                    except Exception as e:
                        print(f"   ❌ Error removing {dir_path}: {e}")
    
    def is_essential_file(self, file_path: Path) -> bool:
        """Check if a file is essential and should never be removed."""
        file_str = str(file_path)
        file_name = file_path.name

        # Check if file is in virtual environment (always preserve venv files)
        if 'venv' in file_path.parts or 'env' in file_path.parts:
            return True

        # Check essential patterns
        essential_checks = [
            file_name.endswith('.pyd'),  # Python extensions
            file_name.endswith('.dll'),  # Dynamic libraries
            file_name.endswith('.so'),   # Shared objects
            file_name.endswith('.pem'),  # Certificates
            file_name == 'python.exe',
            file_name == 'pip.exe',
            file_name == '__init__.py',
            file_name == 'site.py',
            '.dist-info' in file_str,
            '.egg-info' in file_str,
        ]

        return any(essential_checks)

    def clean_temporary_files(self):
        """Remove temporary and backup files (but preserve essential files)."""
        print("\n🗑️ Cleaning temporary files (preserving essential files)...")

        # Safe patterns to remove (excluding essential extensions)
        safe_patterns_to_remove = [
            '*.tmp', '*.temp', '*.bak',
            '*.backup.*',
            '*.log',
            '.coverage*',
            'debug_*.md',
            'test_*.md'
        ]

        # Python cache files (safe to remove, will be regenerated)
        python_cache_patterns = [
            '*.pyc', '*.pyo'  # Removed *.pyd from here!
        ]

        # Process safe patterns
        for pattern in safe_patterns_to_remove:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file() and not self.is_essential_file(file_path):
                    try:
                        size = file_path.stat().st_size
                        file_path.unlink()

                        print(f"   ✅ Removed: {file_path.relative_to(self.project_root)}")
                        self.files_removed += 1
                        self.space_saved += size
                        self.cleanup_log.append(f"Removed file: {file_path}")

                    except Exception as e:
                        print(f"   ❌ Error removing {file_path}: {e}")

        # Process Python cache files (only outside venv)
        for pattern in python_cache_patterns:
            for file_path in self.project_root.rglob(pattern):
                if (file_path.is_file() and
                    not self.is_essential_file(file_path) and
                    'venv' not in file_path.parts):  # Don't touch venv cache
                    try:
                        size = file_path.stat().st_size
                        file_path.unlink()

                        print(f"   ✅ Removed cache: {file_path.relative_to(self.project_root)}")
                        self.files_removed += 1
                        self.space_saved += size
                        self.cleanup_log.append(f"Removed cache file: {file_path}")

                    except Exception as e:
                        print(f"   ❌ Error removing {file_path}: {e}")

        print(f"   🛡️ Protected all .pyd, .dll, and other essential files")
    
    def clean_analysis_reports(self):
        """Keep only the most recent analysis report."""
        print("\n📊 Cleaning analysis reports...")
        
        # Find all analysis reports
        json_reports = list(self.project_root.glob('safe_project_analysis_*.json'))
        md_reports = list(self.project_root.glob('safe_project_analysis_*.md'))
        
        # Keep only the most recent
        if json_reports:
            json_reports.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            for report in json_reports[1:]:  # Remove all except the newest
                try:
                    size = report.stat().st_size
                    report.unlink()
                    print(f"   ✅ Removed old report: {report.name}")
                    self.files_removed += 1
                    self.space_saved += size
                except Exception as e:
                    print(f"   ❌ Error removing {report}: {e}")
        
        if md_reports:
            md_reports.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            for report in md_reports[1:]:  # Remove all except the newest
                try:
                    size = report.stat().st_size
                    report.unlink()
                    print(f"   ✅ Removed old report: {report.name}")
                    self.files_removed += 1
                    self.space_saved += size
                except Exception as e:
                    print(f"   ❌ Error removing {report}: {e}")
        
        # Remove the old project_analysis_report.json if it exists
        old_report = self.project_root / 'project_analysis_report.json'
        if old_report.exists():
            try:
                size = old_report.stat().st_size
                old_report.unlink()
                print(f"   ✅ Removed old analysis report: {old_report.name}")
                self.files_removed += 1
                self.space_saved += size
            except Exception as e:
                print(f"   ❌ Error removing {old_report}: {e}")
    
    def update_gitignore(self):
        """Update .gitignore file with comprehensive patterns."""
        print("\n📝 Updating .gitignore...")
        
        gitignore_path = self.project_root / '.gitignore'
        
        # Read existing .gitignore
        existing_patterns = set()
        if gitignore_path.exists():
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                existing_patterns = {line.strip() for line in f if line.strip() and not line.startswith('#')}
        
        # Add new patterns
        new_patterns = self.git_ignore_patterns - existing_patterns
        
        if new_patterns:
            with open(gitignore_path, 'a', encoding='utf-8') as f:
                f.write('\n# Added by pre-commit cleanup\n')
                for pattern in sorted(new_patterns):
                    f.write(f'{pattern}\n')
            
            print(f"   ✅ Added {len(new_patterns)} new patterns to .gitignore")
        else:
            print("   ✅ .gitignore is up to date")
    
    def create_cleanup_summary(self):
        """Create a summary of cleanup operations."""
        print("\n📋 Creating cleanup summary...")
        
        summary = {
            'cleanup_date': datetime.datetime.now().isoformat(),
            'files_removed': self.files_removed,
            'space_saved_bytes': self.space_saved,
            'space_saved_mb': round(self.space_saved / (1024 * 1024), 2),
            'operations': self.cleanup_log
        }
        
        summary_file = self.project_root / 'pre_commit_cleanup_summary.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2)
        
        print(f"   ✅ Summary saved to: {summary_file.name}")
        
        return summary
    
    def run_pre_commit_cleanup(self):
        """Run the complete pre-commit cleanup process."""
        print("🚀 N8N Builder Pre-Commit Cleanup")
        print("=" * 60)
        print("Preparing project for GitHub commit...")
        print()
        
        # Analyze current state
        initial_files, initial_size, file_types, large_files = self.analyze_current_state()
        
        # Perform cleanup operations
        self.clean_cache_directories()
        self.clean_temporary_files()
        self.clean_analysis_reports()
        self.update_gitignore()
        
        # Create summary
        summary = self.create_cleanup_summary()
        
        # Final analysis
        print("\n📊 Cleanup Results:")
        print(f"   Files removed: {self.files_removed}")
        print(f"   Space saved: {self.space_saved:,} bytes ({self.space_saved/1024/1024:.2f} MB)")
        
        final_files, final_size, _, _ = self.analyze_current_state()
        print(f"\n📈 Before/After Comparison:")
        print(f"   Files: {initial_files:,} → {final_files:,} ({initial_files - final_files:,} removed)")
        print(f"   Size: {initial_size/1024/1024:.2f} MB → {final_size/1024/1024:.2f} MB ({(initial_size - final_size)/1024/1024:.2f} MB saved)")
        
        print(f"\n🎯 Next Steps:")
        print(f"1. Review the cleanup summary: pre_commit_cleanup_summary.json")
        print(f"2. Test the application to ensure nothing important was removed")
        print(f"3. Commit the cleaned project to GitHub:")
        print(f"   git add .")
        print(f"   git commit -m 'Clean up project files before commit'")
        print(f"   git push")
        
        print(f"\n✅ Pre-commit cleanup complete!")


def main():
    """Main function."""
    cleanup = PreCommitCleanup()
    cleanup.run_pre_commit_cleanup()


if __name__ == "__main__":
    main()
