# N8N Builder - Sync Community Edition Only
# This script only syncs to the community edition (useful when main repo is already committed)

param(
    [string]$CommunityMessage = ""
)

# Set up colors for better output
$ErrorActionPreference = "Stop"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "🎯 $Title" "Cyan"
    Write-ColorOutput ("=" * ($Title.Length + 3)) "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Blue"
}

try {
    Write-Header "N8N Builder - Community Edition Sync"
    
    # Check if we're in the right directory
    if (-not (Test-Path "n8n_builder") -or -not (Test-Path "Scripts")) {
        Write-Error "Please run this script from the N8N_Builder root directory"
        exit 1
    }

    # Get community sync message
    if (-not $CommunityMessage) {
        Write-Info "Enter message for Community Edition sync:"
        $CommunityMessage = Read-Host "Community message"
    }

    if (-not $CommunityMessage -or $CommunityMessage.Trim() -eq "") {
        Write-Error "Community message is required"
        exit 1
    }

    # Run the community sync
    Write-Info "Syncing to Community Edition..."
    
    # Check if sync script exists
    if (-not (Test-Path "sync-public.ps1")) {
        Write-Error "sync-public.ps1 not found. Please ensure the separation system is set up."
        exit 1
    }

    # Run the sync
    .\sync-public.ps1 -PublicRepoPath "..\N8N_Builder_Community" -Force
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Community sync failed"
        exit 1
    }

    Write-Success "Community files synced successfully"

    # Commit and push to GitHub
    Write-Header "Pushing to GitHub"
    
    $originalLocation = Get-Location
    try {
        Set-Location "..\N8N_Builder_Community"
        
        # Check if there are changes to commit
        $communityStatus = git status --porcelain
        if ($communityStatus) {
            Write-Info "Committing community changes..."
            git add .
            git commit -m "$CommunityMessage"
            
            Write-Info "Pushing to GitHub..."
            git push origin master
            
            Write-Success "Successfully pushed to GitHub!"
            Write-ColorOutput "📝 Community commit: $CommunityMessage" "Cyan"
            Write-Info "GitHub: https://github.com/vbwyrde/N8N_Builder"
        } else {
            Write-Warning "No changes to commit in community repository"
        }
    }
    finally {
        Set-Location $originalLocation
    }

    Write-Header "Sync Complete"
    Write-Success "Community Edition sync completed successfully!"

} catch {
    Write-Error "Script failed: $($_.Exception.Message)"
    Write-Info "You may need to run the sync manually:"
    Write-Info "  .\sync-public.ps1 -PublicRepoPath '..\N8N_Builder_Community' -Force"
    exit 1
}
