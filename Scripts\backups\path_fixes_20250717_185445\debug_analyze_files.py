"""
Debug version of analyze_project_files.py to investigate the winsockets.exe issue
"""

import os
import sys
from pathlib import Path
import datetime

def debug_file_creation():
    """Debug file creation to understand the winsockets.exe issue."""
    
    print("🔍 Debug Analysis - File Creation Investigation")
    print("=" * 60)
    
    # Get current working directory
    cwd = Path.cwd()
    print(f"📁 Current working directory: {cwd}")
    
    # Check if we're in the right directory
    expected_files = ['README.md', 'requirements.txt', 'setup.py']
    for file in expected_files:
        if (cwd / file).exists():
            print(f"✅ Found expected file: {file}")
        else:
            print(f"❌ Missing expected file: {file}")
    
    # Test creating the markdown file
    test_filename = "test_markdown_creation.md"
    print(f"\n📝 Testing markdown file creation: {test_filename}")
    
    try:
        test_content = [
            "# Test Markdown File",
            f"Created at: {datetime.datetime.now().isoformat()}",
            f"Working directory: {cwd}",
            "",
            "This is a test to see if markdown files are created correctly."
        ]
        
        # Write the test file
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(test_content))
        
        print(f"✅ Successfully created: {test_filename}")
        
        # Check if the file exists and has the right content
        if Path(test_filename).exists():
            print(f"✅ File exists on disk")
            
            # Read it back
            with open(test_filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "Test Markdown File" in content:
                print(f"✅ File content is correct")
            else:
                print(f"❌ File content is incorrect")
                print(f"Content: {content[:100]}...")
        else:
            print(f"❌ File does not exist on disk")
        
        # List files in current directory to see what was actually created
        print(f"\n📂 Files in current directory:")
        for file_path in sorted(cwd.iterdir()):
            if file_path.is_file():
                print(f"   📄 {file_path.name} ({file_path.stat().st_size} bytes)")
        
        # Clean up test file
        if Path(test_filename).exists():
            Path(test_filename).unlink()
            print(f"🧹 Cleaned up test file")
            
    except Exception as e:
        print(f"❌ Error creating test file: {e}")
        import traceback
        traceback.print_exc()
    
    # Check for any suspicious processes or files
    print(f"\n🔍 Checking for suspicious files...")
    
    suspicious_patterns = ['winsockets', 'winsock', '.exe']
    for pattern in suspicious_patterns:
        print(f"\n🔍 Searching for files containing '{pattern}':")
        found_files = list(cwd.rglob(f"*{pattern}*"))
        if found_files:
            for file_path in found_files:
                print(f"   ⚠️ Found: {file_path}")
        else:
            print(f"   ✅ No files found with pattern '{pattern}'")
    
    # Check environment variables that might affect file creation
    print(f"\n🔧 Environment check:")
    env_vars = ['TEMP', 'TMP', 'PYTHONPATH', 'PATH']
    for var in env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"   {var}: {value[:100]}{'...' if len(value) > 100 else ''}")
    
    # Check Python executable
    print(f"\n🐍 Python information:")
    print(f"   Executable: {sys.executable}")
    print(f"   Version: {sys.version}")
    print(f"   Platform: {sys.platform}")
    
    # Test the actual save_markdown_report function
    print(f"\n📝 Testing actual markdown report creation...")
    
    try:
        # Create a minimal version of the save function
        def test_save_markdown_report(filename: str = "debug_project_analysis_report.md"):
            """Test version of save_markdown_report."""
            md_content = [
                "# Debug N8N Builder Project File Analysis Report",
                f"**Generated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"**Working Directory:** `{Path.cwd()}`",
                "",
                "## Test Content",
                "This is a test to debug the winsockets.exe issue.",
                "",
                "If you see this file created correctly, the issue is not with the markdown creation logic."
            ]
            
            print(f"   📝 Attempting to create: {filename}")
            print(f"   📁 In directory: {Path.cwd()}")
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(md_content))
            
            print(f"   ✅ File creation completed")
            
            # Verify the file
            if Path(filename).exists():
                size = Path(filename).stat().st_size
                print(f"   ✅ File exists: {filename} ({size} bytes)")
                return True
            else:
                print(f"   ❌ File does not exist: {filename}")
                return False
        
        # Test the function
        success = test_save_markdown_report()
        
        if success:
            print(f"✅ Markdown creation test PASSED")
        else:
            print(f"❌ Markdown creation test FAILED")
            
    except Exception as e:
        print(f"❌ Error in markdown creation test: {e}")
        import traceback
        traceback.print_exc()


def check_antivirus_interference():
    """Check for potential antivirus interference."""
    print(f"\n🛡️ Checking for potential antivirus interference...")
    
    # Check Windows Defender status (if on Windows)
    if sys.platform == 'win32':
        try:
            import subprocess
            result = subprocess.run(['powershell', '-Command', 'Get-MpPreference | Select-Object -Property DisableRealtimeMonitoring'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   Windows Defender status: {result.stdout.strip()}")
            else:
                print(f"   Could not check Windows Defender status")
        except Exception as e:
            print(f"   Error checking Windows Defender: {e}")
    
    # Check for common antivirus processes
    try:
        import psutil
        av_processes = ['avp.exe', 'avgnt.exe', 'avguard.exe', 'bdagent.exe', 'MsMpEng.exe']
        running_av = []
        
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] and proc.info['name'].lower() in [av.lower() for av in av_processes]:
                    running_av.append(proc.info['name'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if running_av:
            print(f"   🛡️ Detected antivirus processes: {', '.join(running_av)}")
            print(f"   💡 Antivirus software might be interfering with file creation")
        else:
            print(f"   ✅ No common antivirus processes detected")
            
    except ImportError:
        print(f"   ⚠️ psutil not available, cannot check for antivirus processes")
    except Exception as e:
        print(f"   ❌ Error checking antivirus processes: {e}")


def main():
    """Main debug function."""
    print("🔍 N8N Builder File Creation Debug Tool")
    print("=" * 60)
    print("This tool investigates the winsockets.exe issue when running analyze_project_files.py")
    print("")
    
    debug_file_creation()
    check_antivirus_interference()
    
    print(f"\n📋 Summary and Recommendations:")
    print(f"1. Check if antivirus software is quarantining or renaming files")
    print(f"2. Try running the script as administrator")
    print(f"3. Add the project directory to antivirus exclusions")
    print(f"4. Check Windows Event Viewer for security warnings")
    print(f"5. Scan the system for malware that might be hijacking file operations")
    
    print(f"\n✅ Debug analysis complete!")


if __name__ == "__main__":
    main()
