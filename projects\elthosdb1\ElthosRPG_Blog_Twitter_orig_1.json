{"name": "ElthosRPG_Blog_Twitter", "nodes": [{"parameters": {"url": "={{ $json.selectedPostURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [-640, 260], "id": "519ac501-579d-47ea-9823-59bb58fcddd7"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-800, 360], "id": "8a146a7e-9802-4381-a0c2-e7feff15e3d1", "name": "When clicking 'Execute workflow'"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "mainBlogURL", "value": "https://elthosrpg.blogspot.com/", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1200, 320], "id": "new-set-main-blog-url", "name": "Set Main Blog URL"}, {"parameters": {"url": "={{ $json.mainBlogURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Blog Page", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [-1040, 280], "id": "new-fetch-blog-page"}, {"parameters": {"operation": "extract-content", "html": "={{ $json.data }}", "outputMarkdown": false}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [-880, 240], "id": "new-scrape-blog-page", "name": "Scrape Blog Page"}, {"parameters": {"jsCode": "// Extract blog post URLs from the scraped content\nconst content = $input.first().json.content;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log('Scraping blog page for post URLs...');\n\n// Regex patterns to match blog post URLs\nconst patterns = [\n  // Match full URLs like https://elthosrpg.blogspot.com/2022/10/woaf-game-session-39.html\n  /https:\\/\\/elthosrpg\\.blogspot\\.com\\/\\d{4}\\/\\d{2}\\/[^\\s\"'<>]+\\.html/g,\n  // Match relative URLs like /2022/10/woaf-game-session-39.html\n  /\\/\\d{4}\\/\\d{2}\\/[^\\s\"'<>]+\\.html/g\n];\n\nlet postUrls = [];\n\n// Extract URLs using all patterns\npatterns.forEach(pattern => {\n  const matches = content.match(pattern) || [];\n  matches.forEach(url => {\n    // Convert relative URLs to absolute\n    if (url.startsWith('/')) {\n      url = 'https://elthosrpg.blogspot.com' + url;\n    }\n    \n    // Add to array if not already present\n    if (!postUrls.includes(url)) {\n      postUrls.push(url);\n    }\n  });\n});\n\n// Remove duplicates and filter out non-post URLs\npostUrls = [...new Set(postUrls)];\npostUrls = postUrls.filter(url => {\n  return url.includes('.html') && \n         url.includes('/20') && // Year pattern\n         !url.includes('feeds') && \n         !url.includes('search') &&\n         !url.includes('labels');\n});\n\nconsole.log(`Found ${postUrls.length} blog post URLs:`);\npostUrls.forEach(url => console.log(`- ${url}`));\n\n// If no URLs found, use fallback\nif (postUrls.length === 0) {\n  console.log('No blog post URLs found, using fallback URL');\n  postUrls = ['https://elthosrpg.blogspot.com/2022/10/woaf-game-session-39.html'];\n}\n\n// Return the list of URLs\nreturn [{ json: { \n  postUrls: postUrls,\n  totalPosts: postUrls.length,\n  mainBlogURL: mainBlogURL\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-720, 200], "id": "new-extract-urls", "name": "Extract Post URLs"}, {"parameters": {"jsCode": "// Randomly select a blog post URL\nconst postUrls = $input.first().json.postUrls;\nconst totalPosts = $input.first().json.totalPosts;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log(`Selecting random post from ${totalPosts} available posts...`);\n\n// Generate random index\nconst randomIndex = Math.floor(Math.random() * postUrls.length);\nconst selectedPostURL = postUrls[randomIndex];\n\nconsole.log(`Selected post: ${selectedPostURL}`);\n\n// Return the selected URL along with metadata\nreturn [{ json: { \n  selectedPostURL: selectedPostURL,\n  randomIndex: randomIndex,\n  totalPosts: totalPosts,\n  mainBlogURL: mainBlogURL\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-560, 160], "id": "new-random-selection", "name": "Random Selection"}, {"parameters": {"promptType": "define", "text": "={{ $json.firstParagraph }}", "messages": {"messageValues": [{"message": "You are a twitter expert who can take the best content from the blog entry and make the perfect tweet from it that is no more than 60 characters long. Use Text format. Include hash tags #IndieRPG #Elthos #TTRPG. Be concise and engaging."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [100, 320], "id": "5e2f3a27-3112-4b28-8ab8-3585d8c7f10d", "name": "Basic LLM Chain", "alwaysOutputData": false}, {"parameters": {"model": {"__rl": true, "value": "deepseek-r1-distill-llama-8b", "mode": "list", "cachedResultName": "deepseek-r1-distill-llama-8b"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [180, 540], "id": "e0c8aa8a-7960-460f-9e4d-87c5b7bf6422", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "cqdpm9ID0q2zjSkV", "name": "LM Studio"}}}, {"parameters": {"operation": "extract-content", "html": "={{ $('Fetch Post').item.json.data }}", "outputMarkdown": true}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [-280, 280], "id": "32026d21-344c-4e5a-842c-594f8e2e4b4e", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [600, 320], "id": "250ca31b-5151-49be-9b86-2b65e3b6ac5f", "name": "Create Tweet", "credentials": {"twitterOAuth2Api": {"id": "QMuVcnYLarrzm0jp", "name": "X account"}}}, {"parameters": {"jsCode": "// Replace \"content\" with the actual field name from ScrapeNinja output\nconst content = $input.first().json.content;\n\n// Split into paragraphs by double line breaks\nconst paragraphs = content.split(/\\n\\s*\\n/);\n\n// Get the first paragraph\nlet firstParagraph = paragraphs[2] || \"\";\n\n// Limit to 3000 words\nconst words = firstParagraph.split(/\\s+/).slice(0, 3000);\nconst limitedParagraph = words.join(\" \");\n\n// PRESERVE the blogURL field from input\nconst blogURL = $input.first().json.blogURL;\n\nreturn [{ json: { \n    firstParagraph: limitedParagraph,\n    blogURL: blogURL \n}}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-100, 360], "id": "6e907c03-8a43-4eaa-a3c3-6f48e2d8cffc", "name": "Code - Get Paragraph 1"}, {"parameters": {"jsCode": "// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// Get the blog URL directly from the Set node\nconst blogUrl = $node[\"Edit Field - blogURL\"].json.blogURL;\n\nconsole.log('LLM text:', text);\nconsole.log('Blog URL from Set node:', blogUrl);\n\n// Remove <think>...</think> and everything in between\nconst cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Clean whitespace\ntext = cleaned.replace(/^\\s+|\\s+$/g, '');\n\n// Twitter character limit\nconst TARGET_LIMIT = 280;\n\n// Try the full text + URL first\nconst testTweet = text + ' ' + blogUrl;\nconsole.log('Test tweet length:', testTweet.length);\n\n// If it fits within limit, use it as-is\nif (testTweet.length <= TARGET_LIMIT) {\n    console.log('Tweet fits within limit, using full text');\n    return [{ json: { text: testTweet } }];\n}\n\n// If too long, try to preserve hashtags by truncating main content\nconst hashtagRegex = /#\\w+/g;\nconst hashtags = text.match(hashtagRegex) || [];\nconst hashtagsText = hashtags.join(' ');\n\n// Remove hashtags and em-dashes from main text to see content length\nconst contentWithoutHashtags = text.replace(hashtagRegex, '').replace(/—/g, '').replace(/\\s+/g, ' ').trim();\n\n// More precise calculation: content + \" \" + hashtags + \" \" + URL = TARGET_LIMIT\n// So: content = TARGET_LIMIT - hashtags.length - URL.length - 2 spaces - 3 for \"...\"\nconst spacesNeeded = 2; // one before hashtags, one before URL\nconst ellipsisLength = 3;\nconst availableForContent = TARGET_LIMIT - hashtagsText.length - blogUrl.length - spacesNeeded - ellipsisLength;\n\nconsole.log('Available space for content:', availableForContent);\nconsole.log('Content without hashtags:', contentWithoutHashtags);\nconsole.log('Hashtags found:', hashtags);\nconsole.log('Hashtags text length:', hashtagsText.length);\n\n// If we have enough space, use truncated content + hashtags\nif (availableForContent > 15 && hashtags.length > 0) {\n    const truncatedContent = contentWithoutHashtags.substring(0, availableForContent) + '...';\n    const finalTweet = truncatedContent + ' ' + hashtagsText + ' ' + blogUrl;\n    console.log('Using truncated content with hashtags');\n    console.log('Final tweet length:', finalTweet.length);\n    return [{ json: { text: finalTweet } }];\n}\n\n// Fallback: just truncate everything\nconst maxTextLength = TARGET_LIMIT - blogUrl.length - 1 - 3;\nconst truncatedText = text.substring(0, maxTextLength) + '...';\nconst fallbackTweet = truncatedText + ' ' + blogUrl;\n\nconsole.log('Using fallback truncation');\nreturn [{ json: { text: fallbackTweet } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [420, 380], "id": "1dde5f62-e466-4451-a2ba-717f8882a67a", "name": "Code - Clean for Tweet"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "blogURL", "value": "={{ $json.selectedPostURL }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-460, 360], "id": "4eb7da45-b297-42e7-9784-6b6581eab4ad", "name": "Edit Field - blogURL"}], "pinData": {}, "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "Set Main Blog URL", "type": "main", "index": 0}]]}, "Set Main Blog URL": {"main": [[{"node": "Fetch Blog Page", "type": "main", "index": 0}]]}, "Fetch Blog Page": {"main": [[{"node": "Scrape Blog Page", "type": "main", "index": 0}]]}, "Scrape Blog Page": {"main": [[{"node": "Extract Post URLs", "type": "main", "index": 0}]]}, "Extract Post URLs": {"main": [[{"node": "Random Selection", "type": "main", "index": 0}]]}, "Random Selection": {"main": [[{"node": "Fetch Post", "type": "main", "index": 0}]]}, "Fetch Post": {"main": [[{"node": "Edit Field - blogURL", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Code - Clean for Tweet", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "ScrapeNinja": {"main": [[{"node": "Code - Get Paragraph 1", "type": "main", "index": 0}]]}, "Code - Get Paragraph 1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Code - Clean for Tweet": {"main": [[{"node": "Create Tweet", "type": "main", "index": 0}]]}, "Edit Field - blogURL": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "71a98f76-8693-459d-a1e6-cc132ddc4235", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "M4PfByDgjUvSFPqZ", "tags": []}