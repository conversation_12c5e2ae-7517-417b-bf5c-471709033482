# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
﻿# N8N Builder - Simple Commit and Sync Community Edition

param(
    [string]$CommitMessage = "",
    [string]$CommunityMessage = ""
)

Write-Host ""
Write-Host "N8N Builder - Commit and Sync Workflow" -ForegroundColor Cyan

# Check git status
$gitStatus = git status --porcelain
if (-not $gitStatus) {
    Write-Host "No changes to commit" -ForegroundColor Yellow
    exit 0
}

# Step 1: Commit to main repository
if (-not $CommitMessage) {
    $CommitMessage = Read-Host "Enter commit message for main repository"
}

if ($CommitMessage) {
    git add .
    git commit -m "$CommitMessage"
    Write-Host "Committed to main repository" -ForegroundColor Green
}

# Step 2: Community sync
if (-not $CommunityMessage) {
    $CommunityMessage = Read-Host "Enter community message (or press Enter to skip)"
}

if ($CommunityMessage) {
    .\sync-public.ps1 -PublicRepoPath "..\N8N_Builder_Community" -Force
    
    $originalLocation = Get-Location
    Set-Location "..\N8N_Builder_Community"
    git add .
    git commit -m "$CommunityMessage"
    git push origin master
    Set-Location $originalLocation
    
    Write-Host "Synced to GitHub successfully" -ForegroundColor Green
}
