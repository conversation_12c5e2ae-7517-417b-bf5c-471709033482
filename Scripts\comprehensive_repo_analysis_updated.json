{"scan_metadata": {"scan_time": "2025-07-05T15:12:53.277079", "scan_type": "comprehensive_repository_analysis", "local_repo_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder", "github_user": "vbwyrde", "end_time": "2025-07-05T15:12:54.457963", "duration_seconds": 1.180884}, "local_repository": {"repository_root": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder", "git_info": {"remote_url": {"success": true, "output": "https://github.com/vbwyrde/N8N_Builder.git", "error": ""}, "current_branch": {"success": true, "output": "master", "error": ""}, "status": {"success": true, "output": "M .augment-guidelines\n D 2.5.2\n D ARCHITECTURE.md\n M Documentation/technical/PYTHON_ENVIRONMENT_SETUP.md\n D Emergency-Shutdown.ps1\n M GETTING_STARTED.md\n D GITHUB_SETUP_INSTRUCTIONS.md\n M README.md\n D SYSTEMATIC_REMEDIATION_PLAN.md\n D check_db_state.py\n D check_schema.py\n D comprehensive-audit.ps1\n D create_analytics_procedure.py\n D create_simple_session_procedure.py\n D debug_error_criteria.py\n D deploy_public.ps1\n D emergency_shutdown.bat\n D example_enhanced_workflow.py\n D feedback_log.json\n D logging_config.py\n M n8n_builder/n8n_builder.py\n D ngrok-config.yml.template\n D private-component-audit.json\n D restore_n8n_setup.ps1\n D run_with_venv.bat\n D run_with_venv.ps1\n D shutdown.bat\n D shutdown.py\n D sync-public.ps1\n D verify-public-clean.ps1\n?? Documentation/ARCHITECTURE.md\n?? Documentation/FOLDER_ORGANIZATION.md\n?? Documentation/GITHUB_ORGANIZATION_HANDOFF.md\n?? Documentation/GITHUB_ORGANIZATION_SUMMARY.md\n?? Documentation/GITHUB_ORGANIZATION_TASKS.md\n?? Documentation/GITHUB_SETUP_INSTRUCTIONS.md\n?? Documentation/MANUAL_REVIEW_CHECKLIST.md\n?? Documentation/PHASE1_COMPLETION_SUMMARY.md\n?? Documentation/PHASE2_COMPLETION_SUMMARY.md\n?? Documentation/PHASE3_COMPLETION_SUMMARY.md\n?? Documentation/SYSTEMATIC_REMEDIATION_PLAN.md\n?? README_community.md\n?? Scripts/Emergency-Shutdown.ps1\n?? Scripts/cleanup-root-folder.ps1\n?? Scripts/comprehensive-audit.ps1\n?? Scripts/comprehensive_repo_scan.py\n?? Scripts/consolidate-self-healer.ps1\n?? Scripts/deploy_public.ps1\n?? Scripts/detect-private-components.ps1\n?? Scripts/dev_config.json\n?? Scripts/dev_publish.py\n?? Scripts/emergency_shutdown.bat\n?? Scripts/execute_separation.py\n?? Scripts/github_repository_setup.py\n?? Scripts/pre_execution_verification.py\n?? Scripts/prepare_public_release.py\n?? Scripts/public_repo_config.json\n?? Scripts/restore_n8n_setup.ps1\n?? Scripts/run_with_venv.bat\n?? Scripts/run_with_venv.ps1\n?? Scripts/sanitize_documentation.py\n?? Scripts/shutdown.bat\n?? Scripts/shutdown.py\n?? Scripts/sync-public.ps1\n?? Scripts/test-detection.ps1\n?? Scripts/test_detection_simple.py\n?? Scripts/test_enhanced_sync.py\n?? Scripts/test_verification_systems.py\n?? Scripts/verification_pipeline.py\n?? Scripts/verify-public-clean.ps1\n?? archive/\n?? comprehensive_repo_analysis.json\n?? config/\n?? data/\n?? n8n_builder/example_enhanced_workflow.py", "error": ""}, "last_commit": {"success": true, "output": "fb29463 Moved SH files to sub folder", "error": ""}}, "file_structure": {"root_files": [{"name": ".augment-guidelines", "size": 4120, "modified": "2025-07-04T17:52:12.512535"}, {"name": ".augment-guidelines-public", "size": 2634, "modified": "2025-07-01T19:56:42.391649"}, {"name": ".giti<PERSON>re", "size": 4162, "modified": "2025-07-03T17:18:35.191243"}, {"name": ".gitignore_public", "size": 2461, "modified": "2025-07-01T19:56:08.303336"}, {"name": "cleanup-root-folder.log", "size": 5567, "modified": "2025-07-04T15:36:58.557751"}, {"name": "comprehensive_repo_analysis.json", "size": 10271, "modified": "2025-07-05T14:46:32.533159"}, {"name": "config_public.yaml", "size": 2278, "modified": "2025-07-01T19:58:14.487708"}, {"name": "FEATURES.md", "size": 1877, "modified": "2025-07-03T00:10:58.835635"}, {"name": "GETTING_STARTED.md", "size": 7814, "modified": "2025-07-04T16:24:42.397548"}, {"name": "LIGHTNING_START.md", "size": 1018, "modified": "2025-06-24T20:29:28.694778"}, {"name": "README.md", "size": 8441, "modified": "2025-07-04T17:53:17.888529"}, {"name": "README_community.md", "size": 7216, "modified": "2025-07-04T23:48:58.844254"}, {"name": "README_public.md", "size": 6727, "modified": "2025-07-01T19:55:51.386619"}, {"name": "requirements.txt", "size": 733, "modified": "2025-06-27T22:54:51.255578"}, {"name": "requirements_public.txt", "size": 645, "modified": "2025-07-01T19:55:11.892572"}, {"name": "run.py", "size": 36325, "modified": "2025-06-30T13:03:56.790359"}, {"name": "run_public.py", "size": 7834, "modified": "2025-07-01T19:54:56.852611"}, {"name": "setup.py", "size": 1173, "modified": "2025-06-21T17:01:55.310286"}, {"name": "setup_public.py", "size": 1916, "modified": "2025-07-01T19:56:21.788566"}, {"name": "sync-public.log", "size": 8916, "modified": "2025-07-04T13:57:23.077735"}], "directories": [{"name": "agents", "file_count": 1}, {"name": "archive", "file_count": 3}, {"name": "cache", "file_count": 9}, {"name": "config", "file_count": 2}, {"name": "data", "file_count": 10}, {"name": "Documentation", "file_count": 30}, {"name": "logs", "file_count": 2}, {"name": "n8n-docker", "file_count": 73}, {"name": "n8n_builder", "file_count": 368}, {"name": "n8n_builder.egg-info", "file_count": 5}, {"name": "projects", "file_count": 11}, {"name": "<PERSON><PERSON><PERSON>", "file_count": 59}, {"name": "Self_Healer", "file_count": 82}, {"name": "static", "file_count": 1}, {"name": "tests", "file_count": 41}, {"name": "venv", "file_count": 6003}, {"name": "__pycache__", "file_count": 2}]}, "private_components": {"detected": [{"name": "Self_Healer", "type": "directory", "exists": true}]}, "public_components": {"public_suffix_files": [{"name": "README_public.md", "size": 6727, "exists": true}, {"name": "requirements_public.txt", "size": 645, "exists": true}, {"name": "run_public.py", "size": 7834, "exists": true}, {"name": "setup_public.py", "size": 1916, "exists": true}, {"name": "config_public.yaml", "size": 2278, "exists": true}, {"name": ".gitignore_public", "size": 2461, "exists": true}]}, "configuration_files": {"separation_system": [{"name": "Scripts/sync-public.ps1", "size": 18919, "exists": true}, {"name": "Scripts/public_repo_config.json", "size": 6234, "exists": true}, {"name": "Scripts/detect_private_components.py", "size": 13890, "exists": true}, {"name": "Scripts/verification_pipeline.py", "size": 16650, "exists": true}]}}, "github_repositories": {"scan_method": "github_cli_unavailable", "repositories": [], "n8n_builder_details": {}, "knowledgebase_details": {"access_error": "'gh' is not recognized as an internal or external command,\noperable program or batch file.\n"}, "scan_errors": ["GitHub CLI failed: 'gh' is not recognized as an internal or external command,\noperable program or batch file.\n"]}, "analysis": {"repository_status": {"n8n_builder_exists_github": false, "knowledgebase_exists_github": true, "local_git_configured": true, "current_branch": "master"}, "private_component_status": {"private_components_detected_locally": 1, "public_suffix_files_ready": 6, "separation_system_files": 4}, "separation_readiness": {"required_files_present": 4, "total_required_files": 4, "readiness_percentage": 100.0}, "github_organization_status": {"knowledgebase": {"visibility": "unknown", "access_status": "restricted"}}, "critical_issues": [], "recommendations": ["Separation system appears ready - can proceed with separation execution"]}, "recommendations": []}