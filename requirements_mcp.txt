# MCP (Model Context Protocol) Dependencies for N8N_Builder
# Install with: pip install -r requirements_mcp.txt

# Core MCP package
mcp>=1.0.0

# Additional dependencies for MCP servers
asyncio-mqtt>=0.16.0
websockets>=12.0
aiohttp>=3.9.0

# JSON schema validation
jsonschema>=4.20.0

# Enhanced logging for MCP servers
structlog>=23.2.0

# Optional: For enhanced MCP server features
pydantic>=2.5.0
typing-extensions>=4.8.0
