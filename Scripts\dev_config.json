{"developer_workflow": {"description": "Configuration for streamlined developer publishing workflow", "version": "1.0.0"}, "repository_settings": {"public_repo_path": "../N8N_Builder_Community", "git_remote": "origin", "git_branch": "main"}, "automation_settings": {"auto_push": true, "verification_enabled": true, "backup_enabled": true, "cleanup_temp_files": true}, "workflow_options": {"default_commit_prefix": "Update N8N_Builder Community Edition", "include_timestamp": true, "auto_generate_message": true}, "safety_settings": {"require_verification": true, "stop_on_private_references": true, "backup_before_sync": true, "rollback_on_failure": true}}