"""
Analyze virtual environment files to understand what's essential vs optional
"""

import os
from pathlib import Path
from collections import defaultdict
import json


def analyze_venv_files():
    """Analyze files in the virtual environment."""
    
    venv_path = Path("venv")
    if not venv_path.exists():
        print("❌ Virtual environment not found")
        return
    
    print("🔍 Analyzing Virtual Environment Files")
    print("=" * 60)
    
    # File type analysis
    file_types = defaultdict(list)
    total_files = 0
    total_size = 0
    
    # Essential vs optional categorization
    essential_extensions = {
        '.pyd': 'Essential compiled Python extensions',
        '.dll': 'Essential dynamic link libraries',
        '.exe': 'Essential executables (python.exe, pip.exe, etc.)',
        '.py': 'Python source code',
        '.pyi': 'Type hint files',
        '.so': 'Shared libraries (Unix equivalent of .dll)'
    }
    
    cache_extensions = {
        '.pyc': 'Python bytecode cache (can be regenerated)',
        '.pyo': 'Optimized Python bytecode (can be regenerated)',
        '__pycache__': 'Python cache directories'
    }
    
    # Scan all files
    for file_path in venv_path.rglob('*'):
        if file_path.is_file():
            try:
                size = file_path.stat().st_size
                total_files += 1
                total_size += size
                
                ext = file_path.suffix.lower()
                if file_path.name == '__pycache__':
                    ext = '__pycache__'
                
                file_types[ext].append({
                    'path': str(file_path.relative_to(venv_path)),
                    'size': size,
                    'name': file_path.name
                })
                
            except Exception as e:
                print(f"   ⚠️ Error processing {file_path}: {e}")
    
    print(f"📊 Virtual Environment Summary:")
    print(f"   Total files: {total_files:,}")
    print(f"   Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
    
    # Analyze by file type
    print(f"\n📋 File Types Analysis:")
    
    essential_files = 0
    essential_size = 0
    cache_files = 0
    cache_size = 0
    
    for ext, files in sorted(file_types.items(), key=lambda x: len(x[1]), reverse=True):
        count = len(files)
        size = sum(f['size'] for f in files)
        
        if ext in essential_extensions:
            essential_files += count
            essential_size += size
            status = "✅ ESSENTIAL"
            description = essential_extensions[ext]
        elif ext in cache_extensions:
            cache_files += count
            cache_size += size
            status = "🗑️ CACHE"
            description = cache_extensions[ext]
        else:
            status = "❓ OTHER"
            description = "Review needed"
        
        print(f"   {ext or '(no ext)'}: {count:,} files, {size/1024/1024:.2f} MB - {status}")
        if description:
            print(f"      {description}")
    
    # Show critical .pyd files
    pyd_files = file_types.get('.pyd', [])
    if pyd_files:
        print(f"\n🔧 Critical .pyd Files (Windows Extensions):")
        for pyd_file in sorted(pyd_files, key=lambda x: x['size'], reverse=True)[:10]:
            print(f"   {pyd_file['name']} ({pyd_file['size']/1024:.1f} KB)")
            
            # Identify which package it belongs to
            path_parts = pyd_file['path'].split('/')
            if len(path_parts) > 2:
                package = path_parts[2]  # Usually Lib/site-packages/package_name
                print(f"      Package: {package}")
    
    # Show largest files
    all_files = []
    for files in file_types.values():
        all_files.extend(files)
    
    largest_files = sorted(all_files, key=lambda x: x['size'], reverse=True)[:10]
    
    print(f"\n📦 Largest Files in venv:")
    for file_info in largest_files:
        size_mb = file_info['size'] / 1024 / 1024
        print(f"   {file_info['name']} ({size_mb:.2f} MB)")
        print(f"      {file_info['path']}")
    
    # Summary recommendations
    print(f"\n💡 Recommendations:")
    print(f"   ✅ Essential files: {essential_files:,} files ({essential_size/1024/1024:.2f} MB)")
    print(f"   🗑️ Cache files: {cache_files:,} files ({cache_size/1024/1024:.2f} MB)")
    print(f"   📊 Cache can be safely removed and regenerated")
    print(f"   🔧 .pyd files are ESSENTIAL for Windows - never remove these!")
    
    # Check for specific packages that need .pyd files
    critical_packages = {
        'psutil': 'System monitoring',
        'cryptography': 'Security and encryption',
        'lxml': 'XML processing',
        'pydantic_core': 'Data validation',
        'cffi': 'C Foreign Function Interface',
        'markupsafe': 'Safe string handling'
    }
    
    print(f"\n🔍 Critical Packages Check:")
    for package, description in critical_packages.items():
        package_pyd = [f for f in pyd_files if package in f['path'].lower()]
        if package_pyd:
            print(f"   ✅ {package}: {len(package_pyd)} .pyd files - {description}")
        else:
            print(f"   ❓ {package}: No .pyd files found - may not be installed or needed")
    
    return {
        'total_files': total_files,
        'total_size': total_size,
        'essential_files': essential_files,
        'essential_size': essential_size,
        'cache_files': cache_files,
        'cache_size': cache_size,
        'file_types': {ext: len(files) for ext, files in file_types.items()}
    }


def check_requirements_necessity():
    """Check which packages in requirements.txt are actually necessary."""
    
    print(f"\n📋 Requirements.txt Analysis:")
    print("=" * 40)
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return
    
    # Read requirements
    with open(requirements_file, 'r') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    # Categorize packages
    core_packages = {
        'fastapi', 'uvicorn', 'pydantic', 'requests', 'aiohttp', 'asyncio',
        'pathlib', 'json', 'datetime', 'logging'
    }
    
    system_packages = {
        'psutil', 'schedule'
    }
    
    security_packages = {
        'cryptography', 'python-jose', 'passlib'
    }
    
    data_packages = {
        'lxml', 'beautifulsoup4', 'markupsafe'
    }
    
    testing_packages = {
        'pytest', 'pytest-asyncio'
    }
    
    print(f"📦 Package Categories:")
    
    for req in requirements:
        package_name = req.split('>=')[0].split('==')[0].split('[')[0].lower()
        
        if any(core in package_name for core in core_packages):
            category = "🔧 CORE"
        elif any(sys in package_name for sys in system_packages):
            category = "💻 SYSTEM"
        elif any(sec in package_name for sec in security_packages):
            category = "🔒 SECURITY"
        elif any(data in package_name for data in data_packages):
            category = "📊 DATA"
        elif any(test in package_name for test in testing_packages):
            category = "🧪 TESTING"
        else:
            category = "❓ OTHER"
        
        print(f"   {req:<30} - {category}")


def main():
    """Main analysis function."""
    venv_analysis = analyze_venv_files()
    check_requirements_necessity()
    
    print(f"\n🎯 Key Takeaways:")
    print(f"1. .pyd files are ESSENTIAL - never remove these in cleanup")
    print(f"2. .pyc files can be safely removed (Python will regenerate)")
    print(f"3. Virtual environment is properly configured")
    print(f"4. All critical packages have their required extensions")


if __name__ == "__main__":
    main()
