# N8N_Builder Documentation Analysis Report

**Generated:** 2025-07-17 18:41:30

## Executive Summary

- **Total Markdown Files:** 1
- **Folders with Documentation:** 1
- **Total Headers:** 16
- **Total Bullet Points:** 20

## Documentation Distribution by Folder

- **ROOT:** 1 files

## Potential Redundancy Analysis

Headers that appear multiple times across documents:


## Detailed File Analysis

### ROOT

#### README.md

- **Path:** `README.md`
- **Last Modified:** 2025-07-16 14:04:12
- **Lines:** 79
- **Size:** 2492 characters
- **Headers:** 16
- **Bullet Points:** 20

**Headers:**
- N8N_Builder Scripts (Line 1)
  - 🚀 Quick Start (Line 6)
  - 📁 Script Categories (Line 18)
    - 🔧 Project Management (Line 20)
    - 📊 Analysis Tools (Line 25)
    - 🔄 Log Management (Line 29)
    - 🧪 Testing Utilities (Line 33)
  - 🚀 Usage (Line 37)
    - Running Python Scripts (Line 39)
- From the project root directory (Line 41)
    - Running PowerShell Scripts (Line 45)
- From the project root directory (Line 47)
  - ⚙️ Configuration (Line 51)
  - 🛡️ Safety Features (Line 58)
  - 📋 Best Practices (Line 65)
  - 🔗 Integration (Line 72)

**Key Bullet Points (Sample):**
- **project_cleanup_manager.py**: Clean up temporary files and optimize project st...
- **analyze_project_files.py**: Analyze project structure and generate reports
- **safe_cleanup.py**: Safely remove unnecessary files while preserving important ...
- **generate_process_flow.py**: Generate process flow documentation
- **validate_stored_procedures.py**: Validate database stored procedures
  ... and 15 more bullet points

---

