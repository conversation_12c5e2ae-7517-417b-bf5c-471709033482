{"public_repository_structure": {"name": "N8N_Builder_Community", "description": "AI-Powered Workflow Automation - Community Edition", "version": "1.0.0", "edition": "community"}, "folder_structure": {"root_files": ["README.md", "LICENSE", "requirements.txt", "run.py", "setup.py", "config.yaml", ".giti<PERSON>re", ".augment-guidelines"], "directories": {"Documentation": {"description": "Complete documentation for Community Edition", "include_subdirs": true, "exclude_patterns": ["*private*", "*enterprise*", "*advanced*", "MANUAL_REVIEW_CHECKLIST.md", "PHASE1_COMPLETION_SUMMARY.md", "GITHUB_ORGANIZATION_*"]}, "Scripts": {"description": "Community-relevant scripts only", "include_files": ["Emergency-Shutdown.ps1", "emergency_shutdown.bat", "restore_n8n_setup.ps1", "run_with_venv.bat", "run_with_venv.ps1", "shutdown.bat", "shutdown.py", "Setup-LogRotation.ps1", "generate_process_flow.py", "analyze_project_files.py"], "exclude_patterns": ["*private*", "*detection*", "*verification*", "*sync*", "*cleanup*", "test_*", "debug_*"]}, "n8n_builder": {"description": "Core application code", "include_subdirs": true, "exclude_patterns": ["*private*", "*advanced*", "__pycache__"]}, "n8n-docker": {"description": "Docker environment for n8n", "include_subdirs": true, "exclude_patterns": ["data/", "backups/", "logs/", "config.ps1"]}, "static": {"description": "Web interface assets", "include_subdirs": true}, "config": {"description": "Configuration templates", "include_files": ["ngrok-config.yml.template"], "exclude_patterns": ["*private*", "*.ps1"]}, "agents": {"description": "Agent system components", "include_subdirs": true, "exclude_patterns": ["*private*", "*advanced*"]}}, "exclude_directories": ["Self_Healer", "Self-Healer", "KnowledgeBase", "advanced_systems", "venv", "__pycache__", "cache", "logs", "backups", "archive", "projects/knowledgebase*", "projects/self_healer*", "projects/advanced*"]}, "file_transformations": {"rename_files": {"README_public.md": "README.md", "requirements_public.txt": "requirements.txt", "run_public.py": "run.py", "setup_public.py": "setup.py", "config_public.yaml": "config.yaml", ".gitignore_public": ".giti<PERSON>re", ".augment-guidelines-public": ".augment-guidelines"}, "content_replacements": {"global_patterns": {"Self-Healer": "Enterprise Module", "Self_Healer": "Enterprise_Module", "SelfHealer": "EnterpriseModule", "KnowledgeBase": "Enterprise_Database", "Knowledge_Base": "Enterprise_Database", "healer_config": "enterprise_config", "knowledgebase_config": "enterprise_database_config", "SelfHealerManager": "EnterpriseModuleManager", "KnowledgeBaseIntegrator": "EnterpriseDatabaseIntegrator"}, "import_removals": ["from Self_Healer", "import.*Self_Healer", "from.*knowledge_database_wrapper", "import.*SelfHealerManager", "from.*healer_manager"]}}, "documentation_updates": {"community_edition_features": ["AI-powered workflow generation", "Local LLM integration", "REST API with OpenAPI docs", "Web interface", "Validation system", "Research integration", "Docker support", "Command-line interface"], "remove_references": ["Self-Healer", "KnowledgeBase", "Enterprise Edition", "Advanced features", "Proprietary components", "Private database", "Advanced healing", "Automatic error resolution"], "replace_with_generic": {"advanced error handling": "basic error handling", "enterprise features": "community features", "proprietary system": "open-source system", "advanced analytics": "basic analytics", "enterprise database": "standard database support"}}, "github_repository_settings": {"name": "N8N_Builder", "description": "🤖 AI-Powered Workflow Automation - Transform plain English into n8n workflows using local AI", "topics": ["n8n", "workflow-automation", "ai", "llm", "local-ai", "python", "<PERSON><PERSON><PERSON>", "docker", "automation", "no-code", "low-code", "workflow-generator", "lm-studio"], "homepage": "", "license": "MIT", "default_branch": "main", "features": {"issues": true, "projects": true, "wiki": false, "discussions": true}}, "quality_assurance": {"required_files": ["README.md", "LICENSE", "requirements.txt", "run.py", "setup.py"], "validation_checks": ["No private component references", "All imports work without private modules", "Documentation links are valid", "Examples execute successfully", "API endpoints respond correctly"], "test_commands": ["python -m pytest tests/ -v --tb=short", "python run.py --test-mode", "python -m n8n_builder.cli validate"]}, "sync_configuration": {"verification_enabled": true, "backup_enabled": true, "rollback_on_failure": true, "post_sync_validation": true, "git_initialization": true}}