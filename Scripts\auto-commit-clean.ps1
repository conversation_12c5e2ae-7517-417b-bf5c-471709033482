# N8N Builder - Automatic Commit and Sync (Clean Version)

param(
    [string]$CommitMessage = "",
    [string]$CommunityMessage = "",
    [switch]$AutoSync = $false,
    [switch]$QuickCommit = $false
)

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root

Write-Host ""
Write-Host "N8N Builder - Smart Commit & Sync" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# Check git status
$gitStatus = git status --porcelain
if (-not $gitStatus) {
    Write-Host "No changes to commit - repository is clean" -ForegroundColor Green
    exit 0
}

# Show what will be committed
Write-Host "Files to be committed:" -ForegroundColor Blue
$gitStatus | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
Write-Host ""

# Smart commit message generation
if (-not $CommitMessage) {
    $changedFiles = git diff --name-only HEAD
    $hasDocumentation = $changedFiles | Where-Object { $_ -like "*Documentation*" -or $_ -like "*.md" }
    $hasScripts = $changedFiles | Where-Object { $_ -like "*Scripts*" -or $_ -like "*.ps1" }
    $hasCore = $changedFiles | Where-Object { $_ -like "*n8n_builder*" -or $_ -like "*.py" }
    $hasConfig = $changedFiles | Where-Object { $_ -like "*config*" -or $_ -like "*.json" -or $_ -like "*.yaml" }
    
    # Generate smart default message
    $defaultMessage = ""
    if ($hasDocumentation) { $defaultMessage += "Update documentation" }
    if ($hasScripts) { 
        if ($defaultMessage) { $defaultMessage += " and scripts" }
        else { $defaultMessage = "Update scripts" }
    }
    if ($hasCore) {
        if ($defaultMessage) { $defaultMessage += " and core functionality" }
        else { $defaultMessage = "Update core functionality" }
    }
    if ($hasConfig) {
        if ($defaultMessage) { $defaultMessage += " and configuration" }
        else { $defaultMessage = "Update configuration" }
    }
    if (-not $defaultMessage) { $defaultMessage = "Update project files" }
    
    if ($QuickCommit) {
        $CommitMessage = $defaultMessage
        Write-Host "Auto-generated commit message: '$CommitMessage'" -ForegroundColor Cyan
    } else {
        Write-Host "Suggested: $defaultMessage" -ForegroundColor Yellow
        Write-Host "Enter commit message (or press Enter to use suggestion):" -ForegroundColor Blue
        $userInput = Read-Host "Commit message"
        $CommitMessage = if ($userInput.Trim()) { $userInput } else { $defaultMessage }
    }
}

# Commit to main repository
Write-Host "Committing to main repository..." -ForegroundColor Blue
git add .
git commit -m "$CommitMessage"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Successfully committed to main repository" -ForegroundColor Green
} else {
    Write-Host "Failed to commit to main repository" -ForegroundColor Red
    exit 1
}

# Community sync decision
$shouldSync = $false

if ($AutoSync) {
    $shouldSync = $true
    Write-Host "Auto-sync enabled - syncing to community..." -ForegroundColor Cyan
} elseif ($CommunityMessage) {
    $shouldSync = $true
    Write-Host "Community message provided - syncing..." -ForegroundColor Cyan
} else {
    # Smart default: sync if changes look community-relevant
    $communityRelevant = $changedFiles | Where-Object { 
        $_ -notlike "*Self_Healer*" -and 
        $_ -notlike "*KnowledgeBase*" -and
        $_ -notlike "*private*" -and
        $_ -notlike "*enterprise*"
    }
    
    if ($communityRelevant) {
        if ($QuickCommit) {
            $shouldSync = $true
            Write-Host "Auto-detected community-relevant changes - syncing..." -ForegroundColor Cyan
        } else {
            Write-Host "Detected community-relevant changes. Sync to GitHub? (Y/n):" -ForegroundColor Yellow
            $syncChoice = Read-Host "Sync"
            $shouldSync = ($syncChoice -eq "" -or $syncChoice -eq "y" -or $syncChoice -eq "Y")
        }
    } else {
        Write-Host "Changes appear to be private-only - skipping community sync" -ForegroundColor Yellow
    }
}

if ($shouldSync) {
    # Generate community message if not provided
    if (-not $CommunityMessage) {
        # Create a clean version of the commit message for community
        $CommunityMessage = $CommitMessage -replace "Self.?Healer", "error handling system"
        $CommunityMessage = $CommunityMessage -replace "KnowledgeBase", "knowledge system"
        $CommunityMessage = $CommunityMessage -replace "Enterprise", "advanced"
        $CommunityMessage = $CommunityMessage -replace "private", "internal"
        
        if (-not $QuickCommit) {
            Write-Host "Suggested community message: $CommunityMessage" -ForegroundColor Yellow
            Write-Host "Enter community message (or press Enter to use suggestion):" -ForegroundColor Blue
            $userCommunityInput = Read-Host "Community message"
            if ($userCommunityInput.Trim()) { $CommunityMessage = $userCommunityInput }
        }
    }
    
    # Run the community sync
    Write-Host "Syncing to Community Edition..." -ForegroundColor Blue
    
    if (Test-Path "sync-public.ps1") {
        .\sync-public.ps1 -PublicRepoPath "..\N8N_Builder_Community" -Force
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Community files synced successfully" -ForegroundColor Green
            
            # Push to GitHub (sync script already committed changes)
            $originalLocation = Get-Location
            Set-Location "..\N8N_Builder_Community"

            # Check for pending Pull Requests before force pushing
            Write-Host "Checking for pending Pull Requests..." -ForegroundColor Blue
            $pendingPRs = git ls-remote origin 'refs/pull/*/head' 2>$null
            if ($pendingPRs) {
                Write-Host "⚠️  Warning: There are pending Pull Requests on GitHub" -ForegroundColor Yellow
                Write-Host "Consider reviewing PRs at: https://github.com/vbwyrde/N8N_Builder/pulls" -ForegroundColor Yellow
                Write-Host "Force push will not affect PRs, but you may want to review them first." -ForegroundColor Cyan
                Write-Host ""
            }

            Write-Host "Pushing to GitHub..." -ForegroundColor Blue
            git push origin master --force

            if ($LASTEXITCODE -eq 0) {
                Write-Host "Successfully pushed to GitHub!" -ForegroundColor Green
                Write-Host "Community commit: $CommunityMessage" -ForegroundColor Cyan
                Write-Host "GitHub: https://github.com/vbwyrde/N8N_Builder" -ForegroundColor Blue
            } else {
                Write-Host "Failed to push to GitHub (check network/credentials)" -ForegroundColor Yellow
            }
            
            Set-Location $originalLocation
        } else {
            Write-Host "Community sync failed" -ForegroundColor Yellow
        }
    } else {
        Write-Host "sync-public.ps1 not found - skipping community sync" -ForegroundColor Yellow
    }
} else {
    Write-Host "Skipping community sync" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Workflow Complete!" -ForegroundColor Green
Write-Host "Main repository: '$CommitMessage'" -ForegroundColor Blue
if ($shouldSync -and $CommunityMessage) {
    Write-Host "Community repository: '$CommunityMessage'" -ForegroundColor Blue
}
