#!/usr/bin/env python3
"""
Automatic Script Path Fixer
Automatically updates scripts in the Scripts folder to work properly from their new location
by adding project root detection and updating path references.

This script:
1. Reads the Reset_Paths.md analysis
2. Automatically adds root detection code to scripts
3. Updates path references to use project root
4. Creates backups before making changes
5. Provides detailed logging of all changes
"""

import os
import re
import json
import shutil
from pathlib import Path
from typing import Dict, List, Set, Tuple
from datetime import datetime

class AutoPathFixer:
    def __init__(self):
        self.scripts_dir = Path(__file__).parent
        self.project_root = self.scripts_dir.parent
        self.backup_dir = self.scripts_dir / "backups" / f"path_fixes_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Root detection code templates
        self.python_root_detection = '''import os
from pathlib import Path

# Get project root (parent of Scripts folder)
project_root = Path(__file__).parent.parent
os.chdir(project_root)  # Change working directory to project root

'''

        self.powershell_root_detection = '''# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root

'''

        # Path replacement patterns
        self.path_replacements = {
            # Project folders - these should work as-is after chdir
            'n8n_builder/': 'n8n_builder/',
            'Self_Healer/': 'Self_Healer/',
            'KnowledgeBase/': 'KnowledgeBase/',
            'Documentation/': 'Documentation/',
            'config/': 'config/',
            'data/': 'data/',
            'tests/': 'tests/',
            'Tests/': 'Tests/',
            'archive/': 'archive/',
            'Scripts/': 'Scripts/',
            
            # Root files - these should work as-is after chdir
            'README.md': 'README.md',
            'requirements.txt': 'requirements.txt',
            'setup.py': 'setup.py',
            'run.py': 'run.py',
            '.gitignore': '.gitignore',
            'docker-compose.yml': 'docker-compose.yml',
            'Dockerfile': 'Dockerfile'
        }

    def create_backup(self, file_path: Path) -> Path:
        """Create a backup of the file before modification."""
        backup_path = self.backup_dir / file_path.name
        shutil.copy2(file_path, backup_path)
        return backup_path

    def has_root_detection(self, content: str, file_extension: str) -> bool:
        """Check if the file already has root detection code."""
        if file_extension == '.py':
            return 'project_root = Path(__file__).parent.parent' in content
        elif file_extension == '.ps1':
            return '$ProjectRoot = Split-Path -Parent $PSScriptRoot' in content
        return False

    def add_root_detection_python(self, content: str) -> str:
        """Add root detection code to Python script."""
        lines = content.split('\n')
        
        # Find the best insertion point (after shebang and docstring, before other imports)
        insert_index = 0
        in_docstring = False
        docstring_char = None
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Skip shebang
            if stripped.startswith('#!'):
                insert_index = i + 1
                continue
                
            # Handle docstrings
            if not in_docstring:
                if stripped.startswith('"""') or stripped.startswith("'''"):
                    docstring_char = stripped[:3]
                    if stripped.count(docstring_char) >= 2:  # Single line docstring
                        insert_index = i + 1
                    else:
                        in_docstring = True
                    continue
            else:
                if docstring_char in line:
                    in_docstring = False
                    insert_index = i + 1
                continue
                
            # If we hit an import or other code, stop
            if stripped and not stripped.startswith('#'):
                break
                
        # Insert root detection code
        lines.insert(insert_index, self.python_root_detection.rstrip())
        return '\n'.join(lines)

    def add_root_detection_powershell(self, content: str) -> str:
        """Add root detection code to PowerShell script."""
        lines = content.split('\n')
        
        # Find insertion point (after initial comments)
        insert_index = 0
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped and not stripped.startswith('#'):
                insert_index = i
                break
        else:
            insert_index = len(lines)
            
        # Insert root detection code
        lines.insert(insert_index, self.powershell_root_detection.rstrip())
        return '\n'.join(lines)

    def fix_specific_path_patterns(self, content: str, file_extension: str) -> Tuple[str, List[str]]:
        """Fix specific problematic path patterns in the content."""
        changes = []

        if file_extension == '.py':
            # Fix common Python path patterns that assume root directory

            # Pattern: open("folder/file") -> open("folder/file") (works after chdir)
            # Pattern: Path("folder/file") -> Path("folder/file") (works after chdir)
            # Pattern: "data/file.ext" -> "data/file.ext" (works after chdir)

            # These patterns should work fine after we add chdir to project root
            # The main fix is adding the root detection code

            # However, we can fix some specific patterns that might be problematic:

            # Fix relative imports that might break
            old_patterns = [
                (r'from\s+\.\s+import', 'from . import'),  # Normalize spacing
                (r'from\s+\.\.\s+import', 'from .. import'),  # Normalize spacing
            ]

            for pattern, replacement in old_patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    changes.append(f"Fixed import pattern: {pattern}")

        elif file_extension == '.ps1':
            # Fix PowerShell path patterns

            # Common PowerShell patterns that need fixing:
            # Test-Path "folder\file" -> Test-Path "folder\file" (works after Set-Location)
            # Get-Content "file.txt" -> Get-Content "file.txt" (works after Set-Location)

            # These should work fine after Set-Location to project root
            pass

        return content, changes

    def fix_file_paths(self, file_path: Path) -> Tuple[bool, List[str]]:
        """Fix paths in a single file."""
        changes_made = []

        try:
            # Read original content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                original_content = f.read()

            # Create backup
            backup_path = self.create_backup(file_path)
            changes_made.append(f"Created backup: {backup_path}")

            content = original_content
            file_extension = file_path.suffix.lower()

            # Add root detection if not present
            if not self.has_root_detection(content, file_extension):
                if file_extension == '.py':
                    content = self.add_root_detection_python(content)
                    changes_made.append("Added Python root detection code")
                elif file_extension == '.ps1':
                    content = self.add_root_detection_powershell(content)
                    changes_made.append("Added PowerShell root detection code")
            else:
                changes_made.append("Root detection already present")

            # Fix specific path patterns
            content, pattern_changes = self.fix_specific_path_patterns(content, file_extension)
            changes_made.extend(pattern_changes)

            # Write updated content
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                changes_made.append("File updated successfully")
                return True, changes_made
            else:
                changes_made.append("No changes needed")
                return False, changes_made

        except Exception as e:
            changes_made.append(f"ERROR: {str(e)}")
            return False, changes_made

    def get_files_to_fix(self) -> List[Path]:
        """Get list of files that need fixing based on Reset_Paths.md."""
        reset_paths_file = self.scripts_dir / "Reset_Paths.md"
        
        if not reset_paths_file.exists():
            print("Reset_Paths.md not found. Run analyze_script_paths.py first.")
            return []
            
        files_to_fix = set()
        
        try:
            with open(reset_paths_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Extract file paths from markdown
            lines = content.split('\n')
            for line in lines:
                if line.startswith('### Scripts\\'):
                    # Extract filename from markdown header
                    file_path = line.replace('### Scripts\\', '').strip()
                    script_path = self.scripts_dir / file_path
                    if script_path.exists():
                        files_to_fix.add(script_path)
                        
        except Exception as e:
            print(f"Error reading Reset_Paths.md: {e}")
            return []
            
        return sorted(list(files_to_fix))

    def fix_all_scripts(self, dry_run: bool = False) -> Dict:
        """Fix all scripts that need path updates."""
        files_to_fix = self.get_files_to_fix()
        
        results = {
            'total_files': len(files_to_fix),
            'files_processed': 0,
            'files_modified': 0,
            'files_failed': 0,
            'details': []
        }
        
        print(f"Found {len(files_to_fix)} files to process")
        if dry_run:
            print("DRY RUN MODE - No files will be modified")
            
        for file_path in files_to_fix:
            print(f"\nProcessing: {file_path.name}")
            
            if dry_run:
                results['details'].append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'status': 'would_process',
                    'changes': ['DRY RUN - would add root detection if needed']
                })
                results['files_processed'] += 1
                continue
                
            success, changes = self.fix_file_paths(file_path)
            
            results['files_processed'] += 1
            if success:
                results['files_modified'] += 1
            else:
                results['files_failed'] += 1
                
            results['details'].append({
                'file': str(file_path.relative_to(self.project_root)),
                'status': 'success' if success else 'failed',
                'changes': changes
            })
            
            # Print summary for this file
            for change in changes:
                print(f"  - {change}")
                
        return results

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Automatically fix script paths')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be done without making changes')
    parser.add_argument('--force', action='store_true',
                       help='Skip confirmation prompt')
    
    args = parser.parse_args()
    
    fixer = AutoPathFixer()
    
    if not args.dry_run and not args.force:
        response = input("This will modify script files. Continue? (y/N): ")
        if response.lower() != 'y':
            print("Aborted.")
            return
            
    print("Starting automatic path fixing...")
    results = fixer.fix_all_scripts(dry_run=args.dry_run)
    
    # Print summary
    print(f"\n{'='*50}")
    print("SUMMARY")
    print(f"{'='*50}")
    print(f"Total files: {results['total_files']}")
    print(f"Files processed: {results['files_processed']}")
    print(f"Files modified: {results['files_modified']}")
    print(f"Files failed: {results['files_failed']}")
    
    if not args.dry_run:
        print(f"Backups created in: {fixer.backup_dir}")
        
    # Save detailed results
    results_file = fixer.scripts_dir / f"path_fix_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    print(f"Detailed results saved to: {results_file}")

if __name__ == "__main__":
    main()
