#!/usr/bin/env python3
"""
Documentation Analysis Script for N8N_Builder Project

This script analyzes all Markdown files in the project to create a comprehensive
outline showing folder structure, main headers, and bullet points. This helps
identify documentation redundancy, obsolete content, and consolidation opportunities.

Author: N8N_Builder Development Team
Date: 2025-07-08
Purpose: Combat documentation creep and create consolidation plan
"""

import os
import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

class DocumentationAnalyzer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.analysis_results = []
        self.summary_stats = {
            'total_files': 0,
            'total_folders': 0,
            'total_headers': 0,
            'total_bullet_points': 0,
            'files_by_folder': {},
            'common_topics': {}
        }
    
    def extract_markdown_structure(self, file_path: Path) -> Dict:
        """Extract headers and bullet points from a markdown file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {
                'error': f"Could not read file: {str(e)}",
                'headers': [],
                'bullet_points': [],
                'line_count': 0
            }
        
        lines = content.split('\n')
        headers = []
        bullet_points = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # Extract headers (# ## ### etc.)
            header_match = re.match(r'^(#{1,6})\s+(.+)', line)
            if header_match:
                level = len(header_match.group(1))
                title = header_match.group(2).strip()
                headers.append({
                    'level': level,
                    'title': title,
                    'line': line_num
                })
            
            # Extract bullet points (- * +)
            bullet_match = re.match(r'^[\s]*[-\*\+]\s+(.+)', line)
            if bullet_match:
                content_text = bullet_match.group(1).strip()
                # Calculate indentation level
                indent_level = (len(line) - len(line.lstrip())) // 2
                bullet_points.append({
                    'content': content_text,
                    'indent_level': indent_level,
                    'line': line_num
                })
        
        return {
            'headers': headers,
            'bullet_points': bullet_points,
            'line_count': len(lines),
            'file_size': len(content)
        }
    
    def analyze_folder(self, folder_path: Path) -> List[Dict]:
        """Analyze all markdown files in a folder"""
        folder_results = []
        
        # Find all .md files in this folder
        md_files = list(folder_path.glob('*.md'))
        
        if md_files:
            self.summary_stats['files_by_folder'][str(folder_path.relative_to(self.root_path))] = len(md_files)
        
        for md_file in md_files:
            relative_path = md_file.relative_to(self.root_path)
            structure = self.extract_markdown_structure(md_file)
            
            # Update summary stats
            self.summary_stats['total_headers'] += len(structure.get('headers', []))
            self.summary_stats['total_bullet_points'] += len(structure.get('bullet_points', []))
            
            # Track common topics (header titles)
            for header in structure.get('headers', []):
                topic = header['title'].lower()
                if topic in self.summary_stats['common_topics']:
                    self.summary_stats['common_topics'][topic] += 1
                else:
                    self.summary_stats['common_topics'][topic] = 1
            
            folder_results.append({
                'file_path': str(relative_path),
                'file_name': md_file.name,
                'folder': str(folder_path.relative_to(self.root_path)),
                'structure': structure,
                'last_modified': datetime.fromtimestamp(md_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return folder_results
    
    def scan_all_folders(self):
        """Recursively scan all folders for markdown files"""
        folders_processed = set()

        # Walk through all directories
        for root, dirs, files in os.walk(self.root_path):
            root_path = Path(root)

            # Skip hidden directories, common ignore patterns, and venv folder
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in [
                '__pycache__', 'node_modules', 'venv', '.git', '.vscode',
                'dist', 'build', '.pytest_cache'
            ]]

            # Only process folders that contain .md files
            if any(f.endswith('.md') for f in files):
                folder_results = self.analyze_folder(root_path)
                if folder_results:
                    self.analysis_results.extend(folder_results)
                    folders_processed.add(str(root_path.relative_to(self.root_path)))

        self.summary_stats['total_files'] = len(self.analysis_results)
        self.summary_stats['total_folders'] = len(folders_processed)
    
    def generate_report(self, output_file: str):
        """Generate comprehensive documentation analysis report"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# N8N_Builder Documentation Analysis Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## Executive Summary\n\n")
            f.write(f"- **Total Markdown Files:** {self.summary_stats['total_files']}\n")
            f.write(f"- **Folders with Documentation:** {self.summary_stats['total_folders']}\n")
            f.write(f"- **Total Headers:** {self.summary_stats['total_headers']}\n")
            f.write(f"- **Total Bullet Points:** {self.summary_stats['total_bullet_points']}\n\n")
            
            # Files by folder summary
            f.write("## Documentation Distribution by Folder\n\n")
            for folder, count in sorted(self.summary_stats['files_by_folder'].items()):
                folder_display = folder if folder != '.' else 'ROOT'
                f.write(f"- **{folder_display}:** {count} files\n")
            f.write("\n")
            
            # Common topics (potential redundancy indicators)
            f.write("## Potential Redundancy Analysis\n\n")
            f.write("Headers that appear multiple times across documents:\n\n")
            common_topics = {k: v for k, v in self.summary_stats['common_topics'].items() if v > 1}
            for topic, count in sorted(common_topics.items(), key=lambda x: x[1], reverse=True):
                if count > 2:  # Only show topics that appear 3+ times
                    f.write(f"- **\"{topic}\"** appears {count} times\n")
            f.write("\n")
            
            # Detailed file analysis
            f.write("## Detailed File Analysis\n\n")
            
            # Group by folder for better organization
            files_by_folder = {}
            for result in self.analysis_results:
                folder = result['folder']
                if folder not in files_by_folder:
                    files_by_folder[folder] = []
                files_by_folder[folder].append(result)
            
            for folder in sorted(files_by_folder.keys()):
                folder_display = folder if folder != '.' else 'ROOT'
                f.write(f"### {folder_display}\n\n")
                
                for file_info in sorted(files_by_folder[folder], key=lambda x: x['file_name']):
                    f.write(f"#### {file_info['file_name']}\n\n")
                    f.write(f"- **Path:** `{file_info['file_path']}`\n")
                    f.write(f"- **Last Modified:** {file_info['last_modified']}\n")
                    
                    structure = file_info['structure']
                    if 'error' in structure:
                        f.write(f"- **Error:** {structure['error']}\n\n")
                        continue
                    
                    f.write(f"- **Lines:** {structure['line_count']}\n")
                    f.write(f"- **Size:** {structure['file_size']} characters\n")
                    f.write(f"- **Headers:** {len(structure['headers'])}\n")
                    f.write(f"- **Bullet Points:** {len(structure['bullet_points'])}\n\n")
                    
                    # Show headers
                    if structure['headers']:
                        f.write("**Headers:**\n")
                        for header in structure['headers']:
                            indent = "  " * (header['level'] - 1)
                            f.write(f"{indent}- {header['title']} (Line {header['line']})\n")
                        f.write("\n")
                    
                    # Show first few bullet points as sample
                    if structure['bullet_points']:
                        f.write("**Key Bullet Points (Sample):**\n")
                        for i, bullet in enumerate(structure['bullet_points'][:5]):  # Show first 5
                            indent = "  " * bullet['indent_level']
                            f.write(f"{indent}- {bullet['content'][:80]}{'...' if len(bullet['content']) > 80 else ''}\n")
                        if len(structure['bullet_points']) > 5:
                            f.write(f"  ... and {len(structure['bullet_points']) - 5} more bullet points\n")
                        f.write("\n")
                    
                    f.write("---\n\n")

def main():
    """Main execution function"""
    print("N8N_Builder Documentation Analysis Tool")
    print("=" * 50)
    
    # Get the current working directory (should be project root)
    root_path = os.getcwd()
    print(f"Analyzing documentation in: {root_path}")
    
    # Create analyzer and run analysis
    analyzer = DocumentationAnalyzer(root_path)
    print("Scanning folders for Markdown files...")
    analyzer.scan_all_folders()
    
    # Generate report
    output_file = "data/documentation_analysis_report.md"
    os.makedirs("data", exist_ok=True)
    
    print(f"Generating report: {output_file}")
    analyzer.generate_report(output_file)
    
    print("\nAnalysis Complete!")
    print(f"- Found {analyzer.summary_stats['total_files']} Markdown files")
    print(f"- Across {analyzer.summary_stats['total_folders']} folders")
    print(f"- Total headers: {analyzer.summary_stats['total_headers']}")
    print(f"- Total bullet points: {analyzer.summary_stats['total_bullet_points']}")
    print(f"\nReport saved to: {output_file}")

if __name__ == "__main__":
    main()
