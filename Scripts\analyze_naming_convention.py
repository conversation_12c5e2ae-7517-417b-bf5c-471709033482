#!/usr/bin/env python3
"""
Naming Convention Analysis Script
=================================
Analyzes existing _public and _community files to determine migration strategy.
Follows the "Script First" approach to minimize LLM inferences.

Usage: python analyze_naming_convention.py [--execute-migration]
"""

import os
import json
import hashlib
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any

class NamingConventionAnalyzer:
    """Analyzes and migrates naming conventions from _public to _community."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.analysis_results = {
            "scan_time": datetime.now().isoformat(),
            "public_files": {},
            "community_files": {},
            "migration_plan": {},
            "recommendations": []
        }
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Get detailed information about a file."""
        if not file_path.exists():
            return {"exists": False}
        
        stat = file_path.stat()
        with open(file_path, 'rb') as f:
            content_hash = hashlib.md5(f.read()).hexdigest()
        
        return {
            "exists": True,
            "size": stat.st_size,
            "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "hash": content_hash,
            "path": str(file_path)
        }
    
    def find_public_files(self) -> Dict[str, Dict]:
        """Find all files with _public suffix."""
        public_files = {}
        
        # Search for _public files in root
        for pattern in ["*_public.*"]:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    base_name = file_path.name.replace("_public", "")
                    public_files[base_name] = self.get_file_info(file_path)
        
        return public_files
    
    def find_community_files(self) -> Dict[str, Dict]:
        """Find all files with _community suffix."""
        community_files = {}
        
        # Search for _community files in root
        for pattern in ["*_community.*", "*community*"]:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    # Extract base name
                    if "_community" in file_path.name:
                        base_name = file_path.name.replace("_community", "")
                    else:
                        # Handle cases like README_community.md
                        base_name = file_path.name.replace("community", "")
                        if base_name.startswith("_"):
                            base_name = base_name[1:]
                        if base_name.endswith("_."):
                            base_name = base_name[:-2] + base_name[-1:]
                    
                    community_files[base_name] = self.get_file_info(file_path)
        
        return community_files
    
    def compare_files(self, public_info: Dict, community_info: Dict) -> str:
        """Compare two files and determine relationship."""
        if not public_info.get("exists") and not community_info.get("exists"):
            return "both_missing"
        elif not public_info.get("exists"):
            return "community_only"
        elif not community_info.get("exists"):
            return "public_only"
        elif public_info["hash"] == community_info["hash"]:
            return "identical"
        elif public_info["size"] == community_info["size"]:
            return "same_size_different_content"
        else:
            return "different_files"
    
    def analyze_migration_strategy(self) -> Dict[str, Any]:
        """Analyze what migration strategy to use for each file."""
        public_files = self.find_public_files()
        community_files = self.find_community_files()
        
        self.analysis_results["public_files"] = public_files
        self.analysis_results["community_files"] = community_files
        
        migration_plan = {}
        
        # Get all unique base names
        all_base_names = set(public_files.keys()) | set(community_files.keys())
        
        for base_name in all_base_names:
            public_info = public_files.get(base_name, {"exists": False})
            community_info = community_files.get(base_name, {"exists": False})
            
            comparison = self.compare_files(public_info, community_info)
            
            migration_plan[base_name] = {
                "public_file": public_info,
                "community_file": community_info,
                "comparison": comparison,
                "recommended_action": self.get_recommended_action(comparison, base_name)
            }
        
        return migration_plan
    
    def get_recommended_action(self, comparison: str, base_name: str) -> str:
        """Get recommended action based on file comparison."""
        if comparison == "public_only":
            return f"rename_public_to_community"
        elif comparison == "community_only":
            return "keep_community_remove_public_references"
        elif comparison == "identical":
            return "remove_public_keep_community"
        elif comparison == "different_files":
            return "manual_review_required"
        elif comparison == "same_size_different_content":
            return "manual_review_required"
        else:
            return "no_action_needed"
    
    def generate_recommendations(self, migration_plan: Dict) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        for base_name, plan in migration_plan.items():
            action = plan["recommended_action"]
            
            if action == "rename_public_to_community":
                public_path = plan["public_file"]["path"]
                new_path = public_path.replace("_public", "_community")
                recommendations.append(f"RENAME: {public_path} → {new_path}")
            
            elif action == "remove_public_keep_community":
                public_path = plan["public_file"]["path"]
                recommendations.append(f"DELETE: {public_path} (identical to community version)")
            
            elif action == "manual_review_required":
                recommendations.append(f"REVIEW: {base_name} - files differ, manual comparison needed")
        
        return recommendations
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run complete analysis."""
        print("🔍 Analyzing naming convention migration...")
        
        migration_plan = self.analyze_migration_strategy()
        recommendations = self.generate_recommendations(migration_plan)
        
        self.analysis_results["migration_plan"] = migration_plan
        self.analysis_results["recommendations"] = recommendations
        
        return self.analysis_results
    
    def display_results(self):
        """Display analysis results."""
        print("\n" + "="*70)
        print("📊 NAMING CONVENTION ANALYSIS RESULTS")
        print("="*70)
        
        print(f"\n📁 Public Files Found: {len(self.analysis_results['public_files'])}")
        for name, info in self.analysis_results['public_files'].items():
            print(f"   • {name}: {info['path']}")
        
        print(f"\n📁 Community Files Found: {len(self.analysis_results['community_files'])}")
        for name, info in self.analysis_results['community_files'].items():
            print(f"   • {name}: {info['path']}")
        
        print(f"\n🎯 Migration Recommendations:")
        for i, rec in enumerate(self.analysis_results['recommendations'], 1):
            print(f"   {i}. {rec}")
        
        print("="*70)
    
    def save_results(self, output_file: str = "naming_convention_analysis.json"):
        """Save analysis results to file."""
        with open(output_file, 'w') as f:
            json.dump(self.analysis_results, f, indent=2)
        print(f"📄 Analysis saved to: {output_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Analyze naming convention migration")
    parser.add_argument("--output", default="naming_convention_analysis.json", help="Output file")
    parser.add_argument("--execute-migration", action="store_true", help="Execute migration (not implemented yet)")
    
    args = parser.parse_args()
    
    analyzer = NamingConventionAnalyzer()
    results = analyzer.run_analysis()
    analyzer.display_results()
    analyzer.save_results(args.output)
    
    if args.execute_migration:
        print("\n⚠️  Migration execution not implemented yet - analysis only")

if __name__ == "__main__":
    main()
