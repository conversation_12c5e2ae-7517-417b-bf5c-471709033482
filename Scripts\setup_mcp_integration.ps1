# Complete MCP Integration Setup for N8N_Builder
# This script sets up the complete MCP integration with VS Code

param(
    [switch]$SkipVSCodeCheck,
    [switch]$SkipDependencies,
    [switch]$TestOnly
)

Write-Host "N8N_Builder MCP Integration Setup" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# Step 1: Check VS Code version
if (-not $SkipVSCodeCheck) {
    Write-Host "`nStep 1: Checking VS Code version..." -ForegroundColor Yellow
    
    try {
        $version = & code --version 2>$null | Select-Object -First 1
        Write-Host "VS Code version: $version" -ForegroundColor Green
        
        if ($version -match "^(\d+)\.(\d+)") {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            
            if ($major -eq 1 -and $minor -ge 102) {
                Write-Host "VS Code supports MCP!" -ForegroundColor Green
            } else {
                Write-Host "VS Code version too old for MCP (requires 1.102+)" -ForegroundColor Red
                Write-Host "Please upgrade VS Code first." -ForegroundColor Yellow
                exit 1
            }
        }
    } catch {
        Write-Host "VS Code not found. Please install VS Code 1.102+ first." -ForegroundColor Red
        exit 1
    }
}

# Step 2: Install MCP dependencies
if (-not $SkipDependencies -and -not $TestOnly) {
    Write-Host "`nStep 2: Installing MCP dependencies..." -ForegroundColor Yellow
    
    try {
        & powershell -ExecutionPolicy Bypass -File "Scripts\install_mcp_dependencies.ps1"
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Dependency installation failed!" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "Error running dependency installer: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Step 3: Test MCP servers
Write-Host "`nStep 3: Testing MCP servers..." -ForegroundColor Yellow

$servers = @(
    @{Name="Research Server"; Module="n8n_builder.mcp_research_server"},
    @{Name="Database Server"; Module="n8n_builder.mcp_database_server"},
    @{Name="Workflow Server"; Module="n8n_builder.mcp_workflow_server"}
)

$allServersWorking = $true

foreach ($server in $servers) {
    Write-Host "Testing $($server.Name)..." -ForegroundColor Gray
    
    try {
        # Test import
        $testResult = & python -c "
import sys
sys.path.insert(0, '.')
try:
    from $($server.Module) import server
    print('IMPORT_OK')
except ImportError as e:
    print(f'IMPORT_ERROR: {e}')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1
        
        if ($testResult -match "IMPORT_OK") {
            Write-Host "$($server.Name): OK" -ForegroundColor Green
        } elseif ($testResult -match "IMPORT_ERROR") {
            Write-Host "$($server.Name): Import Error - $testResult" -ForegroundColor Red
            $allServersWorking = $false
        } else {
            Write-Host "$($server.Name): Error - $testResult" -ForegroundColor Red
            $allServersWorking = $false
        }
    } catch {
        Write-Host "$($server.Name): Failed - $($_.Exception.Message)" -ForegroundColor Red
        $allServersWorking = $false
    }
}

# Step 4: Verify MCP configuration
Write-Host "`nStep 4: Verifying MCP configuration..." -ForegroundColor Yellow

$mcpConfigPath = ".vscode\mcp.json"
if (Test-Path $mcpConfigPath) {
    Write-Host "MCP configuration found: $mcpConfigPath" -ForegroundColor Green
    
    try {
        $mcpConfig = Get-Content $mcpConfigPath | ConvertFrom-Json
        $serverCount = $mcpConfig.servers.PSObject.Properties.Count
        Write-Host "Configured MCP servers: $serverCount" -ForegroundColor Green
        
        # List configured servers
        foreach ($serverName in $mcpConfig.servers.PSObject.Properties.Name) {
            Write-Host "  - $serverName" -ForegroundColor White
        }
    } catch {
        Write-Host "Error reading MCP configuration: $($_.Exception.Message)" -ForegroundColor Red
        $allServersWorking = $false
    }
} else {
    Write-Host "MCP configuration not found at: $mcpConfigPath" -ForegroundColor Red
    $allServersWorking = $false
}

# Step 5: Create test script for VS Code
Write-Host "`nStep 5: Creating VS Code test script..." -ForegroundColor Yellow

$testScript = @"
# VS Code MCP Test Script
# Run this in VS Code to test MCP integration

## Test 1: Research Tool
Ask the AI to search N8N documentation:
"Search for HTTP Request node documentation"

## Test 2: Database Tool  
Ask the AI to query the database:
"Show me the database schema"

## Test 3: Workflow Generator
Ask the AI to generate a workflow:
"Create a workflow that sends a Slack message when a new email arrives"

## Expected Results:
- Tools should be available in VS Code agent mode
- AI should be able to call N8N_Builder tools
- Results should be formatted and useful

## Troubleshooting:
- Check VS Code version (1.102+)
- Verify MCP servers are running
- Check .vscode/mcp.json configuration
- Look at VS Code output panel for errors
"@

$testScriptPath = "Scripts\test_mcp_in_vscode.md"
$testScript | Out-File -FilePath $testScriptPath -Encoding UTF8
Write-Host "Test script created: $testScriptPath" -ForegroundColor Green

# Final results
Write-Host "`nSetup Results:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan

if ($allServersWorking) {
    Write-Host "SUCCESS: MCP integration setup completed!" -ForegroundColor Green
    
    Write-Host "`nNext steps:" -ForegroundColor White
    Write-Host "1. Open VS Code in this workspace" -ForegroundColor Gray
    Write-Host "2. Open a file and start a chat session" -ForegroundColor Gray
    Write-Host "3. Enable agent mode in the chat" -ForegroundColor Gray
    Write-Host "4. Try the test commands in: $testScriptPath" -ForegroundColor Gray
    Write-Host "5. Check that N8N_Builder tools are available" -ForegroundColor Gray
    
    Write-Host "`nMCP Configuration:" -ForegroundColor White
    Write-Host "- Research Server: Available for N8N documentation search" -ForegroundColor Gray
    Write-Host "- Database Server: Available for KnowledgeBase queries" -ForegroundColor Gray  
    Write-Host "- Workflow Server: Available for workflow generation" -ForegroundColor Gray
    
} else {
    Write-Host "FAILED: Some components are not working properly!" -ForegroundColor Red
    Write-Host "Please check the error messages above and fix issues." -ForegroundColor Yellow
    
    Write-Host "`nCommon issues:" -ForegroundColor White
    Write-Host "- Missing MCP package: pip install mcp" -ForegroundColor Gray
    Write-Host "- VS Code too old: Upgrade to 1.102+" -ForegroundColor Gray
    Write-Host "- Python path issues: Check PYTHONPATH" -ForegroundColor Gray
    Write-Host "- Virtual environment: Activate venv first" -ForegroundColor Gray
    
    exit 1
}

Write-Host "`nMCP Integration setup completed!" -ForegroundColor Green
