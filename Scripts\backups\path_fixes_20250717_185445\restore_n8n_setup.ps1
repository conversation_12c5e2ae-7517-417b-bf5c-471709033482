# N8N Docker Setup Restoration Script
# This script restores your n8n environment after Docker reinstall

param(
    [switch]$DryRun,
    [switch]$Execute
)

Write-Host "🔄 N8N Docker Setup Restoration" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

if (-not $DryRun -and -not $Execute) {
    Write-Host "❌ Please specify either -DryRun or -Execute" -ForegroundColor Red
    Write-Host "Usage: .\restore_n8n_setup.ps1 -DryRun    # Preview restoration steps"
    Write-Host "       .\restore_n8n_setup.ps1 -Execute   # Execute restoration"
    exit 1
}

# Check if Docker is installed and running
function Test-DockerStatus {
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-Host "✅ Docker installed: $dockerVersion" -ForegroundColor Green
            
            # Test if Docker daemon is running
            $dockerInfo = docker info 2>$null
            if ($dockerInfo) {
                Write-Host "✅ Docker daemon is running" -ForegroundColor Green
                return $true
            } else {
                Write-Host "⚠️ Docker installed but daemon not running" -ForegroundColor Yellow
                return $false
            }
        } else {
            Write-Host "❌ Docker not found" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error checking Docker status: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Available workflows to restore
$workflows = @(
    @{
        Name = "BlogToTweet"
        Path = "projects/elthosdb1/BlogToTweet.json"
        Description = "Blog-to-Tweet automation workflow"
    },
    @{
        Name = "KnowledgeBase"
        Path = "projects/knowledgebase1/KB1.json"
        Description = "KnowledgeBase integration workflow"
    },
    @{
        Name = "Test Workflow"
        Path = "projects/test-1/test-workflow.json"
        Description = "Test workflow for validation"
    }
)

Write-Host ""
Write-Host "📋 Available Workflows to Restore:" -ForegroundColor Cyan
foreach ($workflow in $workflows) {
    $exists = Test-Path $workflow.Path
    $status = if ($exists) { "✅ Found" } else { "❌ Missing" }
    $color = if ($exists) { "Green" } else { "Red" }
    Write-Host "  $status $($workflow.Name): $($workflow.Description)" -ForegroundColor $color
    Write-Host "    Path: $($workflow.Path)" -ForegroundColor Gray
}

if ($DryRun) {
    Write-Host ""
    Write-Host "🔍 DRY RUN - Restoration Steps:" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "1. ✅ Check Docker status" -ForegroundColor Yellow
    Write-Host "2. 🐳 Navigate to n8n-docker directory" -ForegroundColor Yellow
    Write-Host "3. 🔧 Start n8n with docker-compose" -ForegroundColor Yellow
    Write-Host "4. ⏳ Wait for n8n to be ready" -ForegroundColor Yellow
    Write-Host "5. 🌐 Open n8n interface (http://localhost:5678)" -ForegroundColor Yellow
    Write-Host "6. 📥 Import available workflows:" -ForegroundColor Yellow
    
    foreach ($workflow in $workflows) {
        if (Test-Path $workflow.Path) {
            Write-Host "   - Import $($workflow.Name) from $($workflow.Path)" -ForegroundColor Yellow
        }
    }
    
    Write-Host ""
    Write-Host "🎯 To execute restoration: .\restore_n8n_setup.ps1 -Execute" -ForegroundColor Cyan
}

if ($Execute) {
    Write-Host ""
    Write-Host "🚀 Executing N8N Restoration..." -ForegroundColor Green
    
    # Step 1: Check Docker
    Write-Host ""
    Write-Host "Step 1: Checking Docker status..." -ForegroundColor Cyan
    if (-not (Test-DockerStatus)) {
        Write-Host "❌ Docker is not ready. Please ensure Docker Desktop is installed and running." -ForegroundColor Red
        exit 1
    }
    
    # Step 2: Navigate to n8n-docker
    Write-Host ""
    Write-Host "Step 2: Preparing n8n-docker environment..." -ForegroundColor Cyan
    if (-not (Test-Path "n8n-docker")) {
        Write-Host "❌ n8n-docker directory not found" -ForegroundColor Red
        exit 1
    }
    
    Set-Location "n8n-docker"
    Write-Host "✅ Changed to n8n-docker directory" -ForegroundColor Green
    
    # Step 3: Start n8n
    Write-Host ""
    Write-Host "Step 3: Starting n8n with Docker Compose..." -ForegroundColor Cyan
    try {
        # Stop any existing containers
        docker-compose down 2>$null
        
        # Start n8n
        Write-Host "Starting n8n containers..." -ForegroundColor Gray
        docker-compose up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ N8N containers started successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to start n8n containers" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "❌ Error starting n8n: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    # Step 4: Wait for n8n to be ready
    Write-Host ""
    Write-Host "Step 4: Waiting for n8n to be ready..." -ForegroundColor Cyan
    $maxWait = 60
    $waited = 0
    $ready = $false
    
    while ($waited -lt $maxWait -and -not $ready) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5678" -TimeoutSec 5 -UseBasicParsing 2>$null
            if ($response.StatusCode -eq 200) {
                $ready = $true
                Write-Host "✅ N8N is ready!" -ForegroundColor Green
            }
        } catch {
            Write-Host "⏳ Waiting for n8n... ($waited/$maxWait seconds)" -ForegroundColor Gray
            Start-Sleep -Seconds 5
            $waited += 5
        }
    }
    
    if (-not $ready) {
        Write-Host "⚠️ N8N may not be fully ready yet, but continuing..." -ForegroundColor Yellow
    }
    
    # Step 5: Open n8n interface
    Write-Host ""
    Write-Host "Step 5: Opening n8n interface..." -ForegroundColor Cyan
    Start-Process "http://localhost:5678"
    Write-Host "✅ N8N interface opened in browser" -ForegroundColor Green
    
    # Step 6: Display workflow import instructions
    Write-Host ""
    Write-Host "Step 6: Workflow Import Instructions" -ForegroundColor Cyan
    Write-Host "=====================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎯 To import your workflows:" -ForegroundColor Yellow
    Write-Host "1. Go to http://localhost:5678 (should be open in browser)" -ForegroundColor White
    Write-Host "2. Click 'Import from file' or use Ctrl+O" -ForegroundColor White
    Write-Host "3. Import these workflow files:" -ForegroundColor White
    
    Set-Location ".."  # Go back to project root
    
    foreach ($workflow in $workflows) {
        if (Test-Path $workflow.Path) {
            $fullPath = (Resolve-Path $workflow.Path).Path
            Write-Host "   📄 $($workflow.Name): $fullPath" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "🎉 N8N Restoration Complete!" -ForegroundColor Green
    Write-Host "📍 N8N URL: http://localhost:5678" -ForegroundColor Cyan
    Write-Host "📁 Workflow files are ready for import" -ForegroundColor Cyan
}
