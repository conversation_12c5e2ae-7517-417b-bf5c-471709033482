{"scan_metadata": {"scan_time": "2025-07-05T21:23:19.140067", "scan_type": "comprehensive_repository_analysis", "local_repo_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder", "github_user": "vbwyrde", "end_time": "2025-07-05T21:23:20.614400", "duration_seconds": 1.474333}, "local_repository": {"repository_root": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder", "git_info": {"remote_url": {"success": true, "output": "https://github.com/vbwyrde/N8N_Builder.git", "error": ""}, "current_branch": {"success": true, "output": "master", "error": ""}, "status": {"success": true, "output": "M .augment-guidelines\n D .gitignore_public\n D 2.5.2\n D ARCHITECTURE.md\n M Documentation/technical/PYTHON_ENVIRONMENT_SETUP.md\n D Emergency-Shutdown.ps1\n M GETTING_STARTED.md\n D GITHUB_SETUP_INSTRUCTIONS.md\n M README.md\n D README_public.md\n D SYSTEMATIC_REMEDIATION_PLAN.md\n M Scripts/clean_commit_messages.py\n M Scripts/fix_analyze_files.py\n M Scripts/generate_process_flow.py\n M Scripts/test_safe_cleanup.py\n D check_db_state.py\n D check_schema.py\n D comprehensive-audit.ps1\n D config_public.yaml\n D create_analytics_procedure.py\n D create_simple_session_procedure.py\n D debug_error_criteria.py\n D deploy_public.ps1\n D emergency_shutdown.bat\n D example_enhanced_workflow.py\n D feedback_log.json\n D logging_config.py\n M n8n_builder/n8n_builder.py\n D ngrok-config.yml.template\n D private-component-audit.json\n D requirements_public.txt\n D restore_n8n_setup.ps1\n D run_public.py\n D run_with_venv.bat\n D run_with_venv.ps1\n D setup_public.py\n D shutdown.bat\n D shutdown.py\n M sync-public.ps1\n D verify-public-clean.ps1\n?? .gitignore_community\n?? Documentation/ARCHITECTURE.md\n?? Documentation/FOLDER_ORGANIZATION.md\n?? Documentation/GITHUB_ORGANIZATION_HANDOFF.md\n?? Documentation/GITHUB_ORGANIZATION_SUMMARY.md\n?? Documentation/GITHUB_ORGANIZATION_TASKS.md\n?? Documentation/GITHUB_SETUP_INSTRUCTIONS.md\n?? Documentation/MANUAL_REVIEW_CHECKLIST.md\n?? Documentation/PHASE1_COMPLETION_SUMMARY.md\n?? Documentation/PHASE2_COMPLETION_SUMMARY.md\n?? Documentation/PHASE3_COMPLETION_SUMMARY.md\n?? Documentation/SYSTEMATIC_REMEDIATION_PLAN.md\n?? README_community.md\n?? Scripts/Emergency-Shutdown.ps1\n?? Scripts/analyze_naming_convention.py\n?? Scripts/analyze_workspace_folders.py\n?? Scripts/cleanup-root-folder.ps1\n?? Scripts/compare_readme_files.py\n?? Scripts/comprehensive-audit.ps1\n?? Scripts/comprehensive_repo_scan.py\n?? Scripts/consolidate-self-healer.ps1\n?? Scripts/deploy_public.ps1\n?? Scripts/detect-private-components.ps1\n?? Scripts/dev_config.json\n?? Scripts/dev_publish.py\n?? Scripts/emergency_shutdown.bat\n?? Scripts/execute_separation.py\n?? Scripts/fix_script_references.py\n?? Scripts/github_repository_setup.py\n?? Scripts/migrate_naming_convention.py\n?? Scripts/pre_execution_verification.py\n?? Scripts/prepare_public_release.py\n?? Scripts/public_repo_config.json\n?? Scripts/restore_n8n_setup.ps1\n?? Scripts/run_with_venv.bat\n?? Scripts/run_with_venv.ps1\n?? Scripts/sanitize_documentation.py\n?? Scripts/shutdown.bat\n?? Scripts/shutdown.py\n?? Scripts/sync-public.ps1\n?? Scripts/test-detection.ps1\n?? Scripts/test_detection_simple.py\n?? Scripts/test_enhanced_sync.py\n?? Scripts/test_verification_systems.py\n?? Scripts/update_folder_references.py\n?? Scripts/verification_pipeline.py\n?? Scripts/verify-public-clean.ps1\n?? archive/\n?? backup_naming_migration_20250705_161137/\n?? comprehensive_repo_analysis.json\n?? comprehensive_repo_analysis_updated.json\n?? config/\n?? config_community.yaml\n?? data/\n?? n8n_builder/example_enhanced_workflow.py\n?? naming_convention_analysis.json\n?? pre_sync_detection.json\n?? repo_scan_results.json\n?? requirements_community.txt\n?? run_community.py\n?? separation_detection.json\n?? separation_detection.md\n?? setup_community.py\n?? test_after_fixes.json\n?? workspace_analysis.json", "error": ""}, "last_commit": {"success": true, "output": "fb29463 Moved SH files to sub folder", "error": ""}}, "file_structure": {"root_files": [{"name": ".augment-guidelines", "size": 4717, "modified": "2025-07-05T16:03:44.627313"}, {"name": ".augment-guidelines-public", "size": 2634, "modified": "2025-07-01T19:56:42.391649"}, {"name": ".giti<PERSON>re", "size": 4162, "modified": "2025-07-03T17:18:35.191243"}, {"name": ".gitignore_community", "size": 2461, "modified": "2025-07-01T19:56:08.303336"}, {"name": "cleanup-root-folder.log", "size": 5567, "modified": "2025-07-04T15:36:58.557751"}, {"name": "comprehensive_repo_analysis.json", "size": 10271, "modified": "2025-07-05T14:46:32.533159"}, {"name": "comprehensive_repo_analysis_updated.json", "size": 10466, "modified": "2025-07-05T15:12:54.469844"}, {"name": "config_community.yaml", "size": 2278, "modified": "2025-07-01T19:58:14.487708"}, {"name": "detect_private_components.py", "size": 13890, "modified": "2025-07-04T18:45:32.914664"}, {"name": "FEATURES.md", "size": 1877, "modified": "2025-07-03T00:10:58.835635"}, {"name": "folder_reference_updates.log", "size": 1519, "modified": "2025-07-05T16:29:35.311860"}, {"name": "GETTING_STARTED.md", "size": 7814, "modified": "2025-07-04T16:24:42.397548"}, {"name": "LIGHTNING_START.md", "size": 1018, "modified": "2025-06-24T20:29:28.694778"}, {"name": "naming_convention_analysis.json", "size": 3931, "modified": "2025-07-05T16:04:41.538412"}, {"name": "naming_migration.log", "size": 1242, "modified": "2025-07-05T16:11:37.974651"}, {"name": "pre_sync_detection.json", "size": 1373992170, "modified": "2025-07-05T15:19:15.619906"}, {"name": "README.md", "size": 8441, "modified": "2025-07-04T17:53:17.888529"}, {"name": "README_community.md", "size": 7216, "modified": "2025-07-04T23:48:58.844254"}, {"name": "repo_scan_results.json", "size": 18117, "modified": "2025-07-05T16:38:42.895572"}, {"name": "requirements.txt", "size": 733, "modified": "2025-06-27T22:54:51.255578"}, {"name": "requirements_community.txt", "size": 645, "modified": "2025-07-01T19:55:11.892572"}, {"name": "run.py", "size": 36325, "modified": "2025-06-30T13:03:56.790359"}, {"name": "run_community.py", "size": 7834, "modified": "2025-07-01T19:54:56.852611"}, {"name": "script_reference_fixes.log", "size": 1580, "modified": "2025-07-05T16:33:31.779730"}, {"name": "separation_detection.json", "size": 80741402, "modified": "2025-07-05T15:17:46.239477"}, {"name": "separation_detection.md", "size": 23663126, "modified": "2025-07-05T15:17:46.364376"}, {"name": "setup.py", "size": 1173, "modified": "2025-06-21T17:01:55.310286"}, {"name": "setup_community.py", "size": 1916, "modified": "2025-07-01T19:56:21.788566"}, {"name": "sync-public.log", "size": 8970, "modified": "2025-07-05T16:29:35.302985"}, {"name": "sync-public.ps1", "size": 19354, "modified": "2025-07-05T16:33:31.774143"}, {"name": "test_after_fixes.json", "size": 11685, "modified": "2025-07-05T16:35:02.864644"}, {"name": "workspace_analysis.json", "size": 6749, "modified": "2025-07-05T16:29:35.303498"}], "directories": [{"name": "agents", "file_count": 1}, {"name": "archive", "file_count": 3}, {"name": "backup_naming_migration_20250705_161137", "file_count": 4}, {"name": "cache", "file_count": 9}, {"name": "config", "file_count": 2}, {"name": "data", "file_count": 10}, {"name": "Documentation", "file_count": 30}, {"name": "logs", "file_count": 2}, {"name": "n8n-docker", "file_count": 73}, {"name": "n8n_builder", "file_count": 368}, {"name": "n8n_builder.egg-info", "file_count": 5}, {"name": "projects", "file_count": 11}, {"name": "<PERSON><PERSON><PERSON>", "file_count": 66}, {"name": "Self_Healer", "file_count": 82}, {"name": "static", "file_count": 1}, {"name": "tests", "file_count": 41}, {"name": "venv", "file_count": 6003}, {"name": "__pycache__", "file_count": 2}]}, "private_components": {"detected": [{"name": "Self_Healer", "type": "directory", "exists": true}]}, "public_components": {"community_suffix_files": [{"name": "README_community.md", "size": 7216, "exists": true}, {"name": "requirements_community.txt", "size": 645, "exists": true}, {"name": "run_community.py", "size": 7834, "exists": true}, {"name": "setup_community.py", "size": 1916, "exists": true}, {"name": "config_community.yaml", "size": 2278, "exists": true}, {"name": ".gitignore_community", "size": 2461, "exists": true}]}, "configuration_files": {"separation_system": [{"name": "sync-public.ps1", "size": 19354, "exists": true}, {"name": "Scripts/public_repo_config.json", "size": 6234, "exists": true}, {"name": "detect_private_components.py", "size": 13890, "exists": true}]}}, "github_repositories": {"scan_method": "github_cli_success", "repositories": [{"createdAt": "2025-07-03T21:50:53Z", "description": "AI-Powered Workflow Automation - Transform plain English into powerful N8N workflows using local AI models. Built with Augment Code to demonstrate advanced AI-assisted development capabilities.", "name": "N8N_Builder", "updatedAt": "2025-07-03T21:56:03Z", "url": "https://github.com/vbwyrde/N8N_Builder", "visibility": "PUBLIC"}, {"createdAt": "2025-05-31T05:10:31Z", "description": "", "name": "AG_UI_Test", "updatedAt": "2025-06-07T19:38:53Z", "url": "https://github.com/vbwyrde/AG_UI_Test", "visibility": "PUBLIC"}, {"createdAt": "2024-01-16T02:16:53Z", "description": "KnowledgeBase is a database designed to store knowledge, and rate facts and opinions with a Validity Rating.", "name": "KnowledgeBase", "updatedAt": "2025-04-27T02:10:17Z", "url": "https://github.com/vbwyrde/KnowledgeBase", "visibility": "PRIVATE"}, {"createdAt": "2025-01-15T04:38:25Z", "description": "Some tools that I find useful when using AI for development", "name": "AI_Dev_Helpers", "updatedAt": "2025-01-18T15:29:41Z", "url": "https://github.com/vbwyrde/AI_Dev_Helpers", "visibility": "PUBLIC"}, {"createdAt": "2024-09-24T00:17:32Z", "description": "", "name": "BT_ImageSync", "updatedAt": "2024-09-24T00:17:38Z", "url": "https://github.com/vbwyrde/BT_ImageSync", "visibility": "PRIVATE"}, {"createdAt": "2021-09-30T13:09:13Z", "description": "Config files for my GitHub profile.", "name": "vbwyrde", "updatedAt": "2024-07-26T03:43:51Z", "url": "https://github.com/vbwyrde/vbwyrde", "visibility": "PUBLIC"}, {"createdAt": "2024-03-18T02:35:42Z", "description": "DSPY Experiments", "name": "DSPY_VBWyrde", "updatedAt": "2024-09-30T00:25:40Z", "url": "https://github.com/vbwyrde/DSPY_VBWyrde", "visibility": "PUBLIC"}, {"createdAt": "2024-04-27T23:09:45Z", "description": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "2024-05-02T12:28:57Z", "url": "https://github.com/vbwyrde/ElthosRouter", "visibility": "PRIVATE"}, {"createdAt": "2023-12-18T13:17:09Z", "description": "AI_Exchange is a database system for extending AI Model memory with Project-Oriented Structure.  ", "name": "AI_Exchange", "updatedAt": "2023-12-18T13:18:41Z", "url": "https://github.com/vbwyrde/AI_Exchange", "visibility": "PRIVATE"}, {"createdAt": "2023-02-03T00:54:49Z", "description": "", "name": "TestErrors", "updatedAt": "2023-02-03T00:54:57Z", "url": "https://github.com/vbwyrde/TestErrors", "visibility": "PRIVATE"}, {"createdAt": "2022-11-30T18:15:37Z", "description": "This project is designed to help developers to clean up code by offering a query method to locate issues.  It should combine with ClassChecker so that the two projects share the same features and distill into a unified method.", "name": "StylishCSS", "updatedAt": "2022-11-30T18:15:52Z", "url": "https://github.com/vbwyrde/StylishCSS", "visibility": "PRIVATE"}, {"createdAt": "2021-01-11T21:28:15Z", "description": "", "name": "ClassChecker_Local", "updatedAt": "2022-11-28T18:03:11Z", "url": "https://github.com/vbwyrde/ClassChecker_Local", "visibility": "PRIVATE"}, {"createdAt": "2022-02-23T17:14:52Z", "description": "This is just a test of Source Control of DBs in Github", "name": "Test_DB", "updatedAt": "2022-02-23T19:53:59Z", "url": "https://github.com/vbwyrde/Test_DB", "visibility": "PUBLIC"}, {"createdAt": "2022-01-27T14:59:08Z", "description": "", "name": "WebAPI_2", "updatedAt": "2022-03-31T22:11:23Z", "url": "https://github.com/vbwyrde/WebAPI_2", "visibility": "PRIVATE"}], "n8n_builder_details": {"createdAt": "2025-07-03T21:50:53Z", "description": "AI-Powered Workflow Automation - Transform plain English into powerful N8N workflows using local AI models. Built with Augment Code to demonstrate advanced AI-assisted development capabilities.", "name": "N8N_Builder", "updatedAt": "2025-07-03T21:56:03Z", "url": "https://github.com/vbwyrde/N8N_Builder", "visibility": "PUBLIC"}, "knowledgebase_details": {"createdAt": "2024-01-16T02:16:53Z", "description": "KnowledgeBase is a database designed to store knowledge, and rate facts and opinions with a Validity Rating.", "name": "KnowledgeBase", "updatedAt": "2025-04-27T02:10:17Z", "url": "https://github.com/vbwyrde/KnowledgeBase", "visibility": "PRIVATE"}, "scan_errors": []}, "analysis": {"repository_status": {"n8n_builder_exists_github": true, "knowledgebase_exists_github": true, "local_git_configured": true, "current_branch": "master"}, "private_component_status": {"private_components_detected_locally": 1, "community_suffix_files_ready": 6, "separation_system_files": 3}, "separation_readiness": {"required_files_present": 4, "total_required_files": 4, "readiness_percentage": 100.0}, "github_organization_status": {"n8n_builder": {"visibility": "PUBLIC", "default_branch": "unknown", "created_at": "2025-07-03T21:50:53Z", "description": "AI-Powered Workflow Automation - Transform plain English into powerful N8N workflows using local AI models. Built with Augment Code to demonstrate advanced AI-assisted development capabilities."}, "knowledgebase": {"visibility": "PRIVATE", "access_status": "accessible"}}, "critical_issues": ["N8N_Builder repository exists on GitHub while private components exist locally - potential leak risk"], "recommendations": ["Address critical issues before proceeding with GitHub organization", "Separation system appears ready - can proceed with separation execution", "Existing N8N_Builder repository detected - consider backup/archive strategy"]}, "recommendations": []}