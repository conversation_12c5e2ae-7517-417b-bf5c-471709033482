# ============================================================================
# N8N_Builder Enhanced Public Repository Sync Script
# ============================================================================
# This script syncs only public/community files from the private development
# repository to a clean public repository for GitHub publication.
#
# ENHANCED FEATURES:
# - Updated for new folder structure (Scripts/, Documentation/, config/, etc.)
# - Integrated with verification pipeline
# - Enhanced private reference cleaning
# - Comprehensive error handling and logging
# - Rollback capabilities
#
# USAGE: .\sync-public.ps1 [-PublicRepoPath "C:\path\to\public\repo"] [-DryRun] [-RunVerification]
# ============================================================================

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
param(
    [string]$PublicRepoPath = "..\N8N_Builder_Community",
    [switch]$DryRun = $false,
    [switch]$Force = $false,
    [switch]$RunVerification = $false,
    [switch]$SkipTests = $false,
    [string]$LogLevel = "INFO"
)

# Configuration
$SourceRepo = Get-Location
$LogFile = "Scripts\sync-public.log"
$BackupPath = "$PublicRepoPath.backup"
$VerificationScript = "verification_pipeline.py"
$DetectionScript = "detect_private_components.py"

# ============================================================================
# ENHANCED LOGGING FUNCTIONS
# ============================================================================
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"

    # Color coding for console output
    switch ($Level) {
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        "WARNING" { Write-Host $logEntry -ForegroundColor Yellow }
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        default { Write-Host $logEntry }
    }

    # Always log to file
    Add-Content -Path $LogFile -Value $logEntry
}

function Write-Success { param([string]$Message) Write-Log $Message "SUCCESS" }
function Write-Warning { param([string]$Message) Write-Log $Message "WARNING" }
function Write-Error { param([string]$Message) Write-Log $Message "ERROR" }

# ============================================================================
# ENHANCED PUBLIC FILES CONFIGURATION (Updated for New Folder Structure)
# ============================================================================

# Files to copy with _public suffix (rename during copy)
$PublicSuffixFiles = @{
    ".augment-guidelines-public" = ".augment-guidelines"
    ".gitignore_public" = ".gitignore"
    "README_public.md" = "README.md"
    "requirements_public.txt" = "requirements.txt"
    "run_public.py" = "run.py"
    "setup_public.py" = "setup.py"
    "config_public.yaml" = "config.yaml"
}

# Directories to copy entirely (public components)
$PublicDirectories = @(
    "n8n-docker",
    "Documentation",
    "static",
    "n8n_builder",
    "config",
    "agents"
)

# Individual files to copy as-is (updated for new structure)
$PublicFiles = @(
    "FEATURES.md",
    "GETTING_STARTED.md",
    "LIGHTNING_START.md",
    "Emergency-Shutdown.ps1",
    "emergency_shutdown.bat",
    "restore_n8n_setup.ps1",
    "run_with_venv.bat",
    "run_with_venv.ps1",
    "shutdown.bat",
    "shutdown.py",
    "Setup-LogRotation.ps1",
    "generate_process_flow.py",
    "analyze_project_files.py"
)

# Public Scripts to include (community-relevant only)
$PublicScripts = @(
    "Emergency-Shutdown.ps1",
    "emergency_shutdown.bat",
    "restore_n8n_setup.ps1",
    "run_with_venv.bat",
    "run_with_venv.ps1",
    "shutdown.bat",
    "shutdown.py",
    "Setup-LogRotation.ps1",
    "generate_process_flow.py",
    "analyze_project_files.py"
)

# ENHANCED EXCLUDE PATTERNS (Based on Detection System)
$ExcludePatterns = @(
    # Private Component Directories
    "Self-Healer*",
    "Self_Healer*",
    "KnowledgeBase*",
    "advanced_systems*",

    # Private Files and Scripts
    "*self_healer*",
    "*knowledgebase*",
    "*_private*",
    "*_advanced*",
    "debug_self_healer*",
    "test_*self_healer*",
    "test_*knowledgebase*",
    "cleanup_self_healer*",
    "cleanup_duplicate_folder*",
    "check_healer_status*",
    "fix_healer_sync*",

    # Private Configuration Files
    "healer_config*",
    "knowledgebase_config*",
    "config_private*",
    "advanced_config*",

    # Private Project Folders
    "projects/knowledgebase*",
    "projects/self_healer*",
    "projects/advanced*",

    # System and Cache Directories
    "logs/",
    "cache/",
    "venv/",
    "__pycache__/",
    "*.pyc",
    "*.log",
    ".env*",
    "config.ps1",
    "ngrok.yml",

    # Private Analysis and Reports
    "safe_project_analysis_*",
    "pre_commit_cleanup_summary.json",
    "private-component-*",
    "*detection*report*",
    "*verification*results*",

    # Development and Testing Files
    "test_detection_*",
    "test_verification_*",
    "*_test_*",
    "README_KnowledgeBase_Testing.md"
)

# ============================================================================
# ENHANCED SYNC FUNCTIONS WITH VERIFICATION INTEGRATION
# ============================================================================

function Invoke-PreSyncVerification {
    Write-Log "=== PRE-SYNC VERIFICATION ==="

    if (-not $RunVerification) {
        Write-Log "Verification skipped (use -RunVerification to enable)"
        return $true
    }

    # Run detection on source repository
    Write-Log "Running private component detection on source repository..."
    $detectionCmd = "python `"$DetectionScript`" --path `"$SourceRepo`" --output `"pre_sync_detection.json`""

    try {
        $result = Invoke-Expression $detectionCmd

        if (Test-Path "pre_sync_detection.json") {
            $detectionResults = Get-Content "pre_sync_detection.json" | ConvertFrom-Json
            $referencesFound = $detectionResults.scan_metadata.total_references

            if ($referencesFound -gt 0) {
                Write-Warning "Found $referencesFound private references in source repository"
                Write-Warning "Review pre_sync_detection.json for details"
                return $false
            } else {
                Write-Success "No private references detected - safe to proceed"
                return $true
            }
        } else {
            Write-Warning "Detection report not generated - proceeding with caution"
            return $true
        }
    }
    catch {
        Write-Warning "Pre-sync verification failed: $($_.Exception.Message)"
        return $true  # Don't block sync on verification failure
    }
}

function Invoke-PostSyncVerification {
    Write-Log "=== POST-SYNC VERIFICATION ==="

    if (-not $RunVerification) {
        Write-Log "Verification skipped"
        return $true
    }

    if (-not (Test-Path $PublicRepoPath)) {
        Write-Error "Public repository path does not exist: $PublicRepoPath"
        return $false
    }

    # Run detection on public repository
    Write-Log "Running private component detection on public repository..."
    $detectionCmd = "python `"$DetectionScript`" --path `"$PublicRepoPath`" --output `"post_sync_detection.json`""

    try {
        $result = Invoke-Expression $detectionCmd

        if (Test-Path "post_sync_detection.json") {
            $detectionResults = Get-Content "post_sync_detection.json" | ConvertFrom-Json
            $referencesFound = $detectionResults.scan_metadata.total_references

            if ($referencesFound -gt 0) {
                Write-Error "CRITICAL: Found $referencesFound private references in public repository!"
                Write-Error "This is a security issue - review post_sync_detection.json immediately"
                return $false
            } else {
                Write-Success "✅ No private references detected in public repository"
                return $true
            }
        } else {
            Write-Warning "Post-sync detection report not generated"
            return $false
        }
    }
    catch {
        Write-Error "Post-sync verification failed: $($_.Exception.Message)"
        return $false
    }
}

function New-BackupIfExists {
    if (Test-Path $PublicRepoPath) {
        $backupPath = "$PublicRepoPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Write-Log "Creating backup of existing public repository: $backupPath"

        if ($DryRun) {
            Write-Log "[DRY RUN] Would create backup: $backupPath"
        } else {
            Copy-Item -Path $PublicRepoPath -Destination $backupPath -Recurse -Force
            Write-Success "Backup created: $backupPath"
        }
    }
}

function Initialize-PublicRepo {
    Write-Log "Initializing public repository at: $PublicRepoPath"

    if ($DryRun) {
        Write-Log "[DRY RUN] Would create directory: $PublicRepoPath"
        return $true
    }

    if (Test-Path $PublicRepoPath) {
        if (-not $Force) {
            Write-Warning "Public repo directory already exists. Use -Force to overwrite."
            return $false
        }
        Write-Log "Removing existing public repo directory..."
        Remove-Item -Path $PublicRepoPath -Recurse -Force
    }

    New-Item -Path $PublicRepoPath -ItemType Directory -Force | Out-Null
    Write-Success "Created public repository directory"
    return $true
}

function Copy-PublicSuffixFiles {
    Write-Log "Copying files with _public suffix..."
    
    foreach ($sourceFile in $PublicSuffixFiles.Keys) {
        $targetFile = $PublicSuffixFiles[$sourceFile]
        $sourcePath = Join-Path $SourceRepo $sourceFile
        $targetPath = Join-Path $PublicRepoPath $targetFile
        
        if (Test-Path $sourcePath) {
            if ($DryRun) {
                Write-Log "[DRY RUN] Would copy: $sourceFile -> $targetFile"
            } else {
                Copy-Item -Path $sourcePath -Destination $targetPath -Force
                Write-Success "Copied: $sourceFile -> $targetFile"
            }
        } else {
            Write-Warning "Source file not found: $sourceFile"
        }
    }
}

function Copy-PublicDirectories {
    Write-Log "Copying public directories..."
    
    foreach ($dir in $PublicDirectories) {
        $sourcePath = Join-Path $SourceRepo $dir
        $targetPath = Join-Path $PublicRepoPath $dir
        
        if (Test-Path $sourcePath) {
            if ($DryRun) {
                Write-Log "[DRY RUN] Would copy directory: $dir"
            } else {
                # Create target directory
                New-Item -Path $targetPath -ItemType Directory -Force | Out-Null
                
                # Copy contents, excluding private patterns
                $items = Get-ChildItem -Path $sourcePath -Recurse
                foreach ($item in $items) {
                    $relativePath = $item.FullName.Substring($sourcePath.Length + 1)
                    $shouldExclude = $false
                    
                    foreach ($pattern in $ExcludePatterns) {
                        if ($relativePath -like $pattern -or $item.Name -like $pattern) {
                            $shouldExclude = $true
                            break
                        }
                    }
                    
                    if (-not $shouldExclude) {
                        $targetItemPath = Join-Path $targetPath $relativePath
                        $targetItemDir = Split-Path $targetItemPath -Parent
                        
                        if (-not (Test-Path $targetItemDir)) {
                            New-Item -Path $targetItemDir -ItemType Directory -Force | Out-Null
                        }
                        
                        if ($item.PSIsContainer) {
                            if (-not (Test-Path $targetItemPath)) {
                                New-Item -Path $targetItemPath -ItemType Directory -Force | Out-Null
                            }
                        } else {
                            Copy-Item -Path $item.FullName -Destination $targetItemPath -Force
                        }
                    }
                }
                Write-Success "Copied directory: $dir"
            }
        } else {
            Write-Warning "Source directory not found: $dir"
        }
    }
}

function Copy-PublicFiles {
    Write-Log "Copying individual public files..."
    
    foreach ($file in $PublicFiles) {
        $sourcePath = Join-Path $SourceRepo $file
        $targetPath = Join-Path $PublicRepoPath $file
        
        if (Test-Path $sourcePath) {
            if ($DryRun) {
                Write-Log "[DRY RUN] Would copy: $file"
            } else {
                $targetDir = Split-Path $targetPath -Parent
                if (-not (Test-Path $targetDir)) {
                    New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
                }
                Copy-Item -Path $sourcePath -Destination $targetPath -Force
                Write-Success "Copied: $file"
            }
        } else {
            Write-Warning "Source file not found: $file"
        }
    }
}

function Remove-PrivateReferences {
    Write-Log "=== ENHANCED PRIVATE REFERENCE CLEANING ==="

    if ($DryRun) {
        Write-Log "[DRY RUN] Would clean private references from files"
        return
    }

    # Get all text files that might contain references
    $filesToClean = Get-ChildItem -Path $PublicRepoPath -Recurse -Include "*.py", "*.yaml", "*.yml", "*.md", "*.txt", "*.json", "*.ps1" -ErrorAction SilentlyContinue
    $filesModified = 0

    foreach ($file in $filesToClean) {
        try {
            $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
            if ($content) {
                $originalContent = $content

                # Enhanced private reference replacement (case-sensitive order matters)
                # Process in order from most specific to least specific
                $content = $content -replace 'SelfHealerManager', 'EnterpriseModuleManager'
                $content = $content -replace 'KnowledgeBaseIntegrator', 'EnterpriseDatabaseIntegrator'
                $content = $content -replace 'from Self_Healer', 'from Enterprise_Module'
                $content = $content -replace 'Self-Healer', 'Enterprise Module'
                $content = $content -replace 'Self_Healer', 'Enterprise_Module'
                $content = $content -replace 'SelfHealer', 'EnterpriseModule'
                $content = $content -replace 'KnowledgeBase', 'Enterprise_Database'
                $content = $content -replace 'Knowledge_Base', 'Enterprise_Database'
                $content = $content -replace 'healer_config', 'enterprise_config'
                $content = $content -replace 'knowledgebase_config', 'enterprise_database_config'

                # Case-insensitive replacements for lowercase variants
                $content = $content -ireplace '\bself-healer\b', 'enterprise-module'
                $content = $content -ireplace '\bself_healer\b', 'enterprise_module'
                $content = $content -ireplace '\bselfhealer\b', 'enterprisemodule'
                $content = $content -ireplace '\bknowledgebase\b', 'enterprise_database'
                $content = $content -ireplace '\bknowledge_base\b', 'enterprise_database'
                $content = $content -ireplace '\bknowledge-base\b', 'enterprise-database'

                # Remove import statements that reference private components
                $content = $content -replace 'from Self_Healer\..*', '# Private component import removed'
                $content = $content -replace 'import.*SelfHealer.*', '# Private component import removed'

                if ($content -ne $originalContent) {
                    Set-Content -Path $file.FullName -Value $content -NoNewline
                    $relativePath = $file.FullName.Replace($PublicRepoPath, "").TrimStart('\')
                    Write-Success "Cleaned private references in: $relativePath"
                    $filesModified++
                }
            }
        }
        catch {
            Write-Warning "Error processing file $($file.FullName): $($_.Exception.Message)"
        }
    }

    Write-Log "Private reference cleaning completed - $filesModified files modified"
}

function Initialize-GitRepo {
    if ($DryRun) {
        Write-Log "[DRY RUN] Would initialize git repository in public directory"
        return
    }

    Push-Location $PublicRepoPath
    try {
        if (-not (Test-Path ".git")) {
            git init
            Write-Success "Initialized git repository"
        }

        git add .
        git commit -m "Sync from private repository - Community Edition"
        Write-Success "Committed changes to public repository"
    }
    finally {
        Pop-Location
    }
}

# ============================================================================
# ENHANCED MAIN EXECUTION WITH VERIFICATION INTEGRATION
# ============================================================================

Write-Log "🚀 Starting Enhanced N8N_Builder Public Repository Sync"
Write-Log "Source: $SourceRepo"
Write-Log "Target: $PublicRepoPath"
Write-Log "Dry Run: $DryRun"
Write-Log "Run Verification: $RunVerification"
Write-Log "Skip Tests: $SkipTests"

# Stage 1: Pre-sync verification
if (-not (Invoke-PreSyncVerification)) {
    Write-Error "❌ Pre-sync verification failed - aborting sync"
    Write-Error "Review detection results and clean private references before retrying"
    exit 1
}

# Stage 2: Create backup if public repo exists
New-BackupIfExists

# Stage 3: Initialize public repository
if (-not (Initialize-PublicRepo)) {
    Write-Error "❌ Failed to initialize public repository"
    exit 1
}

# Stage 4: Copy files
Write-Log "=== COPYING FILES ==="
Copy-PublicSuffixFiles
Copy-PublicDirectories
Copy-PublicFiles

# Stage 5: Clean private references
Remove-PrivateReferences

# Stage 6: Initialize Git repository
if (-not $DryRun) {
    Initialize-GitRepo
}

# Stage 7: Post-sync verification
if (-not (Invoke-PostSyncVerification)) {
    Write-Error "❌ CRITICAL: Post-sync verification failed!"
    Write-Error "Private components detected in public repository - this is a security issue"
    Write-Error "Do NOT publish this repository until issues are resolved"
    exit 1
}

# Stage 8: Final success
Write-Success "🎉 Enhanced public repository sync completed successfully!"
Write-Success "✅ All verification checks passed"
Write-Log "📄 Log file: $LogFile"

if ($DryRun) {
    Write-Log "ℹ️ This was a dry run. Use without -DryRun to perform actual sync."
} else {
    Write-Success "🚀 Public repository is ready for GitHub publication!"
    Write-Log "📁 Public repository location: $PublicRepoPath"
}
