# Simple and reliable commit message cleanup script
# This approach uses git filter-repo (if available) or provides manual instructions

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
Write-Host "🔧 N8N_Builder Commit Message Cleanup" -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Yellow
Write-Host ""

# Check if we're in a git repository
if (-not (Test-Path ".git")) {
    Write-Host "❌ Error: Not in a git repository" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Found problematic commit messages that need cleaning:" -ForegroundColor Cyan
Write-Host ""

# List of problematic commits and their replacements
$commits = @(
    @{Hash="13c2909"; Old="Remove additional private Self-Healer and KnowledgeBase development files"; New="Remove additional private development files"},
    @{Hash="f389ffc"; Old="Enhanced .gitignore to comprehensively exclude all private Self-Healer and KnowledgeBase components"; New="Enhanced .gitignore to exclude private components"},
    @{Hash="e02c18f"; Old="Remove private Self-Healer and KnowledgeBase components from public repository"; New="Remove private components from public repository"},
    @{Hash="f9be49d"; Old="update gitignore for self-healer and knowledgebase"; New="update gitignore for private components"},
    @{Hash="c40bdd7"; Old="Updates for Self-Healer Separation Finalization"; New="Updates for system separation finalization"},
    @{Hash="ca02330"; Old="Updates to KnowledgeBase Table structures and SP"; New="Updates to database table structures and SP"},
    @{Hash="d8f3583"; Old="Updates to Self-Healer"; New="Updates to system components"},
    @{Hash="c45d762"; Old="Self-Healder - KnowledgeBase Fix"; New="System component fixes"},
    @{Hash="29e2b84"; Old="Self-Healer & KnowledgeBase Updates 4"; New="System component updates 4"},
    @{Hash="8143b61"; Old="Self-Healer & KnowledgeBase Updates 3"; New="System component updates 3"},
    @{Hash="6d40198"; Old="Self-Healer & KnowledgeBse Integration Updates 2"; New="System integration updates 2"},
    @{Hash="f42a2a7"; Old="Self-Healer & KnowledgeBase Integration Updates"; New="System integration updates"},
    @{Hash="6967f06"; Old="KnowledgeBase Updates"; New="Database component updates"},
    @{Hash="383c2d0"; Old="Update to Full version of Self-Healer"; New="Update to full system version"},
    @{Hash="a200e16"; Old="Fixes to Self-Healer and Documentation"; New="Fixes to system and documentation"},
    @{Hash="89f5def"; Old="Updates to Self-Healer / KnowledgeBase"; New="Updates to system components"},
    @{Hash="7a89efa"; Old="Self-Healer Finalized and Integrated with KnowledgeBase - First Pass"; New="System integration finalized - first pass"},
    @{Hash="5ec7918"; Old="Self Healer Updates"; New="System component updates"},
    @{Hash="a96eb48"; Old="Self-Healer"; New="System component implementation"}
)

# Display the commits that will be changed
foreach ($commit in $commits) {
    Write-Host "  $($commit.Hash) - " -NoNewline -ForegroundColor Gray
    Write-Host "$($commit.Old)" -ForegroundColor Red
    Write-Host "           → " -NoNewline -ForegroundColor Gray  
    Write-Host "$($commit.New)" -ForegroundColor Green
    Write-Host ""
}

Write-Host "⚠️  WARNING: This will rewrite Git history!" -ForegroundColor Red
Write-Host "📋 Make sure you have backups before proceeding." -ForegroundColor Yellow
Write-Host ""

$response = Read-Host "Do you want to proceed? (y/N)"
if ($response -ne "y" -and $response -ne "Y") {
    Write-Host "❌ Operation cancelled by user" -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "📋 Creating backup branch..." -ForegroundColor Cyan
$backupBranch = "backup-before-cleanup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
git branch $backupBranch

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Created backup branch: $backupBranch" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to create backup branch" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🔄 Rewriting commit messages using git filter-branch..." -ForegroundColor Cyan

# Set environment variable to suppress filter-branch warning
$env:FILTER_BRANCH_SQUELCH_WARNING = 1

# Create a sed script for message replacement
$sedScript = "temp_sed_script.txt"
$sedCommands = @()

foreach ($commit in $commits) {
    $oldEscaped = $commit.Old -replace '/', '\/' -replace '&', '\&' -replace '"', '\"'
    $newEscaped = $commit.New -replace '/', '\/' -replace '&', '\&' -replace '"', '\"'
    $sedCommands += "s/^$oldEscaped$/$newEscaped/"
}

$sedCommands | Out-File -FilePath $sedScript -Encoding UTF8

try {
    Write-Host "📝 Created sed script with $($sedCommands.Count) replacements" -ForegroundColor Green
    
    # Use git filter-branch with sed to replace messages
    git filter-branch -f --msg-filter "sed -f $sedScript" -- --all
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Successfully rewrote commit messages!" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "🎉 Cleanup completed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Next steps:" -ForegroundColor Cyan
        Write-Host "1. Review changes: git log --oneline | head -20" -ForegroundColor White
        Write-Host "2. Force push to remote: git push --force-with-lease" -ForegroundColor White
        Write-Host "3. If issues occur, restore: git reset --hard $backupBranch" -ForegroundColor White
        Write-Host ""
        Write-Host "⚠️  Remember to force push to update the remote repository!" -ForegroundColor Yellow
        
    } else {
        Write-Host "❌ Error during git filter-branch operation" -ForegroundColor Red
        Write-Host "💡 You can restore using: git reset --hard $backupBranch" -ForegroundColor Yellow
        exit 1
    }
    
} catch {
    Write-Host "❌ Exception during operation: $_" -ForegroundColor Red
    Write-Host "💡 You can restore using: git reset --hard $backupBranch" -ForegroundColor Yellow
    exit 1
} finally {
    # Clean up temporary files
    if (Test-Path $sedScript) {
        Remove-Item $sedScript
        Write-Host "🧹 Cleaned up temporary files" -ForegroundColor Green
    }
}
