# Nginx Configuration for N8N Stable Proxy
# Forwards all traffic from localhost:8080 to n8n:5678

events {
    worker_connections 1024;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Upstream to n8n
    upstream n8n_backend {
        server n8n:5678;
    }

    # Main server block
    server {
        listen 80;
        server_name localhost;

        # Increase client max body size for large workflows
        client_max_body_size 50M;

        # Proxy settings for WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Main location - proxy everything to n8n
        location / {
            proxy_pass http://n8n_backend;
            
            # Additional headers for n8n compatibility
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-Port $server_port;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "Stable proxy is running\n";
            add_header Content-Type text/plain;
        }
    }
}
