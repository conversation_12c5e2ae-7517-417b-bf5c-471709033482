# Test Blogger API Setup Script
# This script helps validate your Blogger API configuration

param(
    [string]$BlogId = "",
    [string]$AccessToken = "",
    [switch]$Interactive = $false
)

Write-Host "🧪 Blogger API Test Script" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Cyan

# Interactive mode for getting parameters
if ($Interactive -or -not $BlogId) {
    Write-Host "`n📝 Interactive Setup Mode" -ForegroundColor Yellow
    
    if (-not $BlogId) {
        Write-Host "`nTo find your Blog ID:" -ForegroundColor White
        Write-Host "1. Go to https://www.blogger.com/" -ForegroundColor White
        Write-Host "2. Select your blog" -ForegroundColor White
        Write-Host "3. Look at the URL for blogID=XXXXXXXXXXXXXXXXX" -ForegroundColor White
        Write-Host "4. Or use the get-blogger-blog-id.ps1 script" -ForegroundColor White
        $BlogId = Read-Host "`nEnter your Blog ID"
    }
    
    if (-not $AccessToken) {
        Write-Host "`nTo get an Access Token for testing:" -ForegroundColor White
        Write-Host "1. Go to https://developers.google.com/oauthplayground/" -ForegroundColor White
        Write-Host "2. In Step 1, add scope: https://www.googleapis.com/auth/blogger.readonly" -ForegroundColor White
        Write-Host "3. Click 'Authorize APIs' and complete OAuth flow" -ForegroundColor White
        Write-Host "4. In Step 2, click 'Exchange authorization code for tokens'" -ForegroundColor White
        Write-Host "5. Copy the 'Access token' value" -ForegroundColor White
        $AccessToken = Read-Host "`nEnter your Access Token (or press Enter to skip token test)"
    }
}

# Test 1: Basic API connectivity
Write-Host "`n🔍 Test 1: Basic API Connectivity" -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "https://www.googleapis.com/blogger/v3/users/self" -Headers @{
        "Authorization" = "Bearer $AccessToken"
    } -ErrorAction Stop
    
    Write-Host "✅ API connectivity successful" -ForegroundColor Green
    Write-Host "User ID: $($response.id)" -ForegroundColor White
    Write-Host "Display Name: $($response.displayName)" -ForegroundColor White
} catch {
    if ($AccessToken) {
        Write-Host "❌ API connectivity failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Check your access token and try again" -ForegroundColor Yellow
    } else {
        Write-Host "⏭️  Skipping API connectivity test (no access token provided)" -ForegroundColor Yellow
    }
}

# Test 2: Blog ID validation
Write-Host "`n🔍 Test 2: Blog ID Validation" -ForegroundColor Green
if ($BlogId -and $AccessToken) {
    try {
        $blogResponse = Invoke-RestMethod -Uri "https://www.googleapis.com/blogger/v3/blogs/$BlogId" -Headers @{
            "Authorization" = "Bearer $AccessToken"
        } -ErrorAction Stop
        
        Write-Host "✅ Blog ID is valid" -ForegroundColor Green
        Write-Host "Blog Name: $($blogResponse.name)" -ForegroundColor White
        Write-Host "Blog URL: $($blogResponse.url)" -ForegroundColor White
        Write-Host "Total Posts: $($blogResponse.posts.totalItems)" -ForegroundColor White
        Write-Host "Last Updated: $($blogResponse.updated)" -ForegroundColor White
    } catch {
        Write-Host "❌ Blog ID validation failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Check your Blog ID and try again" -ForegroundColor Yellow
    }
} else {
    Write-Host "⏭️  Skipping Blog ID validation (missing Blog ID or access token)" -ForegroundColor Yellow
}

# Test 3: Posts retrieval
Write-Host "`n🔍 Test 3: Posts Retrieval" -ForegroundColor Green
if ($BlogId -and $AccessToken) {
    try {
        $postsResponse = Invoke-RestMethod -Uri "https://www.googleapis.com/blogger/v3/blogs/$BlogId/posts?maxResults=5&status=LIVE" -Headers @{
            "Authorization" = "Bearer $AccessToken"
        } -ErrorAction Stop
        
        Write-Host "✅ Posts retrieval successful" -ForegroundColor Green
        Write-Host "Posts returned: $($postsResponse.items.Count)" -ForegroundColor White
        
        if ($postsResponse.items.Count -gt 0) {
            Write-Host "`n📄 Sample Posts:" -ForegroundColor Cyan
            foreach ($post in $postsResponse.items[0..2]) {
                Write-Host "• $($post.title)" -ForegroundColor White
                Write-Host "  URL: $($post.url)" -ForegroundColor Gray
                Write-Host "  Published: $($post.published)" -ForegroundColor Gray
                Write-Host ""
            }
        }
    } catch {
        Write-Host "❌ Posts retrieval failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Check your Blog ID and permissions" -ForegroundColor Yellow
    }
} else {
    Write-Host "⏭️  Skipping posts retrieval (missing Blog ID or access token)" -ForegroundColor Yellow
}

# Test 4: N8N workflow format validation
Write-Host "`n🔍 Test 4: N8N Workflow Format Validation" -ForegroundColor Green
if ($BlogId) {
    $workflowPath = "projects\elthosdb1\ElthosRPG_Blog_Twitter_BloggerAPI.json"
    
    if (Test-Path $workflowPath) {
        try {
            $workflow = Get-Content $workflowPath | ConvertFrom-Json
            
            # Check if Blog ID is updated
            $setBlogIdNode = $workflow.nodes | Where-Object { $_.name -eq "Set Blog ID" }
            if ($setBlogIdNode) {
                $blogIdValue = $setBlogIdNode.parameters.assignments.assignments[0].value
                
                if ($blogIdValue -eq "YOUR_BLOG_ID_HERE") {
                    Write-Host "⚠️  Blog ID not updated in workflow" -ForegroundColor Yellow
                    Write-Host "💡 Update the 'Set Blog ID' node with your actual Blog ID: $BlogId" -ForegroundColor Yellow
                } elseif ($blogIdValue -eq $BlogId) {
                    Write-Host "✅ Blog ID correctly configured in workflow" -ForegroundColor Green
                } else {
                    Write-Host "⚠️  Blog ID mismatch in workflow" -ForegroundColor Yellow
                    Write-Host "Workflow has: $blogIdValue" -ForegroundColor Gray
                    Write-Host "Expected: $BlogId" -ForegroundColor Gray
                }
            }
            
            # Check for OAuth credential placeholder
            $apiNode = $workflow.nodes | Where-Object { $_.name -eq "Get Blog Posts via API" }
            if ($apiNode -and $apiNode.credentials.googleOAuth2Api.id -eq "BLOGGER_OAUTH_CREDENTIAL_ID") {
                Write-Host "⚠️  OAuth credential ID not updated in workflow" -ForegroundColor Yellow
                Write-Host "💡 Update the 'Get Blog Posts via API' node with your actual OAuth credential ID" -ForegroundColor Yellow
            }
            
        } catch {
            Write-Host "❌ Workflow validation failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  Workflow file not found: $workflowPath" -ForegroundColor Yellow
        Write-Host "💡 Make sure you're running this script from the project root directory" -ForegroundColor Yellow
    }
} else {
    Write-Host "⏭️  Skipping workflow validation (no Blog ID provided)" -ForegroundColor Yellow
}

# Summary and next steps
Write-Host "`n📋 Summary and Next Steps" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

if ($BlogId -and $AccessToken) {
    Write-Host "✅ Blog ID: $BlogId" -ForegroundColor Green
    Write-Host "✅ Access Token: Provided" -ForegroundColor Green
} else {
    Write-Host "⚠️  For complete testing, provide both Blog ID and Access Token" -ForegroundColor Yellow
}

Write-Host "`n🔧 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Update workflow with your Blog ID" -ForegroundColor White
Write-Host "2. Configure OAuth2 credentials in N8N" -ForegroundColor White
Write-Host "3. Test the workflow in N8N interface" -ForegroundColor White
Write-Host "4. Run the complete ElthosRPG Blog to Twitter workflow" -ForegroundColor White

Write-Host "`n🔗 Helpful Resources:" -ForegroundColor Blue
Write-Host "• Setup Guide: Documentation\Blogger-API-Workflow-Setup.md" -ForegroundColor White
Write-Host "• Comparison: Documentation\Blogger-Workflow-Comparison.md" -ForegroundColor White
Write-Host "• Blog ID Helper: Scripts\get-blogger-blog-id.ps1" -ForegroundColor White
Write-Host "• Blogger API Docs: https://developers.google.com/blogger/docs/3.0/reference/" -ForegroundColor White

Write-Host "`n🎯 Expected Results:" -ForegroundColor Magenta
Write-Host "After successful setup, your workflow will:" -ForegroundColor White
Write-Host "• Reliably fetch blog posts using official API" -ForegroundColor White
Write-Host "• Randomly select posts for Twitter promotion" -ForegroundColor White
Write-Host "• Handle errors gracefully with clear messages" -ForegroundColor White
Write-Host "• Provide rich metadata for future enhancements" -ForegroundColor White
