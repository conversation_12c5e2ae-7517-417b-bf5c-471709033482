# PowerShell script to rewrite all problematic commit messages
# WARNING: This will rewrite Git history. Make sure you have backups!

Write-Host "🔧 Starting comprehensive commit message cleanup..." -ForegroundColor Yellow
Write-Host "⚠️  WARNING: This will rewrite Git history - make sure you have backups!" -ForegroundColor Red
Write-Host ""

# Create backup branch
Write-Host "📋 Creating backup branch..." -ForegroundColor Cyan
git branch backup-before-cleanup-$(Get-Date -Format "yyyyMMdd-HHmmss")
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Backup branch created successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to create backup branch" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🔄 Rewriting commit messages..." -ForegroundColor Cyan

# Set environment variable to suppress filter-branch warning
$env:FILTER_BRANCH_SQUELCH_WARNING = 1

# Define the message mappings
$messageMap = @{
    "Remove additional private Self-Healer and KnowledgeBase development files" = "Remove additional private development files"
    "Enhanced .gitignore to comprehensively exclude all private Self-Healer and KnowledgeBase components" = "Enhanced .gitignore to exclude private components"
    "Remove private Self-Healer and KnowledgeBase components from public repository" = "Remove private components from public repository"
    "update gitignore for self-healer and knowledgebase" = "update gitignore for private components"
    "Updates for Self-Healer Separation Finalization" = "Updates for system separation finalization"
    "Updates to KnowledgeBase Table structures and SP" = "Updates to database table structures and SP"
    "Updates to Self-Healer" = "Updates to system components"
    "Self-Healder - KnowledgeBase Fix" = "System component fixes"
    "Self-Healer & KnowledgeBase Updates 4" = "System component updates 4"
    "Self-Healer & KnowledgeBase Updates 3" = "System component updates 3"
    "Self-Healer & KnowledgeBse Integration Updates 2" = "System integration updates 2"
    "Self-Healer & KnowledgeBase Integration Updates" = "System integration updates"
    "KnowledgeBase Updates" = "Database component updates"
    "Update to Full version of Self-Healer" = "Update to full system version"
    "Fixes to Self-Healer and Documentation" = "Fixes to system and documentation"
    "Updates to Self-Healer / KnowledgeBase" = "Updates to system components"
    "Self-Healer Finalized and Integrated with KnowledgeBase - First Pass" = "System integration finalized - first pass"
    "Self Healer Updates" = "System component updates"
    "Self-Healer" = "System component implementation"
}

# Use a simpler approach with multiple filter-branch runs
Write-Host "🔄 Running git filter-branch for each message (this may take a while)..." -ForegroundColor Yellow

$successCount = 0
$totalCount = $messageMap.Count

foreach ($oldMsg in $messageMap.Keys) {
    $newMsg = $messageMap[$oldMsg]

    Write-Host "  Replacing: '$oldMsg'" -ForegroundColor Gray
    Write-Host "       With: '$newMsg'" -ForegroundColor Gray

    try {
        # Escape special characters for git filter-branch
        $escapedOld = $oldMsg -replace '"', '\"' -replace '&', '\&'
        $escapedNew = $newMsg -replace '"', '\"' -replace '&', '\&'

        # Run filter-branch for this specific message
        $filterCommand = "if [ `"`$1`" = `"$escapedOld`" ]; then echo `"$escapedNew`"; else echo `"`$1`"; fi"

        git filter-branch -f --msg-filter $filterCommand -- --all 2>$null

        if ($LASTEXITCODE -eq 0) {
            $successCount++
            Write-Host "    ✅ Success" -ForegroundColor Green
        } else {
            Write-Host "    ❌ Failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "    ❌ Exception: $_" -ForegroundColor Red
    }

    Write-Host ""
}

Write-Host "📊 Completed: $successCount/$totalCount messages updated" -ForegroundColor Cyan

Write-Host ""
Write-Host "🎉 Commit message cleanup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Review the changes: git log --oneline" -ForegroundColor White
Write-Host "2. If satisfied, force push: git push --force-with-lease" -ForegroundColor White
Write-Host "3. If you need to rollback: git reset --hard backup-before-cleanup-*" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Remember: You'll need to force push to update the remote repository!" -ForegroundColor Yellow
