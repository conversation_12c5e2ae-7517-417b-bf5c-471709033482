# ============================================================================
# Public Repository Verification Script
# ============================================================================
# Verifies that the public repository contains no private components
# ============================================================================

# Get project root (parent of Scripts folder)
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot  # Change working directory to project root
param(
    [string]$PublicRepoPath = "..\N8N_Builder_Community"
)

$ErrorCount = 0
$WarningCount = 0

function Write-Check {
    param([string]$Message, [string]$Status = "INFO")
    $color = switch ($Status) {
        "PASS" { "Green" }
        "FAIL" { "Red" }
        "WARN" { "Yellow" }
        default { "White" }
    }
    Write-Host "[$Status] $Message" -ForegroundColor $color
}

function Test-PrivateComponents {
    Write-Host "`n=== CHECKING FOR PRIVATE COMPONENTS ===" -ForegroundColor Cyan
    
    $privatePatterns = @(
        "Self-Healer*",
        "Self_Healer*", 
        "KnowledgeBase*",
        "*self_healer*",
        "*knowledgebase*",
        "*_private*",
        "*_advanced*",
        "debug_self_healer*",
        "healer_config*",
        "config_private*"
    )
    
    $found = $false
    foreach ($pattern in $privatePatterns) {
        $matches = Get-ChildItem -Path $PublicRepoPath -Recurse -Name -Include $pattern -ErrorAction SilentlyContinue
        if ($matches) {
            Write-Check "Found private component: $($matches -join ', ')" "FAIL"
            $script:ErrorCount++
            $found = $true
        }
    }
    
    if (-not $found) {
        Write-Check "No private components found" "PASS"
    }
}

function Test-PublicFiles {
    Write-Host "`n=== CHECKING PUBLIC FILE STRUCTURE ===" -ForegroundColor Cyan
    
    $requiredFiles = @(
        "README.md",
        "requirements.txt", 
        "run.py",
        "setup.py",
        "config.yaml",
        ".gitignore"
    )
    
    foreach ($file in $requiredFiles) {
        $filePath = Join-Path $PublicRepoPath $file
        if (Test-Path $filePath) {
            Write-Check "Required file exists: $file" "PASS"
        } else {
            Write-Check "Missing required file: $file" "FAIL"
            $script:ErrorCount++
        }
    }
}

function Test-PublicDirectories {
    Write-Host "`n=== CHECKING PUBLIC DIRECTORIES ===" -ForegroundColor Cyan
    
    $requiredDirs = @(
        "n8n-docker",
        "Documentation",
        "n8n_builder",
        "static"
    )
    
    foreach ($dir in $requiredDirs) {
        $dirPath = Join-Path $PublicRepoPath $dir
        if (Test-Path $dirPath) {
            Write-Check "Required directory exists: $dir" "PASS"
        } else {
            Write-Check "Missing required directory: $dir" "FAIL"
            $script:ErrorCount++
        }
    }
}

function Test-FileContents {
    Write-Host "`n=== CHECKING FILE CONTENTS FOR PRIVATE REFERENCES ===" -ForegroundColor Cyan
    
    $sensitiveTerms = @(
        "Self-Healer",
        "Self_Healer", 
        "KnowledgeBase",
        "healer_config",
        "config_private"
    )
    
    $textFiles = Get-ChildItem -Path $PublicRepoPath -Recurse -Include "*.md", "*.py", "*.txt", "*.yml", "*.yaml" -ErrorAction SilentlyContinue
    
    foreach ($file in $textFiles) {
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content) {
            foreach ($term in $sensitiveTerms) {
                if ($content -match $term) {
                    $relativePath = $file.FullName.Replace($PublicRepoPath, "").TrimStart('\')
                    Write-Check "Found private reference '$term' in: $relativePath" "WARN"
                    $script:WarningCount++
                }
            }
        }
    }
    
    if ($WarningCount -eq 0) {
        Write-Check "No private references found in file contents" "PASS"
    }
}

function Test-GitStatus {
    Write-Host "`n=== CHECKING GIT STATUS ===" -ForegroundColor Cyan
    
    Push-Location $PublicRepoPath
    try {
        $gitStatus = git status --porcelain 2>$null
        if ($gitStatus) {
            Write-Check "Repository has uncommitted changes" "WARN"
            $script:WarningCount++
        } else {
            Write-Check "Repository is clean (no uncommitted changes)" "PASS"
        }
        
        $remotes = git remote -v 2>$null
        if ($remotes) {
            Write-Check "Git remotes configured" "PASS"
            Write-Host "    $($remotes -join "`n    ")" -ForegroundColor Gray
        } else {
            Write-Check "No git remotes configured" "WARN"
            $script:WarningCount++
        }
    }
    finally {
        Pop-Location
    }
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

Write-Host "N8N_Builder Public Repository Verification" -ForegroundColor Cyan
Write-Host "Repository Path: $PublicRepoPath" -ForegroundColor Gray
Write-Host "=" * 60

if (-not (Test-Path $PublicRepoPath)) {
    Write-Check "Public repository path does not exist: $PublicRepoPath" "FAIL"
    exit 1
}

Test-PrivateComponents
Test-PublicFiles
Test-PublicDirectories
Test-FileContents
Test-GitStatus

Write-Host "`n" + "=" * 60
Write-Host "VERIFICATION SUMMARY" -ForegroundColor Cyan
Write-Host "Errors: $ErrorCount" -ForegroundColor $(if ($ErrorCount -eq 0) { "Green" } else { "Red" })
Write-Host "Warnings: $WarningCount" -ForegroundColor $(if ($WarningCount -eq 0) { "Green" } else { "Yellow" })

if ($ErrorCount -eq 0 -and $WarningCount -eq 0) {
    Write-Host "`n✅ PUBLIC REPOSITORY IS CLEAN AND READY FOR GITHUB!" -ForegroundColor Green
    exit 0
} elseif ($ErrorCount -eq 0) {
    Write-Host "`n⚠️  PUBLIC REPOSITORY IS MOSTLY CLEAN (WARNINGS ONLY)" -ForegroundColor Yellow
    exit 0
} else {
    Write-Host "`n❌ PUBLIC REPOSITORY HAS ISSUES THAT NEED TO BE FIXED" -ForegroundColor Red
    exit 1
}
