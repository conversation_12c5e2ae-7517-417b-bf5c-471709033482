# ============================================================================
# Enhanced Private Component Detection Script
# ============================================================================
# This script performs comprehensive scanning to detect ALL private component
# references that must be excluded from the public repository.
#
# USAGE: .\detect-private-components.ps1 [-Path "C:\path\to\scan"] [-OutputFile "report.json"] [-Verbose]
# ============================================================================

param(
    [string]$Path = ".",
    [string]$OutputFile = "private-component-detection-report.json",
    [switch]$Verbose = $false,
    [switch]$ExportMarkdown = $false
)

# Configuration
$ScanStartTime = Get-Date
$TotalFilesScanned = 0
$FilesWithReferences = 0
$TotalReferences = 0

# ============================================================================
# COMPREHENSIVE PRIVATE COMPONENT PATTERNS
# ============================================================================

# Direct component references
$PrivatePatterns = @{
    "Self-Healer" = @{
        "patterns" = @(
            "Self-Healer",
            "Self_Healer", 
            "SelfHealer",
            "self-healer",
            "self_healer",
            "selfhealer"
        )
        "description" = "Self-Healer component references"
    }
    "KnowledgeBase" = @{
        "patterns" = @(
            "KnowledgeBase",
            "Knowledge_Base",
            "knowledgebase",
            "knowledge_base",
            "knowledge-base"
        )
        "description" = "KnowledgeBase component references"
    }
    "Enterprise" = @{
        "patterns" = @(
            "_private",
            "_advanced",
            "Enterprise_Module",
            "enterprise_module",
            "advanced_systems",
            "proprietary"
        )
        "description" = "Enterprise/Advanced component references"
    }
    "Configuration" = @{
        "patterns" = @(
            "healer_config",
            "knowledgebase_config",
            "config_private",
            "advanced_config"
        )
        "description" = "Private configuration references"
    }
    "Database" = @{
        "patterns" = @(
            "S_SYS_SelfHealer",
            "SelfHealerKnowledgeDB",
            "knowledge_database_wrapper",
            "KnowledgeBaseIntegrator"
        )
        "description" = "Private database component references"
    }
    "Imports" = @{
        "patterns" = @(
            "from Self_Healer",
            "import.*Self_Healer",
            "from.*knowledge_database_wrapper",
            "import.*SelfHealerManager",
            "from.*healer_manager"
        )
        "description" = "Private component import statements"
    }
    "Files" = @{
        "patterns" = @(
            "debug_self_healer",
            "test_.*self_healer",
            "test_.*knowledgebase",
            "check_healer_status",
            "fix_healer_sync"
        )
        "description" = "Private component files"
    }
}

# File extensions to scan
$ScanExtensions = @(
    "*.py", "*.yaml", "*.yml", "*.json", "*.md", "*.txt", 
    "*.ps1", "*.bat", "*.sh", "*.js", "*.html", "*.css",
    "*.toml", "*.ini", "*.cfg", "*.conf"
)

# Directories to exclude from scanning
$ExcludeDirectories = @(
    "venv", "__pycache__", ".git", "node_modules", 
    "cache", "logs", "backups", "archive"
)

# ============================================================================
# LOGGING FUNCTIONS
# ============================================================================
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    if ($Verbose) { Write-Host $logEntry }
}

function Write-Success { param([string]$Message) Write-Log $Message "SUCCESS" }
function Write-Warning { param([string]$Message) Write-Log $Message "WARNING" }
function Write-Error { param([string]$Message) Write-Log $Message "ERROR" }

# ============================================================================
# DETECTION FUNCTIONS
# ============================================================================

function Test-ShouldExcludeDirectory {
    param([string]$DirectoryName)
    
    foreach ($exclude in $ExcludeDirectories) {
        if ($DirectoryName -like $exclude) {
            return $true
        }
    }
    return $false
}

function Get-FileMatches {
    param(
        [string]$FilePath,
        [hashtable]$Patterns
    )
    
    $matches = @()
    
    try {
        $content = Get-Content -Path $FilePath -Raw -ErrorAction SilentlyContinue
        if (-not $content) { return $matches }
        
        $lines = $content -split "`n"
        
        for ($lineNum = 0; $lineNum -lt $lines.Count; $lineNum++) {
            $line = $lines[$lineNum]
            
            foreach ($category in $Patterns.Keys) {
                foreach ($pattern in $Patterns[$category]["patterns"]) {
                    if ($line -match $pattern) {
                        $matches += @{
                            "line_number" = $lineNum + 1
                            "category" = $category
                            "pattern" = $pattern
                            "description" = $Patterns[$category]["description"]
                            "matched_text" = $matches[0].Value
                            "context" = $line.Trim()
                            "full_line" = if ($line.Length -gt 100) { $line.Substring(0, 100) + "..." } else { $line }
                        }
                    }
                }
            }
        }
    }
    catch {
        Write-Warning "Error scanning file $FilePath`: $($_.Exception.Message)"
    }
    
    return $matches
}

function Scan-Directory {
    param([string]$DirectoryPath)

    $results = @{}
    $scanPath = Resolve-Path $DirectoryPath

    Write-Log "Starting comprehensive scan of: $scanPath"

    # Get all files to scan
    $allFiles = @()
    foreach ($extension in $ScanExtensions) {
        $files = Get-ChildItem -Path $scanPath -Filter $extension -Recurse -File | Where-Object {
            $shouldExclude = $false
            $relativePath = $_.FullName.Replace($scanPath, "").TrimStart('\')

            foreach ($exclude in $ExcludeDirectories) {
                if ($relativePath -like "*$exclude*") {
                    $shouldExclude = $true
                    break
                }
            }

            return -not $shouldExclude
        }
        $allFiles += $files
    }

    Write-Log "Found $($allFiles.Count) files to scan"

    foreach ($file in $allFiles) {
        $script:TotalFilesScanned++
        $relativePath = $file.FullName.Replace($scanPath, "").TrimStart('\')

        if ($Verbose -and ($script:TotalFilesScanned % 50 -eq 0)) {
            Write-Log "Scanned $script:TotalFilesScanned files..."
        }

        $matches = Get-FileMatches -FilePath $file.FullName -Patterns $PrivatePatterns

        if ($matches.Count -gt 0) {
            $script:FilesWithReferences++
            $script:TotalReferences += $matches.Count
            $results[$relativePath] = @{
                "file_path" = $relativePath
                "full_path" = $file.FullName
                "matches" = $matches
                "match_count" = $matches.Count
            }

            Write-Warning "Found $($matches.Count) private references in: $relativePath"
        }
    }

    return $results
}

function Export-MarkdownReport {
    param(
        [hashtable]$Report,
        [string]$OutputFile
    )

    $markdown = "# Private Component Detection Report`n`n"
    $markdown += "## 📊 Scan Summary`n`n"
    $markdown += "- **Scan Time**: $($Report.scan_metadata.scan_time)`n"
    $markdown += "- **Scan Duration**: $($Report.scan_metadata.scan_duration_seconds) seconds`n"
    $markdown += "- **Files Scanned**: $($Report.scan_metadata.total_files_scanned)`n"
    $markdown += "- **Files with References**: $($Report.scan_metadata.files_with_references)`n"
    $markdown += "- **Total References**: $($Report.scan_metadata.total_references)`n`n"
    $markdown += "## 🔍 Detection Results`n`n"

    if ($Report.scan_metadata.files_with_references -eq 0) {
        $markdown += "✅ **No private component references detected!**`n`n"
        $markdown += "The repository appears ready for public sync.`n`n"
    } else {
        $markdown += "⚠️ **Private components detected!** These must be addressed before public sync.`n`n"
        $markdown += "### Files with Private References`n`n"

        foreach ($file in $Report.results.GetEnumerator() | Sort-Object { $_.Value.match_count } -Descending) {
            $markdown += "#### 📄 $($file.Key)`n"
            $markdown += "- **References Found**: $($file.Value.match_count)`n`n"

            foreach ($match in $file.Value.matches) {
                $markdown += "- **Line $($match.line_number)**: $($match.category) - ``$($match.pattern)```n"
                $markdown += "  - Context: ``$($match.context)```n"
            }
            $markdown += "`n"
        }
    }

    $markdown += "## 🔧 Detection Patterns Used`n`n"

    foreach ($category in $Report.detection_patterns.GetEnumerator()) {
        $markdown += "### $($category.Key)`n"
        $markdown += "- **Description**: $($category.Value.description)`n"
        $patternsJoined = $category.Value.patterns -join ', '
        $markdown += "- **Patterns**: $patternsJoined`n`n"
    }

    $markdown | Out-File -FilePath $OutputFile -Encoding UTF8
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

Write-Log "Enhanced Private Component Detection Starting..."
Write-Log "Scan Path: $Path"
Write-Log "Output File: $OutputFile"

# Perform the scan
$scanResults = Scan-Directory -DirectoryPath $Path

# Generate comprehensive report
$report = @{
    "scan_metadata" = @{
        "scan_time" = $ScanStartTime.ToString("yyyy-MM-dd HH:mm:ss")
        "scan_duration_seconds" = ((Get-Date) - $ScanStartTime).TotalSeconds
        "scan_path" = (Resolve-Path $Path).Path
        "total_files_scanned" = $script:TotalFilesScanned
        "files_with_references" = $script:FilesWithReferences
        "total_references" = $script:TotalReferences
    }
    "detection_patterns" = $PrivatePatterns
    "scan_configuration" = @{
        "extensions_scanned" = $ScanExtensions
        "excluded_directories" = $ExcludeDirectories
    }
    "results" = $scanResults
}

# Export JSON report
$report | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8
Write-Success "Detection report saved to: $OutputFile"

# Export Markdown report if requested
if ($ExportMarkdown) {
    $markdownFile = $OutputFile -replace '\.json$', '.md'
    Export-MarkdownReport -Report $report -OutputFile $markdownFile
    Write-Success "Markdown report saved to: $markdownFile"
}

# Display summary
Write-Log ""
Write-Log "=== DETECTION SUMMARY ==="
Write-Log "Files Scanned: $script:TotalFilesScanned"
Write-Log "Files with Private References: $script:FilesWithReferences"
Write-Log "Total Private References Found: $script:TotalReferences"
Write-Log "Scan Duration: $([math]::Round(((Get-Date) - $ScanStartTime).TotalSeconds, 2)) seconds"

if ($script:FilesWithReferences -gt 0) {
    Write-Warning ""
    Write-Warning "⚠️  PRIVATE COMPONENTS DETECTED!"
    Write-Warning "Review the report file for detailed findings."
    Write-Warning "These files must be cleaned before public repository sync."

    # Show top problematic files
    Write-Log ""
    Write-Log "=== TOP PROBLEMATIC FILES ==="
    $topFiles = $scanResults.GetEnumerator() | Sort-Object { $_.Value.match_count } -Descending | Select-Object -First 5
    foreach ($file in $topFiles) {
        Write-Warning "$($file.Value.match_count) references in: $($file.Key)"
    }
} else {
    Write-Success ""
    Write-Success "✅ No private component references detected!"
    Write-Success "Repository appears ready for public sync."
}

Write-Log "Detection complete. Report saved to: $OutputFile"
