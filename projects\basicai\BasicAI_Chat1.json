{"name": "Local LM Studio Chat Workflow", "nodes": [{"id": "webhookIn", "name": "Chat Request Webhook", "type": "n8n-nodes-base.webhook", "parameters": {"path": "/chat-request", "method": "POST", "active": true}, "position": [250, 300]}, {"id": "sendToLM", "name": "Send to LM Studio", "type": "n8n-nodes-base.httpRequest", "parameters": {"method": "POST", "url": "http://127.0.0.1:1234/generate", "headers": [{"key": "Content-Type", "value": "application/json"}], "body": {"message": "${inputData.message}"}, "active": true}, "position": [500, 350]}, {"id": "errorHandling", "name": "Erro<PERSON>", "type": "n8n-nodes-base.errorHandling", "parameters": {"emailNotifications": false, "logToConsole": true}, "position": [750, 350]}], "connections": {"webhookIn": {"main": [[{"node": "sendToLM", "type": "main", "index": 0}]]}, "sendToLM": {"main": [[{"node": "errorHandling", "type": "main", "index": 0}]]}}, "settings": {}, "active": true, "version": 1}