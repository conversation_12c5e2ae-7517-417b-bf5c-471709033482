# N8N Builder - Run with Virtual Environment
# This script ensures the system runs with the proper virtual environment

Write-Host "=" -ForegroundColor Cyan -NoNewline
Write-Host ("=" * 59) -ForegroundColor Cyan
Write-Host "N8N BUILDER - STARTING WITH VIRTUAL ENVIRONMENT" -ForegroundColor Cyan
Write-Host "=" -ForegroundColor Cyan -NoNewline
Write-Host ("=" * 59) -ForegroundColor Cyan

# Check if virtual environment exists
if (-not (Test-Path "venv\Scripts\python.exe")) {
    Write-Host "ERROR: Virtual environment not found!" -ForegroundColor Red
    Write-Host "Please run: python -m venv venv" -ForegroundColor Yellow
    Write-Host "Then run: .\venv\Scripts\python.exe -m pip install -r requirements.txt" -ForegroundColor Yellow
    exit 1
}

# Verify pyodbc is installed in venv
Write-Host "Checking virtual environment dependencies..." -ForegroundColor Yellow
try {
    $result = & ".\venv\Scripts\python.exe" -c "import pyodbc; print('OK')" 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: pyodbc not found in virtual environment!" -ForegroundColor Red
        Write-Host "Installing missing dependencies..." -ForegroundColor Yellow
        & ".\venv\Scripts\python.exe" -m pip install -r requirements.txt
        if ($LASTEXITCODE -ne 0) {
            Write-Host "ERROR: Failed to install dependencies!" -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "ERROR: Could not verify dependencies!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Virtual environment ready!" -ForegroundColor Green
Write-Host "🚀 Starting N8N Builder with virtual environment..." -ForegroundColor Green

# Run with virtual environment Python
& ".\venv\Scripts\python.exe" run.py

Write-Host "N8N Builder has stopped." -ForegroundColor Yellow
