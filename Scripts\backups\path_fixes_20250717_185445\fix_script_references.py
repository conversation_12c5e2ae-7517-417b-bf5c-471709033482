#!/usr/bin/env python3
"""
Script Reference Fixer
======================
Finds and fixes broken script references within the Scripts/ folder.
Specifically looks for Scripts/<script> references that should be just <script>
when called from within the Scripts/ folder itself.

Usage: python fix_script_references.py [--dry-run] [--execute]
"""

import os
import re
import argparse
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple, Set

class ScriptReferenceFixer:
    """Fixes script references within the Scripts folder."""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.scripts_dir = Path("Scripts")
        self.project_root = Path(".")
        self.fixes_log = []
        
        # File extensions to search
        self.search_extensions = {'.py', '.ps1', '.bat', '.sh'}
        
        # Common script reference patterns to look for
        self.reference_patterns = [
            r'Scripts[/\\]([a-zA-Z0-9_\-\.]+\.(?:py|ps1|bat|sh))',  # Scripts/script.py
            r'"Scripts[/\\]([a-zA-Z0-9_\-\.]+\.(?:py|ps1|bat|sh))"',  # "Scripts/script.py"
            r"'Scripts[/\\]([a-zA-Z0-9_\-\.]+\.(?:py|ps1|bat|sh))'",  # 'Scripts/script.py'
            r'python Scripts[/\\]([a-zA-Z0-9_\-\.]+\.py)',  # python Scripts/script.py
            r'powershell.*Scripts[/\\]([a-zA-Z0-9_\-\.]+\.ps1)',  # powershell Scripts/script.ps1
        ]
    
    def log_action(self, action: str, details: str = ""):
        """Log fix action."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {action}"
        if details:
            log_entry += f": {details}"
        
        self.fixes_log.append(log_entry)
        print(log_entry)
    
    def get_script_files(self) -> Set[str]:
        """Get list of actual script files in Scripts directory."""
        script_files = set()
        
        if self.scripts_dir.exists():
            for file_path in self.scripts_dir.iterdir():
                if file_path.is_file() and file_path.suffix in self.search_extensions:
                    script_files.add(file_path.name)
        
        return script_files
    
    def find_script_references_in_file(self, file_path: Path) -> List[Tuple[int, str, str, str]]:
        """Find script references in a file that need fixing."""
        references = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            script_files = self.get_script_files()
            
            for line_num, line in enumerate(lines, 1):
                original_line = line.rstrip()
                updated_line = original_line
                
                # Check each pattern
                for pattern in self.reference_patterns:
                    matches = re.finditer(pattern, line, re.IGNORECASE)
                    
                    for match in matches:
                        script_name = match.group(1)
                        
                        # Only fix if the script actually exists
                        if script_name in script_files:
                            # Replace Scripts/script.py with just script.py
                            full_match = match.group(0)
                            
                            # Determine the replacement based on the pattern
                            if full_match.startswith('python Scripts'):
                                replacement = f'python {script_name}'
                            elif full_match.startswith('powershell') and 'Scripts' in full_match:
                                replacement = full_match.replace(f'Scripts{os.sep}{script_name}', script_name).replace(f'Scripts/{script_name}', script_name)
                            elif full_match.startswith('"Scripts'):
                                replacement = f'"{script_name}"'
                            elif full_match.startswith("'Scripts"):
                                replacement = f"'{script_name}'"
                            else:
                                replacement = script_name
                            
                            updated_line = updated_line.replace(full_match, replacement)
                
                # If line was modified, add to references
                if updated_line != original_line:
                    references.append((line_num, original_line, updated_line, "script_reference_fix"))
        
        except Exception as e:
            self.log_action("ERROR", f"Failed to read {file_path}: {e}")
        
        return references
    
    def find_relative_path_issues(self, file_path: Path) -> List[Tuple[int, str, str, str]]:
        """Find relative path issues that might break when scripts are in Scripts/ folder."""
        references = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                original_line = line.rstrip()
                updated_line = original_line
                
                # Look for common relative path patterns that might be wrong
                patterns_to_check = [
                    (r'Path\("\."\)', 'Path("..")'),  # Python Path(".") should be Path("..")
                    (r'os\.getcwd\(\)', 'Path("..").resolve()'),  # getcwd() might need adjustment
                    (r'"\./"', '"../"'),  # "./" should be "../"
                    (r"'\./'", "'../'"),  # './' should be '../'
                ]
                
                for old_pattern, new_pattern in patterns_to_check:
                    if re.search(old_pattern, line):
                        # Only suggest this fix, don't auto-apply (too risky)
                        references.append((line_num, original_line, f"# REVIEW: Consider changing to {new_pattern}", "relative_path_review"))
        
        except Exception as e:
            self.log_action("ERROR", f"Failed to read {file_path}: {e}")
        
        return references
    
    def scan_scripts_folder(self) -> Dict[str, List[Tuple[int, str, str, str]]]:
        """Scan all scripts in Scripts folder for reference issues."""
        self.log_action("SCAN_START", "Searching for broken script references")
        
        file_references = {}
        total_files_scanned = 0
        
        if not self.scripts_dir.exists():
            self.log_action("ERROR", "Scripts directory does not exist")
            return {}
        
        for file_path in self.scripts_dir.iterdir():
            if not file_path.is_file():
                continue
            
            if file_path.suffix not in self.search_extensions:
                continue
            
            total_files_scanned += 1
            
            # Find script references
            script_refs = self.find_script_references_in_file(file_path)
            
            # Find relative path issues (for review only)
            path_refs = self.find_relative_path_issues(file_path)
            
            all_refs = script_refs + path_refs
            
            if all_refs:
                relative_path = str(file_path.relative_to(self.project_root))
                file_references[relative_path] = all_refs
        
        self.log_action("SCAN_COMPLETE", f"Scanned {total_files_scanned} script files, found issues in {len(file_references)} files")
        return file_references
    
    def display_references(self, file_references: Dict[str, List[Tuple[int, str, str, str]]]):
        """Display found references."""
        if not file_references:
            self.log_action("NO_ISSUES", "No script reference issues found")
            return
        
        print("\n" + "="*70)
        print("🔧 SCRIPT REFERENCE ANALYSIS")
        print("="*70)
        
        total_issues = sum(len(refs) for refs in file_references.values())
        print(f"\n📊 Found {total_issues} potential issues in {len(file_references)} files:")
        
        for file_path, references in file_references.items():
            print(f"\n📄 {file_path} ({len(references)} issues):")
            
            for line_num, old_line, new_line, issue_type in references:
                if issue_type == "script_reference_fix":
                    print(f"   Line {line_num}: {old_line[:60]}...")
                    if not self.dry_run:
                        print(f"   Fixed:     {new_line[:60]}...")
                    else:
                        print(f"   Would fix: {new_line[:60]}...")
                elif issue_type == "relative_path_review":
                    print(f"   Line {line_num}: {old_line[:60]}...")
                    print(f"   Review:    {new_line}")
        
        print("="*70)
    
    def apply_fixes(self, file_references: Dict[str, List[Tuple[int, str, str, str]]]) -> bool:
        """Apply the script reference fixes."""
        if not file_references:
            return True
        
        self.log_action("FIX_START", f"Applying fixes to {len(file_references)} files")
        
        success_count = 0
        for file_path, references in file_references.items():
            full_path = self.project_root / file_path
            
            # Only apply actual fixes, not reviews
            actual_fixes = [ref for ref in references if ref[3] == "script_reference_fix"]
            
            if not actual_fixes:
                continue
            
            try:
                # Read file
                with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Apply fixes
                updated_content = content
                for line_num, old_line, new_line, issue_type in actual_fixes:
                    if issue_type == "script_reference_fix":
                        updated_content = updated_content.replace(old_line, new_line)
                
                # Write back if not dry run
                if not self.dry_run:
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(updated_content)
                
                action = "WOULD_FIX" if self.dry_run else "FIXED"
                self.log_action(action, f"{file_path} ({len(actual_fixes)} fixes)")
                success_count += 1
                
            except Exception as e:
                self.log_action("ERROR", f"Failed to fix {file_path}: {e}")
        
        self.log_action("FIX_COMPLETE", f"Successfully processed {success_count} files")
        return True
    
    def run_fixes(self) -> bool:
        """Run complete script reference fixing process."""
        mode = "DRY RUN" if self.dry_run else "EXECUTION"
        self.log_action("START", f"Script reference fixing ({mode})")
        
        # Scan for issues
        file_references = self.scan_scripts_folder()
        
        # Display what was found
        self.display_references(file_references)
        
        # Apply fixes
        success = self.apply_fixes(file_references)
        
        if self.dry_run:
            self.log_action("DRY_RUN_COMPLETE", "Use --execute to apply actual fixes")
        else:
            status = "SUCCESS" if success else "PARTIAL_FAILURE"
            self.log_action(status, "Script reference fixing completed")
        
        return success
    
    def save_log(self, log_file: str = "script_reference_fixes.log"):
        """Save fixes log."""
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(self.fixes_log))
        print(f"📄 Log saved to: {log_file}")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Fix script references within Scripts folder")
    parser.add_argument("--dry-run", action="store_true", default=True, help="Show what would be fixed without executing")
    parser.add_argument("--execute", action="store_true", help="Actually execute the fixes")
    
    args = parser.parse_args()
    
    # If --execute is specified, turn off dry-run
    dry_run = not args.execute
    
    fixer = ScriptReferenceFixer(dry_run=dry_run)
    success = fixer.run_fixes()
    fixer.save_log()
    
    if dry_run:
        print("\n🔍 This was a DRY RUN. Use --execute to apply actual fixes.")
    
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
