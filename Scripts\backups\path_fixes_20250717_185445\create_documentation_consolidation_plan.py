#!/usr/bin/env python3
"""
Documentation Consolidation Planning Script for N8N_Builder Project

This script analyzes the documentation analysis report and creates a structured
consolidation plan to reduce documentation creep, eliminate redundancy, and
create a hierarchical documentation structure.

Author: N8N_Builder Development Team
Date: 2025-07-08
Purpose: Create actionable plan to consolidate documentation
"""

import json
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Tuple

class DocumentationConsolidationPlanner:
    def __init__(self, analysis_report_path: str):
        self.analysis_report_path = Path(analysis_report_path)
        self.consolidation_plan = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'issues_identified': [],
            'consolidation_groups': [],
            'obsolete_files': [],
            'hierarchy_plan': {},
            'action_items': []
        }
        
        # Define the desired hierarchical structure
        self.target_hierarchy = {
            'ROOT': {
                'files': ['README.md', 'GETTING_STARTED.md', 'FEATURES.md'],
                'purpose': 'High-level project overview and quick start'
            },
            'Documentation': {
                'files': ['Architecture.md', 'DesignPrinciples.md', 'DevelopersWorkflow.md'],
                'purpose': 'Core technical documentation',
                'subfolders': {
                    'guides': 'User guides and tutorials',
                    'technical': 'Detailed technical specifications',
                    'api': 'API documentation'
                }
            },
            'Scripts': {
                'files': ['README.md'],
                'purpose': 'Script documentation only'
            },
            'n8n-docker': {
                'files': ['README.md', 'SETUP.md'],
                'purpose': 'Docker environment documentation',
                'subfolders': {
                    'Documentation': 'Detailed docker documentation'
                }
            }
        }
    
    def analyze_redundancy_patterns(self, report_content: str) -> List[Dict]:
        """Identify redundancy patterns from the analysis report"""
        redundancy_issues = []
        
        # Extract redundancy section
        redundancy_match = re.search(
            r'## Potential Redundancy Analysis\n\n.*?\n\n(.*?)\n\n## Detailed File Analysis',
            report_content, re.DOTALL
        )
        
        if redundancy_match:
            redundancy_text = redundancy_match.group(1)
            
            # Parse redundant headers
            for line in redundancy_text.split('\n'):
                if 'appears' in line and 'times' in line:
                    match = re.match(r'- \*\*"([^"]+)"\*\* appears (\d+) times', line)
                    if match:
                        header = match.group(1)
                        count = int(match.group(2))
                        
                        if count >= 3:  # Focus on high-redundancy items
                            redundancy_issues.append({
                                'header': header,
                                'occurrences': count,
                                'severity': 'high' if count >= 5 else 'medium',
                                'consolidation_priority': 'immediate' if count >= 7 else 'high'
                            })
        
        return redundancy_issues
    
    def identify_file_categories(self, report_content: str) -> Dict[str, List[str]]:
        """Categorize files by their apparent purpose"""
        categories = {
            'setup_guides': [],
            'technical_specs': [],
            'troubleshooting': [],
            'api_docs': [],
            'project_specific': [],
            'obsolete_candidates': [],
            'duplicates': []
        }
        
        # Extract file information from detailed analysis
        file_sections = re.findall(
            r'#### ([^\n]+)\n\n- \*\*Path:\*\* `([^`]+)`.*?- \*\*Headers:\*\* (\d+).*?- \*\*Bullet Points:\*\* (\d+)',
            report_content, re.DOTALL
        )
        
        for filename, filepath, headers, bullets in file_sections:
            headers_count = int(headers)
            bullets_count = int(bullets)
            
            # Categorize based on filename and path patterns
            if any(keyword in filename.lower() for keyword in ['setup', 'getting', 'start', 'install']):
                categories['setup_guides'].append({
                    'file': filename,
                    'path': filepath,
                    'headers': headers_count,
                    'bullets': bullets_count
                })
            elif any(keyword in filename.lower() for keyword in ['architecture', 'technical', 'spec']):
                categories['technical_specs'].append({
                    'file': filename,
                    'path': filepath,
                    'headers': headers_count,
                    'bullets': bullets_count
                })
            elif any(keyword in filename.lower() for keyword in ['troubleshoot', 'debug', 'error', 'issue']):
                categories['troubleshooting'].append({
                    'file': filename,
                    'path': filepath,
                    'headers': headers_count,
                    'bullets': bullets_count
                })
            elif 'api' in filepath.lower() or 'api' in filename.lower():
                categories['api_docs'].append({
                    'file': filename,
                    'path': filepath,
                    'headers': headers_count,
                    'bullets': bullets_count
                })
            elif any(keyword in filepath.lower() for keyword in ['project', 'test-', 'elthosdb']):
                categories['project_specific'].append({
                    'file': filename,
                    'path': filepath,
                    'headers': headers_count,
                    'bullets': bullets_count
                })
            
            # Identify potential obsolete files (very small or very large)
            if headers_count < 2 and bullets_count < 5:
                categories['obsolete_candidates'].append({
                    'file': filename,
                    'path': filepath,
                    'reason': 'minimal_content',
                    'headers': headers_count,
                    'bullets': bullets_count
                })
            elif headers_count > 50 or bullets_count > 100:
                categories['obsolete_candidates'].append({
                    'file': filename,
                    'path': filepath,
                    'reason': 'excessive_content',
                    'headers': headers_count,
                    'bullets': bullets_count
                })
        
        return categories
    
    def create_consolidation_groups(self, categories: Dict) -> List[Dict]:
        """Create groups of files that should be consolidated"""
        consolidation_groups = []
        
        # Group 1: Setup and Getting Started
        if categories['setup_guides']:
            consolidation_groups.append({
                'group_name': 'Setup and Getting Started',
                'target_file': 'GETTING_STARTED.md',
                'target_location': 'ROOT',
                'files_to_merge': categories['setup_guides'],
                'action': 'consolidate',
                'priority': 'high',
                'rationale': 'Multiple setup guides create confusion for new users'
            })
        
        # Group 2: Technical Documentation
        if categories['technical_specs']:
            consolidation_groups.append({
                'group_name': 'Technical Documentation',
                'target_file': 'Documentation/Architecture.md',
                'target_location': 'Documentation',
                'files_to_merge': categories['technical_specs'],
                'action': 'consolidate',
                'priority': 'medium',
                'rationale': 'Technical specs should be centralized for developers'
            })
        
        # Group 3: Troubleshooting
        if categories['troubleshooting']:
            consolidation_groups.append({
                'group_name': 'Troubleshooting Guide',
                'target_file': 'Documentation/guides/Troubleshooting.md',
                'target_location': 'Documentation/guides',
                'files_to_merge': categories['troubleshooting'],
                'action': 'consolidate',
                'priority': 'medium',
                'rationale': 'Scattered troubleshooting info should be centralized'
            })
        
        # Group 4: Project-Specific Files
        if categories['project_specific']:
            consolidation_groups.append({
                'group_name': 'Project-Specific Documentation',
                'target_file': 'Various project folders',
                'target_location': 'projects/',
                'files_to_merge': categories['project_specific'],
                'action': 'relocate_or_remove',
                'priority': 'low',
                'rationale': 'Project-specific docs should not be in main documentation'
            })
        
        return consolidation_groups
    
    def generate_action_items(self, redundancy_issues: List, consolidation_groups: List, categories: Dict) -> List[Dict]:
        """Generate specific action items for documentation consolidation"""
        action_items = []
        
        # High-priority redundancy fixes
        for issue in redundancy_issues:
            if issue['consolidation_priority'] == 'immediate':
                action_items.append({
                    'task': f"Consolidate '{issue['header']}' sections",
                    'description': f"Header appears {issue['occurrences']} times across documents",
                    'priority': 'high',
                    'estimated_time': '30 minutes',
                    'type': 'redundancy_fix'
                })
        
        # Consolidation tasks
        for group in consolidation_groups:
            action_items.append({
                'task': f"Consolidate {group['group_name']}",
                'description': f"Merge {len(group['files_to_merge'])} files into {group['target_file']}",
                'priority': group['priority'],
                'estimated_time': f"{len(group['files_to_merge']) * 15} minutes",
                'type': 'consolidation',
                'files_involved': [f['path'] for f in group['files_to_merge']]
            })
        
        # Obsolete file cleanup
        for file_info in categories['obsolete_candidates']:
            action_items.append({
                'task': f"Review {file_info['file']} for removal",
                'description': f"File has {file_info['reason']} ({file_info['headers']} headers, {file_info['bullets']} bullets)",
                'priority': 'low',
                'estimated_time': '10 minutes',
                'type': 'cleanup',
                'file_path': file_info['path']
            })
        
        return action_items
    
    def create_consolidation_plan(self):
        """Create the complete consolidation plan"""
        try:
            with open(self.analysis_report_path, 'r', encoding='utf-8') as f:
                report_content = f.read()
        except Exception as e:
            print(f"Error reading analysis report: {e}")
            return
        
        # Analyze redundancy patterns
        redundancy_issues = self.analyze_redundancy_patterns(report_content)
        
        # Categorize files
        categories = self.identify_file_categories(report_content)
        
        # Create consolidation groups
        consolidation_groups = self.create_consolidation_groups(categories)
        
        # Generate action items
        action_items = self.generate_action_items(redundancy_issues, consolidation_groups, categories)
        
        # Build the plan
        self.consolidation_plan.update({
            'issues_identified': redundancy_issues,
            'file_categories': categories,
            'consolidation_groups': consolidation_groups,
            'action_items': sorted(action_items, key=lambda x: {'high': 3, 'medium': 2, 'low': 1}[x['priority']], reverse=True),
            'summary': {
                'total_redundancy_issues': len(redundancy_issues),
                'high_priority_issues': len([i for i in redundancy_issues if i['consolidation_priority'] == 'immediate']),
                'consolidation_groups': len(consolidation_groups),
                'total_action_items': len(action_items),
                'estimated_total_time': sum([int(item['estimated_time'].split()[0]) for item in action_items if item['estimated_time'].split()[0].isdigit()])
            }
        })
    
    def save_plan(self, output_file: str):
        """Save the consolidation plan to a file"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.consolidation_plan, f, indent=2, ensure_ascii=False)

def main():
    """Main execution function"""
    print("N8N_Builder Documentation Consolidation Planner")
    print("=" * 55)
    
    analysis_report = "data/documentation_analysis_report.md"
    output_file = "data/documentation_consolidation_plan.json"
    
    if not Path(analysis_report).exists():
        print(f"Error: Analysis report not found at {analysis_report}")
        print("Please run analyze_documentation.py first")
        return
    
    print(f"Reading analysis report: {analysis_report}")
    planner = DocumentationConsolidationPlanner(analysis_report)
    
    print("Creating consolidation plan...")
    planner.create_consolidation_plan()
    
    print(f"Saving plan to: {output_file}")
    planner.save_plan(output_file)
    
    # Print summary
    summary = planner.consolidation_plan['summary']
    print("\nConsolidation Plan Summary:")
    print(f"- Redundancy Issues Found: {summary['total_redundancy_issues']}")
    print(f"- High Priority Issues: {summary['high_priority_issues']}")
    print(f"- Consolidation Groups: {summary['consolidation_groups']}")
    print(f"- Total Action Items: {summary['total_action_items']}")
    print(f"- Estimated Total Time: {summary['estimated_total_time']} minutes")
    print(f"\nPlan saved to: {output_file}")

if __name__ == "__main__":
    main()
