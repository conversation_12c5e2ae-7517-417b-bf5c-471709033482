"""
Setup script for 24-hour log rotation system
Integrates with existing N8N Builder and Self-Healer logging
"""

import sys
import os
import logging
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.log_rotation_manager import LogRotationManager, setup_24hour_log_rotation


def setup_enhanced_logging_with_rotation():
    """Set up enhanced logging with 24-hour rotation for the entire system."""
    
    print("🔄 Setting up 24-hour log rotation system...")
    print("=" * 60)
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Initialize the log rotation manager
    rotation_manager = LogRotationManager(logs_dir)
    
    # Define comprehensive log configuration
    log_configs = {
        'main': {
            'filename': 'n8n_builder.log',
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
            'loggers': ['n8n_builder', 'n8n_builder.iteration', 'n8n_builder.performance']
        },
        'errors': {
            'filename': 'errors.log',
            'level': 'ERROR',
            'format': '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
            'loggers': ['']  # Root logger for all errors
        },
        'self_healer': {
            'filename': 'self_healer.log',
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'loggers': ['self_healer', 'Self_Healer']
        },
        'validation': {
            'filename': 'validation.log',
            'level': 'DEBUG',
            'format': '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
            'loggers': ['n8n_builder.validation', 'n8n_builder.diff']
        },
        'llm': {
            'filename': 'llm.log',
            'level': 'DEBUG',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'loggers': ['n8n_builder.llm']
        },
        'project': {
            'filename': 'project.log',
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'loggers': ['n8n_builder.project', 'n8n_builder.filesystem']
        },
        'retry': {
            'filename': 'retry.log',
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'loggers': ['n8n_builder.retry']
        },
        'mcp': {
            'filename': 'mcp.log',
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'loggers': ['n8n_builder.mcp_database_tool', 'n8n_builder.mcp_research_tool']
        }
    }
    
    # Set up rotation for each log configuration
    rotation_manager.setup_log_rotation(log_configs)
    
    # Attach handlers to existing loggers
    _attach_rotation_handlers_to_loggers(rotation_manager, log_configs)
    
    print("✅ Log rotation setup complete!")
    print(f"📁 Log directory: {logs_dir.absolute()}")
    print(f"🕛 Rotation time: Midnight (00:00)")
    print(f"📦 Backup count: 30 days")
    print(f"🗜️ Compression: Enabled")
    
    return rotation_manager


def _attach_rotation_handlers_to_loggers(rotation_manager: LogRotationManager, log_configs: dict):
    """Attach rotation handlers to existing loggers."""
    
    for log_name, config in log_configs.items():
        handler = rotation_manager.managed_handlers.get(log_name)
        if not handler:
            continue
        
        # Get the loggers for this configuration
        logger_names = config.get('loggers', [])
        
        for logger_name in logger_names:
            try:
                logger = logging.getLogger(logger_name)
                
                # Remove existing file handlers to avoid duplicates
                _remove_existing_file_handlers(logger)
                
                # Add the rotation handler
                logger.addHandler(handler)
                
                print(f"✅ Attached rotation handler to logger: {logger_name or 'root'}")
                
            except Exception as e:
                print(f"❌ Failed to attach handler to logger {logger_name}: {e}")


def _remove_existing_file_handlers(logger: logging.Logger):
    """Remove existing file handlers from a logger to avoid duplicates."""
    handlers_to_remove = []
    
    for handler in logger.handlers:
        if isinstance(handler, (logging.FileHandler, logging.handlers.RotatingFileHandler)):
            handlers_to_remove.append(handler)
    
    for handler in handlers_to_remove:
        logger.removeHandler(handler)
        handler.close()


def test_log_rotation():
    """Test the log rotation system."""
    print("\n🧪 Testing log rotation system...")
    print("-" * 40)
    
    # Get the rotation manager
    rotation_manager = LogRotationManager()
    
    # Test logging to different loggers
    test_loggers = [
        ('n8n_builder', 'INFO', 'Test message from N8N Builder'),
        ('self_healer', 'INFO', 'Test message from Self-Healer'),
        ('n8n_builder.validation', 'DEBUG', 'Test validation message'),
        ('n8n_builder.llm', 'DEBUG', 'Test LLM message'),
        ('', 'ERROR', 'Test error message to root logger')
    ]
    
    for logger_name, level, message in test_loggers:
        try:
            logger = logging.getLogger(logger_name)
            log_method = getattr(logger, level.lower())
            log_method(f"{message} - {datetime.now().isoformat()}")
            print(f"✅ Logged to {logger_name or 'root'}: {level}")
        except Exception as e:
            print(f"❌ Failed to log to {logger_name or 'root'}: {e}")
    
    # Get and display statistics
    stats = rotation_manager.get_log_statistics()
    
    print("\n📊 Log file statistics:")
    for log_name, stat_data in stats.items():
        if 'error' in stat_data:
            print(f"❌ {log_name}: {stat_data['error']}")
        else:
            size = stat_data.get('current_size_mb', 0)
            backups = stat_data.get('backup_files', 0)
            print(f"📄 {log_name}: {size}MB, {backups} backups")


def cleanup_old_logs(days_to_keep: int = 30):
    """Clean up old log files."""
    print(f"\n🧹 Cleaning up logs older than {days_to_keep} days...")
    
    rotation_manager = LogRotationManager()
    rotation_manager.cleanup_old_logs(days_to_keep)
    
    print("✅ Log cleanup complete!")


def show_log_status():
    """Show current log rotation status."""
    print("\n📊 Current Log Rotation Status")
    print("=" * 60)
    
    rotation_manager = LogRotationManager()
    stats = rotation_manager.get_log_statistics()
    
    total_size = 0
    total_backups = 0
    
    for log_name, stat_data in stats.items():
        if 'error' in stat_data:
            print(f"❌ {log_name}: {stat_data['error']}")
        else:
            size = stat_data.get('current_size_mb', 0)
            backups = stat_data.get('backup_files', 0)
            last_modified = stat_data.get('last_modified', 'Never')
            
            total_size += size
            total_backups += backups
            
            print(f"📄 {log_name}:")
            print(f"   Size: {size}MB")
            print(f"   Backups: {backups}")
            print(f"   Last Modified: {last_modified}")
            print()
    
    print(f"📊 Summary:")
    print(f"   Total current log size: {total_size:.2f}MB")
    print(f"   Total backup files: {total_backups}")
    print(f"   Rotation: 24-hour (midnight)")
    print(f"   Retention: 30 days")
    print(f"   Compression: Enabled")


def main():
    """Main entry point for log rotation setup."""
    import argparse
    
    parser = argparse.ArgumentParser(description="N8N Builder Log Rotation Setup")
    parser.add_argument('--setup', action='store_true', help='Set up log rotation')
    parser.add_argument('--test', action='store_true', help='Test log rotation')
    parser.add_argument('--status', action='store_true', help='Show log status')
    parser.add_argument('--cleanup', type=int, metavar='DAYS', help='Clean up logs older than DAYS')
    
    args = parser.parse_args()
    
    if args.setup:
        setup_enhanced_logging_with_rotation()
    elif args.test:
        test_log_rotation()
    elif args.status:
        show_log_status()
    elif args.cleanup:
        cleanup_old_logs(args.cleanup)
    else:
        # Default: setup and test
        setup_enhanced_logging_with_rotation()
        test_log_rotation()
        show_log_status()


if __name__ == "__main__":
    main()
