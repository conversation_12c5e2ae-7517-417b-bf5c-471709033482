#!/usr/bin/env python3
"""
Phase 3 Separation Execution Script
===================================
Execute the verified separation process with continuous monitoring and audit trail.

Usage: python execute_separation.py [--public-repo-path PATH] [--dry-run] [--verbose]
"""

import os
import json
import argparse
import subprocess
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class SeparationExecutor:
    """Execute verified separation with monitoring and audit trail."""
    
    def __init__(self, public_repo_path: str = "../N8N_Builder_Community", dry_run: bool = False, verbose: bool = False):
        self.public_repo_path = Path(public_repo_path)
        self.dry_run = dry_run
        self.verbose = verbose
        self.execution_start_time = datetime.now()
        
        # Create audit trail
        self.audit_trail = {
            "execution_metadata": {
                "start_time": self.execution_start_time.isoformat(),
                "public_repo_path": str(self.public_repo_path),
                "dry_run": dry_run,
                "stages_completed": [],
                "stages_failed": []
            },
            "stage_results": {},
            "files_processed": {},
            "verification_results": {}
        }
    
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        if self.verbose or level in ["WARNING", "ERROR", "SUCCESS"]:
            print(log_entry)
    
    def run_command(self, command: str, cwd: str = None, timeout: int = 300) -> Tuple[bool, str, str]:
        """Run shell command and return success status and output."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", f"Command timed out after {timeout} seconds"
        except Exception as e:
            return False, "", str(e)
    
    def stage_pre_execution_checks(self) -> Dict[str, Any]:
        """Stage 1: Pre-execution verification checks."""
        stage_name = "pre_execution_checks"
        self.log("=== STAGE 1: PRE-EXECUTION CHECKS ===")
        
        result = {
            "stage_name": stage_name,
            "success": True,
            "checks_performed": [],
            "issues_found": []
        }
        
        # Check critical files exist
        critical_files = [
            "sync-public.ps1",
            "detect_private_components.py",
            "README_community.md",
            "requirements_public.txt",
            "run_public.py"
        ]
        
        for file_path in critical_files:
            exists = Path(file_path).exists()
            result["checks_performed"].append(f"{file_path}: {'EXISTS' if exists else 'MISSING'}")
            if not exists:
                result["success"] = False
                result["issues_found"].append(f"Missing critical file: {file_path}")
        
        # Check directories
        critical_dirs = ["n8n_builder", "Documentation", "Scripts"]
        for dir_path in critical_dirs:
            exists = Path(dir_path).exists()
            result["checks_performed"].append(f"{dir_path}/: {'EXISTS' if exists else 'MISSING'}")
            if not exists:
                result["success"] = False
                result["issues_found"].append(f"Missing critical directory: {dir_path}")
        
        if result["success"]:
            self.log("✅ Pre-execution checks passed")
        else:
            self.log(f"❌ Pre-execution checks failed: {len(result['issues_found'])} issues", "ERROR")
        
        return result
    
    def stage_run_detection(self) -> Dict[str, Any]:
        """Stage 2: Run private component detection."""
        stage_name = "run_detection"
        self.log("=== STAGE 2: PRIVATE COMPONENT DETECTION ===")
        
        result = {
            "stage_name": stage_name,
            "success": False,
            "detection_results": {},
            "references_found": 0
        }
        
        # Run detection script
        detection_cmd = "python detect_private_components.py --path . --output separation_detection.json --markdown"
        self.log(f"Running: {detection_cmd}")
        
        success, stdout, stderr = self.run_command(detection_cmd)
        
        if success and Path("separation_detection.json").exists():
            try:
                with open("separation_detection.json", 'r') as f:
                    detection_data = json.load(f)
                
                result["detection_results"] = detection_data
                result["references_found"] = detection_data["scan_metadata"]["total_references"]
                
                if result["references_found"] == 0:
                    result["success"] = True
                    self.log("✅ No private component references detected")
                else:
                    self.log(f"⚠️ Found {result['references_found']} private references", "WARNING")
                    self.log("Review separation_detection.json for details", "WARNING")
                    # Don't fail - just warn
                    result["success"] = True
            
            except json.JSONDecodeError:
                self.log("❌ Detection results file has invalid JSON", "ERROR")
        else:
            self.log(f"❌ Detection script failed: {stderr}", "ERROR")
        
        return result
    
    def stage_execute_sync(self) -> Dict[str, Any]:
        """Stage 3: Execute enhanced sync process."""
        stage_name = "execute_sync"
        self.log("=== STAGE 3: EXECUTE ENHANCED SYNC ===")
        
        result = {
            "stage_name": stage_name,
            "success": False,
            "sync_output": "",
            "sync_errors": ""
        }
        
        # Prepare sync command
        sync_cmd = f"powershell -ExecutionPolicy Bypass -File Scripts\\sync-public.ps1 -PublicRepoPath \"{self.public_repo_path}\" -RunVerification"
        
        if self.dry_run:
            sync_cmd += " -DryRun"
        
        self.log(f"Running sync: {sync_cmd}")
        
        # Execute sync
        success, stdout, stderr = self.run_command(sync_cmd, timeout=600)  # 10 minute timeout
        
        result["sync_output"] = stdout
        result["sync_errors"] = stderr
        result["success"] = success
        
        if success:
            self.log("✅ Enhanced sync completed successfully")
            if not self.dry_run:
                self.log(f"📁 Public repository created at: {self.public_repo_path}")
        else:
            self.log(f"❌ Enhanced sync failed: {stderr}", "ERROR")
        
        return result
    
    def stage_post_sync_verification(self) -> Dict[str, Any]:
        """Stage 4: Post-sync verification."""
        stage_name = "post_sync_verification"
        self.log("=== STAGE 4: POST-SYNC VERIFICATION ===")
        
        result = {
            "stage_name": stage_name,
            "success": False,
            "verification_results": {},
            "security_check_passed": False
        }
        
        if self.dry_run:
            result["success"] = True
            result["skipped"] = "Skipped in dry run mode"
            self.log("ℹ️ Post-sync verification skipped in dry run mode")
            return result
        
        if not self.public_repo_path.exists():
            self.log("❌ Public repository path does not exist", "ERROR")
            return result
        
        # Run detection on public repository
        detection_cmd = f"python detect_private_components.py --path \"{self.public_repo_path}\" --output post_sync_detection.json --markdown"
        self.log(f"Running post-sync detection: {detection_cmd}")
        
        success, stdout, stderr = self.run_command(detection_cmd)
        
        if success and Path("post_sync_detection.json").exists():
            try:
                with open("post_sync_detection.json", 'r') as f:
                    detection_data = json.load(f)
                
                result["verification_results"] = detection_data
                references_found = detection_data["scan_metadata"]["total_references"]
                
                if references_found == 0:
                    result["success"] = True
                    result["security_check_passed"] = True
                    self.log("✅ Post-sync verification passed - no private components in public repo")
                else:
                    self.log(f"❌ SECURITY ISSUE: Found {references_found} private references in public repo!", "ERROR")
                    self.log("Review post_sync_detection.json immediately", "ERROR")
            
            except json.JSONDecodeError:
                self.log("❌ Post-sync detection results have invalid JSON", "ERROR")
        else:
            self.log(f"❌ Post-sync detection failed: {stderr}", "ERROR")
        
        return result
    
    def stage_functionality_test(self) -> Dict[str, Any]:
        """Stage 5: Test Community Edition functionality."""
        stage_name = "functionality_test"
        self.log("=== STAGE 5: FUNCTIONALITY TESTING ===")
        
        result = {
            "stage_name": stage_name,
            "success": True,
            "tests_run": [],
            "tests_passed": [],
            "tests_failed": []
        }
        
        if self.dry_run:
            result["skipped"] = "Skipped in dry run mode"
            self.log("ℹ️ Functionality testing skipped in dry run mode")
            return result
        
        if not self.public_repo_path.exists():
            self.log("❌ Public repository path does not exist", "ERROR")
            result["success"] = False
            return result
        
        # Test basic file structure
        test_name = "file_structure"
        result["tests_run"].append(test_name)
        
        required_files = ["README.md", "requirements.txt", "run.py"]
        missing_files = []
        
        for file_name in required_files:
            file_path = self.public_repo_path / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            result["tests_failed"].append(test_name)
            result["success"] = False
            self.log(f"❌ File structure test failed: missing {missing_files}", "ERROR")
        else:
            result["tests_passed"].append(test_name)
            self.log("✅ File structure test passed")
        
        # Test Python syntax
        test_name = "python_syntax"
        result["tests_run"].append(test_name)
        
        run_py = self.public_repo_path / "run.py"
        if run_py.exists():
            syntax_cmd = f"python -m py_compile \"{run_py}\""
            success, stdout, stderr = self.run_command(syntax_cmd)
            
            if success:
                result["tests_passed"].append(test_name)
                self.log("✅ Python syntax test passed")
            else:
                result["tests_failed"].append(test_name)
                result["success"] = False
                self.log(f"❌ Python syntax test failed: {stderr}", "ERROR")
        else:
            result["tests_failed"].append(test_name)
            result["success"] = False
        
        return result
    
    def stage_generate_audit_trail(self) -> Dict[str, Any]:
        """Stage 6: Generate comprehensive audit trail."""
        stage_name = "generate_audit_trail"
        self.log("=== STAGE 6: GENERATE AUDIT TRAIL ===")
        
        result = {
            "stage_name": stage_name,
            "success": True,
            "audit_files_created": []
        }
        
        # Update final audit trail metadata
        self.audit_trail["execution_metadata"]["end_time"] = datetime.now().isoformat()
        self.audit_trail["execution_metadata"]["duration_seconds"] = (datetime.now() - self.execution_start_time).total_seconds()
        
        # Export audit trail
        audit_file = f"separation_audit_trail_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(audit_file, 'w') as f:
            json.dump(self.audit_trail, f, indent=2)
        
        result["audit_files_created"].append(audit_file)
        self.log(f"📄 Audit trail saved to: {audit_file}")
        
        # Create summary report
        summary_file = f"separation_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        self.create_summary_report(summary_file)
        result["audit_files_created"].append(summary_file)
        
        return result
    
    def create_summary_report(self, filename: str):
        """Create human-readable summary report."""
        completed_stages = len(self.audit_trail["execution_metadata"]["stages_completed"])
        failed_stages = len(self.audit_trail["execution_metadata"]["stages_failed"])
        total_stages = completed_stages + failed_stages
        
        summary = f"""# Phase 3 Separation Execution Summary

## Execution Overview
- **Start Time**: {self.audit_trail["execution_metadata"]["start_time"]}
- **End Time**: {self.audit_trail["execution_metadata"]["end_time"]}
- **Duration**: {self.audit_trail["execution_metadata"]["duration_seconds"]:.2f} seconds
- **Public Repository Path**: {self.audit_trail["execution_metadata"]["public_repo_path"]}
- **Dry Run**: {self.audit_trail["execution_metadata"]["dry_run"]}

## Stage Results
- **Total Stages**: {total_stages}
- **Completed Successfully**: {completed_stages}
- **Failed**: {failed_stages}
- **Success Rate**: {(completed_stages/total_stages*100):.1f}% (if total_stages > 0 else 'N/A')

## Completed Stages
"""
        
        for stage in self.audit_trail["execution_metadata"]["stages_completed"]:
            summary += f"- ✅ {stage}\n"
        
        if self.audit_trail["execution_metadata"]["stages_failed"]:
            summary += "\n## Failed Stages\n"
            for stage in self.audit_trail["execution_metadata"]["stages_failed"]:
                summary += f"- ❌ {stage}\n"
        
        summary += f"""
## Overall Status
{"✅ SEPARATION EXECUTION SUCCESSFUL" if failed_stages == 0 else "❌ SEPARATION EXECUTION FAILED"}

{"🚀 Public repository is ready for GitHub publication!" if failed_stages == 0 else "🔧 Address failed stages before proceeding."}
"""
        
        with open(filename, 'w') as f:
            f.write(summary)
        
        self.log(f"📄 Summary report saved to: {filename}")
    
    def execute_separation(self) -> Dict[str, Any]:
        """Execute the complete separation process."""
        self.log("🚀 Starting Phase 3 Separation Execution")
        self.log(f"Target: {self.public_repo_path}")
        self.log(f"Dry Run: {self.dry_run}")
        
        stages = [
            self.stage_pre_execution_checks,
            self.stage_run_detection,
            self.stage_execute_sync,
            self.stage_post_sync_verification,
            self.stage_functionality_test,
            self.stage_generate_audit_trail
        ]
        
        overall_success = True
        
        for stage_method in stages:
            try:
                stage_result = stage_method()
                stage_name = stage_result["stage_name"]
                
                self.audit_trail["stage_results"][stage_name] = stage_result
                
                if stage_result["success"]:
                    self.audit_trail["execution_metadata"]["stages_completed"].append(stage_name)
                    self.log(f"✅ Stage '{stage_name}' completed successfully", "SUCCESS")
                else:
                    self.audit_trail["execution_metadata"]["stages_failed"].append(stage_name)
                    self.log(f"❌ Stage '{stage_name}' failed", "ERROR")
                    overall_success = False
                    
                    # Stop on critical failures
                    if stage_name in ["execute_sync", "post_sync_verification"]:
                        self.log("🛑 Critical stage failed - stopping execution", "ERROR")
                        break
            
            except Exception as e:
                self.log(f"❌ Stage crashed: {stage_method.__name__} - {e}", "ERROR")
                self.audit_trail["execution_metadata"]["stages_failed"].append(stage_method.__name__)
                overall_success = False
                break
        
        # Display final summary
        self.display_execution_summary(overall_success)
        
        return self.audit_trail
    
    def display_execution_summary(self, success: bool):
        """Display execution summary."""
        print("\n" + "="*70)
        print("🚀 PHASE 3 SEPARATION EXECUTION SUMMARY")
        print("="*70)
        
        metadata = self.audit_trail["execution_metadata"]
        completed = len(metadata["stages_completed"])
        failed = len(metadata["stages_failed"])
        
        print(f"⏱️  Duration: {metadata.get('duration_seconds', 0):.2f} seconds")
        print(f"✅ Completed Stages: {completed}")
        print(f"❌ Failed Stages: {failed}")
        
        if success:
            print("\n🎉 SEPARATION EXECUTION SUCCESSFUL!")
            print("✅ All stages completed successfully")
            if not self.dry_run:
                print("🚀 Public repository is ready for GitHub publication!")
                print(f"📁 Location: {self.public_repo_path}")
            else:
                print("ℹ️ This was a dry run - use without --dry-run to execute")
        else:
            print("\n⚠️ SEPARATION EXECUTION FAILED!")
            print("❌ One or more stages failed")
            print("🔧 Review stage results and address issues")
        
        print("="*70)

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Phase 3 Separation Execution")
    parser.add_argument("--public-repo-path", default="../N8N_Builder_Community", help="Public repository path")
    parser.add_argument("--dry-run", action="store_true", help="Dry run mode")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Initialize executor
    executor = SeparationExecutor(args.public_repo_path, args.dry_run, args.verbose)
    
    # Execute separation
    results = executor.execute_separation()
    
    # Exit with appropriate code
    if len(results["execution_metadata"]["stages_failed"]) == 0:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
