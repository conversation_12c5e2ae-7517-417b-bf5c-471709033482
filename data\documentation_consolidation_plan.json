{"analysis_date": "2025-07-07 21:36:19", "issues_identified": [{"header": "readme.md", "occurrences": 15, "severity": "high", "consolidation_priority": "immediate"}, {"header": "prerequisites", "occurrences": 7, "severity": "high", "consolidation_priority": "immediate"}, {"header": "🎯 overview", "occurrences": 7, "severity": "high", "consolidation_priority": "immediate"}, {"header": "stop everything", "occurrences": 7, "severity": "high", "consolidation_priority": "immediate"}, {"header": "common issues", "occurrences": 7, "severity": "high", "consolidation_priority": "immediate"}, {"header": "next steps", "occurrences": 6, "severity": "high", "consolidation_priority": "high"}, {"header": "troubleshooting", "occurrences": 6, "severity": "high", "consolidation_priority": "high"}, {"header": "🚀 quick start", "occurrences": 6, "severity": "high", "consolidation_priority": "high"}, {"header": "license.md", "occurrences": 6, "severity": "high", "consolidation_priority": "high"}, {"header": "linux/mac", "occurrences": 5, "severity": "high", "consolidation_priority": "high"}, {"header": "🏗️ how it works", "occurrences": 5, "severity": "high", "consolidation_priority": "high"}, {"header": "configuration", "occurrences": 5, "severity": "high", "consolidation_priority": "high"}, {"header": "core components", "occurrences": 5, "severity": "high", "consolidation_priority": "high"}, {"header": "overview", "occurrences": 5, "severity": "high", "consolidation_priority": "high"}, {"header": "windows", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "📚 documentation", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "📋 overview", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "🆘 getting help", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "configuration files", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "or", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "check container status", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "📚 related documentation", "occurrences": 4, "severity": "medium", "consolidation_priority": "high"}, {"header": "🚀 quick start (choose your speed)", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "🚀 getting started", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "🤝 contributing", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "architecture.md", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "🔒 security considerations", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "📅 completion date: 2025-07-05", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "📊 **system architecture overview**", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "🔧 **technical specifications**", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "**quality assurance**", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "**professional standards**", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "system health check", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "before asking for help", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "usage", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "📚 documentation navigation", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "data flow architecture", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "best practices", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "🔍 troubleshooting", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "navigate to n8n-docker directory first", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "view n8n logs", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "🐳 docker issues", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "common issues:", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "navigate to n8n-docker directory", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "security considerations", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "project information", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "workflows", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "getting started", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "project structure", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "file naming conventions", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "iteration history", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "performance monitoring", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "healing action tracking", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "safety measures", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "monitoring and metrics", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "key metrics", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "future enhancements", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}, {"header": "planned features", "occurrences": 3, "severity": "medium", "consolidation_priority": "high"}], "consolidation_groups": [{"group_name": "Setup and Getting Started", "target_file": "GETTING_STARTED.md", "target_location": "ROOT", "files_to_merge": [{"file": "GETTING_STARTED.md", "path": "GETTING_STARTED.md", "headers": 58, "bullets": 30}, {"file": "LIGHTNING_START.md", "path": "LIGHTNING_START.md", "headers": 9, "bullets": 9}, {"file": "GITHUB_SETUP_INSTRUCTIONS.md", "path": "Documentation\\GITHUB_SETUP_INSTRUCTIONS.md", "headers": 23, "bullets": 23}, {"file": "SERVER_STARTUP_METHODS.md", "path": "Documentation\\SERVER_STARTUP_METHODS.md", "headers": 44, "bullets": 34}, {"file": "INTEGRATION_SETUP.md", "path": "Documentation\\guides\\INTEGRATION_SETUP.md", "headers": 37, "bullets": 53}, {"file": "PYTHON_ENVIRONMENT_SETUP.md", "path": "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md", "headers": 54, "bullets": 21}, {"file": "GETTING_STARTED.md", "path": "n8n-docker\\GETTING_STARTED.md", "headers": 32, "bullets": 16}, {"file": "LIGHTNING_START.md", "path": "n8n-docker\\LIGHTNING_START.md", "headers": 11, "bullets": 10}, {"file": "QUICK_START.md", "path": "n8n-docker\\Documentation\\QUICK_START.md", "headers": 58, "bullets": 48}, {"file": "AUTOMATION_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\AUTOMATION_SETUP.md", "headers": 84, "bullets": 24}, {"file": "CREDENTIALS_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\CREDENTIALS_SETUP.md", "headers": 57, "bullets": 144}, {"file": "SECURITY_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\SECURITY_SETUP.md", "headers": 47, "bullets": 35}, {"file": "DOCKER_SETUP.md", "path": "n8n-docker\\Documentation\\technical\\DOCKER_SETUP.md", "headers": 59, "bullets": 34}, {"file": "ZROK_SETUP_GUIDE.md", "path": "n8n-docker\\legacy-tunneling\\ZROK_SETUP_GUIDE.md", "headers": 51, "bullets": 29}], "action": "consolidate", "priority": "high", "rationale": "Multiple setup guides create confusion for new users"}, {"group_name": "Technical Documentation", "target_file": "Documentation/Architecture.md", "target_location": "Documentation", "files_to_merge": [{"file": "ARCHITECTURE.md", "path": "Documentation\\ARCHITECTURE.md", "headers": 24, "bullets": 43}, {"file": "ARCHITECTURE.md", "path": "Self_Healer\\ARCHITECTURE.md", "headers": 46, "bullets": 131}, {"file": "ARCHITECTURE.md", "path": "Self_Healer\\Documentation\\ARCHITECTURE.md", "headers": 53, "bullets": 134}], "action": "consolidate", "priority": "medium", "rationale": "Technical specs should be centralized for developers"}, {"group_name": "Troubleshooting Guide", "target_file": "Documentation/guides/Troubleshooting.md", "target_location": "Documentation/guides", "files_to_merge": [{"file": "TROUBLESHOOTING.md", "path": "Documentation\\TROUBLESHOOTING.md", "headers": 47, "bullets": 47}, {"file": "TROUBLESHOOTING.md", "path": "n8n-docker\\Documentation\\technical\\TROUBLESHOOTING.md", "headers": 65, "bullets": 47}], "action": "consolidate", "priority": "medium", "rationale": "Scattered troubleshooting info should be centralized"}, {"group_name": "Project-Specific Documentation", "target_file": "Various project folders", "target_location": "projects/", "files_to_merge": [{"file": "safe_project_analysis_20250627_232355.md", "path": "data\\safe_project_analysis_20250627_232355.md", "headers": 2, "bullets": 3}, {"file": "safe_project_analysis_20250628_014002.md", "path": "data\\safe_project_analysis_20250628_014002.md", "headers": 2, "bullets": 3}, {"file": "README.md", "path": "projects\\README.md", "headers": 10, "bullets": 10}, {"file": "README.md", "path": "projects\\elthosdb1\\README.md", "headers": 7, "bullets": 6}, {"file": "README.md", "path": "projects\\test-1\\README.md", "headers": 7, "bullets": 6}, {"file": "README.md", "path": "projects\\test-project\\README.md", "headers": 7, "bullets": 6}], "action": "relocate_or_remove", "priority": "low", "rationale": "Project-specific docs should not be in main documentation"}], "obsolete_files": [], "hierarchy_plan": {}, "action_items": [{"task": "Consolidate 'readme.md' sections", "description": "Header appears 15 times across documents", "priority": "high", "estimated_time": "30 minutes", "type": "redundancy_fix"}, {"task": "Consolidate 'prerequisites' sections", "description": "Header appears 7 times across documents", "priority": "high", "estimated_time": "30 minutes", "type": "redundancy_fix"}, {"task": "Consolidate '🎯 overview' sections", "description": "Header appears 7 times across documents", "priority": "high", "estimated_time": "30 minutes", "type": "redundancy_fix"}, {"task": "Consolidate 'stop everything' sections", "description": "Header appears 7 times across documents", "priority": "high", "estimated_time": "30 minutes", "type": "redundancy_fix"}, {"task": "Consolidate 'common issues' sections", "description": "Header appears 7 times across documents", "priority": "high", "estimated_time": "30 minutes", "type": "redundancy_fix"}, {"task": "Consolidate Setup and Getting Started", "description": "Merge 14 files into GETTING_STARTED.md", "priority": "high", "estimated_time": "210 minutes", "type": "consolidation", "files_involved": ["GETTING_STARTED.md", "LIGHTNING_START.md", "Documentation\\GITHUB_SETUP_INSTRUCTIONS.md", "Documentation\\SERVER_STARTUP_METHODS.md", "Documentation\\guides\\INTEGRATION_SETUP.md", "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md", "n8n-docker\\GETTING_STARTED.md", "n8n-docker\\LIGHTNING_START.md", "n8n-docker\\Documentation\\QUICK_START.md", "n8n-docker\\Documentation\\guides\\AUTOMATION_SETUP.md", "n8n-docker\\Documentation\\guides\\CREDENTIALS_SETUP.md", "n8n-docker\\Documentation\\guides\\SECURITY_SETUP.md", "n8n-docker\\Documentation\\technical\\DOCKER_SETUP.md", "n8n-docker\\legacy-tunneling\\ZROK_SETUP_GUIDE.md"]}, {"task": "Consolidate Technical Documentation", "description": "Merge 3 files into Documentation/Architecture.md", "priority": "medium", "estimated_time": "45 minutes", "type": "consolidation", "files_involved": ["Documentation\\ARCHITECTURE.md", "Self_Healer\\ARCHITECTURE.md", "Self_Healer\\Documentation\\ARCHITECTURE.md"]}, {"task": "Consolidate Troubleshooting Guide", "description": "Merge 2 files into Documentation/guides/Troubleshooting.md", "priority": "medium", "estimated_time": "30 minutes", "type": "consolidation", "files_involved": ["Documentation\\TROUBLESHOOTING.md", "n8n-docker\\Documentation\\technical\\TROUBLESHOOTING.md"]}, {"task": "Consolidate Project-Specific Documentation", "description": "Merge 6 files into Various project folders", "priority": "low", "estimated_time": "90 minutes", "type": "consolidation", "files_involved": ["data\\safe_project_analysis_20250627_232355.md", "data\\safe_project_analysis_20250628_014002.md", "projects\\README.md", "projects\\elthosdb1\\README.md", "projects\\test-1\\README.md", "projects\\test-project\\README.md"]}, {"task": "Review GETTING_STARTED.md for removal", "description": "File has excessive_content (58 headers, 30 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "GETTING_STARTED.md"}, {"task": "Review separation_detection.md for removal", "description": "File has excessive_content (138 headers, 333553 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "separation_detection.md"}, {"task": "Review GITHUB_ORGANIZATION_TASKS.md for removal", "description": "File has excessive_content (28 headers, 128 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Documentation\\GITHUB_ORGANIZATION_TASKS.md"}, {"task": "Review API_DOCUMENTATION.md for removal", "description": "File has excessive_content (71 headers, 80 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Documentation\\api\\API_DOCUMENTATION.md"}, {"task": "Review API_QUICK_REFERENCE.md for removal", "description": "File has excessive_content (60 headers, 64 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Documentation\\api\\API_QUICK_REFERENCE.md"}, {"task": "Review DOCUMENTATION.md for removal", "description": "File has excessive_content (105 headers, 142 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Documentation\\technical\\DOCUMENTATION.md"}, {"task": "Review PYTHON_ENVIRONMENT_SETUP.md for removal", "description": "File has excessive_content (54 headers, 21 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md"}, {"task": "Review ProcessFlow.md for removal", "description": "File has excessive_content (132 headers, 4275 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Documentation\\technical\\ProcessFlow.md"}, {"task": "Review ARCHITECTURE.md for removal", "description": "File has excessive_content (46 headers, 131 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Self_Healer\\ARCHITECTURE.md"}, {"task": "Review README.md for removal", "description": "File has excessive_content (60 headers, 106 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Self_Healer\\README.md"}, {"task": "Review ARCHITECTURE.md for removal", "description": "File has excessive_content (53 headers, 134 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Self_Healer\\Documentation\\ARCHITECTURE.md"}, {"task": "Review INTEGRATION_GUIDE.md for removal", "description": "File has excessive_content (96 headers, 33 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Self_Healer\\Documentation\\INTEGRATION_GUIDE.md"}, {"task": "Review SQLConventions.md for removal", "description": "File has excessive_content (67 headers, 124 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "Self_Healer\\Documentation\\SQLConventions.md"}, {"task": "Review documentation_analysis_report.md for removal", "description": "File has excessive_content (114 headers, 3819 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "data\\documentation_analysis_report.md"}, {"task": "Review QUICK_START.md for removal", "description": "File has excessive_content (58 headers, 48 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\QUICK_START.md"}, {"task": "Review README_OLD.md for removal", "description": "File has excessive_content (76 headers, 50 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\README_OLD.md"}, {"task": "Review AUTOMATION_SETUP.md for removal", "description": "File has excessive_content (84 headers, 24 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\guides\\AUTOMATION_SETUP.md"}, {"task": "Review CREDENTIALS_SETUP.md for removal", "description": "File has excessive_content (57 headers, 144 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\guides\\CREDENTIALS_SETUP.md"}, {"task": "Review ADVANCED_SECURITY.md for removal", "description": "File has excessive_content (80 headers, 103 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\technical\\ADVANCED_SECURITY.md"}, {"task": "Review DOCKER_SETUP.md for removal", "description": "File has excessive_content (59 headers, 34 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\technical\\DOCKER_SETUP.md"}, {"task": "Review MANUAL_OPERATIONS.md for removal", "description": "File has excessive_content (96 headers, 69 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\technical\\MANUAL_OPERATIONS.md"}, {"task": "Review TROUBLESHOOTING.md for removal", "description": "File has excessive_content (65 headers, 47 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\Documentation\\technical\\TROUBLESHOOTING.md"}, {"task": "Review ZROK_SETUP_GUIDE.md for removal", "description": "File has excessive_content (51 headers, 29 bullets)", "priority": "low", "estimated_time": "10 minutes", "type": "cleanup", "file_path": "n8n-docker\\legacy-tunneling\\ZROK_SETUP_GUIDE.md"}], "file_categories": {"setup_guides": [{"file": "GETTING_STARTED.md", "path": "GETTING_STARTED.md", "headers": 58, "bullets": 30}, {"file": "LIGHTNING_START.md", "path": "LIGHTNING_START.md", "headers": 9, "bullets": 9}, {"file": "GITHUB_SETUP_INSTRUCTIONS.md", "path": "Documentation\\GITHUB_SETUP_INSTRUCTIONS.md", "headers": 23, "bullets": 23}, {"file": "SERVER_STARTUP_METHODS.md", "path": "Documentation\\SERVER_STARTUP_METHODS.md", "headers": 44, "bullets": 34}, {"file": "INTEGRATION_SETUP.md", "path": "Documentation\\guides\\INTEGRATION_SETUP.md", "headers": 37, "bullets": 53}, {"file": "PYTHON_ENVIRONMENT_SETUP.md", "path": "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md", "headers": 54, "bullets": 21}, {"file": "GETTING_STARTED.md", "path": "n8n-docker\\GETTING_STARTED.md", "headers": 32, "bullets": 16}, {"file": "LIGHTNING_START.md", "path": "n8n-docker\\LIGHTNING_START.md", "headers": 11, "bullets": 10}, {"file": "QUICK_START.md", "path": "n8n-docker\\Documentation\\QUICK_START.md", "headers": 58, "bullets": 48}, {"file": "AUTOMATION_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\AUTOMATION_SETUP.md", "headers": 84, "bullets": 24}, {"file": "CREDENTIALS_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\CREDENTIALS_SETUP.md", "headers": 57, "bullets": 144}, {"file": "SECURITY_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\SECURITY_SETUP.md", "headers": 47, "bullets": 35}, {"file": "DOCKER_SETUP.md", "path": "n8n-docker\\Documentation\\technical\\DOCKER_SETUP.md", "headers": 59, "bullets": 34}, {"file": "ZROK_SETUP_GUIDE.md", "path": "n8n-docker\\legacy-tunneling\\ZROK_SETUP_GUIDE.md", "headers": 51, "bullets": 29}], "technical_specs": [{"file": "ARCHITECTURE.md", "path": "Documentation\\ARCHITECTURE.md", "headers": 24, "bullets": 43}, {"file": "ARCHITECTURE.md", "path": "Self_Healer\\ARCHITECTURE.md", "headers": 46, "bullets": 131}, {"file": "ARCHITECTURE.md", "path": "Self_Healer\\Documentation\\ARCHITECTURE.md", "headers": 53, "bullets": 134}], "troubleshooting": [{"file": "TROUBLESHOOTING.md", "path": "Documentation\\TROUBLESHOOTING.md", "headers": 47, "bullets": 47}, {"file": "TROUBLESHOOTING.md", "path": "n8n-docker\\Documentation\\technical\\TROUBLESHOOTING.md", "headers": 65, "bullets": 47}], "api_docs": [{"file": "API_DOCUMENTATION.md", "path": "Documentation\\api\\API_DOCUMENTATION.md", "headers": 71, "bullets": 80}, {"file": "API_QUICK_REFERENCE.md", "path": "Documentation\\api\\API_QUICK_REFERENCE.md", "headers": 60, "bullets": 64}], "project_specific": [{"file": "safe_project_analysis_20250627_232355.md", "path": "data\\safe_project_analysis_20250627_232355.md", "headers": 2, "bullets": 3}, {"file": "safe_project_analysis_20250628_014002.md", "path": "data\\safe_project_analysis_20250628_014002.md", "headers": 2, "bullets": 3}, {"file": "README.md", "path": "projects\\README.md", "headers": 10, "bullets": 10}, {"file": "README.md", "path": "projects\\elthosdb1\\README.md", "headers": 7, "bullets": 6}, {"file": "README.md", "path": "projects\\test-1\\README.md", "headers": 7, "bullets": 6}, {"file": "README.md", "path": "projects\\test-project\\README.md", "headers": 7, "bullets": 6}], "obsolete_candidates": [{"file": "GETTING_STARTED.md", "path": "GETTING_STARTED.md", "reason": "excessive_content", "headers": 58, "bullets": 30}, {"file": "separation_detection.md", "path": "separation_detection.md", "reason": "excessive_content", "headers": 138, "bullets": 333553}, {"file": "GITHUB_ORGANIZATION_TASKS.md", "path": "Documentation\\GITHUB_ORGANIZATION_TASKS.md", "reason": "excessive_content", "headers": 28, "bullets": 128}, {"file": "API_DOCUMENTATION.md", "path": "Documentation\\api\\API_DOCUMENTATION.md", "reason": "excessive_content", "headers": 71, "bullets": 80}, {"file": "API_QUICK_REFERENCE.md", "path": "Documentation\\api\\API_QUICK_REFERENCE.md", "reason": "excessive_content", "headers": 60, "bullets": 64}, {"file": "DOCUMENTATION.md", "path": "Documentation\\technical\\DOCUMENTATION.md", "reason": "excessive_content", "headers": 105, "bullets": 142}, {"file": "PYTHON_ENVIRONMENT_SETUP.md", "path": "Documentation\\technical\\PYTHON_ENVIRONMENT_SETUP.md", "reason": "excessive_content", "headers": 54, "bullets": 21}, {"file": "ProcessFlow.md", "path": "Documentation\\technical\\ProcessFlow.md", "reason": "excessive_content", "headers": 132, "bullets": 4275}, {"file": "ARCHITECTURE.md", "path": "Self_Healer\\ARCHITECTURE.md", "reason": "excessive_content", "headers": 46, "bullets": 131}, {"file": "README.md", "path": "Self_Healer\\README.md", "reason": "excessive_content", "headers": 60, "bullets": 106}, {"file": "ARCHITECTURE.md", "path": "Self_Healer\\Documentation\\ARCHITECTURE.md", "reason": "excessive_content", "headers": 53, "bullets": 134}, {"file": "INTEGRATION_GUIDE.md", "path": "Self_Healer\\Documentation\\INTEGRATION_GUIDE.md", "reason": "excessive_content", "headers": 96, "bullets": 33}, {"file": "SQLConventions.md", "path": "Self_Healer\\Documentation\\SQLConventions.md", "reason": "excessive_content", "headers": 67, "bullets": 124}, {"file": "documentation_analysis_report.md", "path": "data\\documentation_analysis_report.md", "reason": "excessive_content", "headers": 114, "bullets": 3819}, {"file": "QUICK_START.md", "path": "n8n-docker\\Documentation\\QUICK_START.md", "reason": "excessive_content", "headers": 58, "bullets": 48}, {"file": "README_OLD.md", "path": "n8n-docker\\Documentation\\README_OLD.md", "reason": "excessive_content", "headers": 76, "bullets": 50}, {"file": "AUTOMATION_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\AUTOMATION_SETUP.md", "reason": "excessive_content", "headers": 84, "bullets": 24}, {"file": "CREDENTIALS_SETUP.md", "path": "n8n-docker\\Documentation\\guides\\CREDENTIALS_SETUP.md", "reason": "excessive_content", "headers": 57, "bullets": 144}, {"file": "ADVANCED_SECURITY.md", "path": "n8n-docker\\Documentation\\technical\\ADVANCED_SECURITY.md", "reason": "excessive_content", "headers": 80, "bullets": 103}, {"file": "DOCKER_SETUP.md", "path": "n8n-docker\\Documentation\\technical\\DOCKER_SETUP.md", "reason": "excessive_content", "headers": 59, "bullets": 34}, {"file": "MANUAL_OPERATIONS.md", "path": "n8n-docker\\Documentation\\technical\\MANUAL_OPERATIONS.md", "reason": "excessive_content", "headers": 96, "bullets": 69}, {"file": "TROUBLESHOOTING.md", "path": "n8n-docker\\Documentation\\technical\\TROUBLESHOOTING.md", "reason": "excessive_content", "headers": 65, "bullets": 47}, {"file": "ZROK_SETUP_GUIDE.md", "path": "n8n-docker\\legacy-tunneling\\ZROK_SETUP_GUIDE.md", "reason": "excessive_content", "headers": 51, "bullets": 29}], "duplicates": []}, "summary": {"total_redundancy_issues": 58, "high_priority_issues": 5, "consolidation_groups": 4, "total_action_items": 32, "estimated_total_time": 755}}